<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Services\RequestService;
use App\Enums\NotificationType;
use App\Services\UserService;
use App\Enums\RequestStatus;
use App\Enums\Role;
use Lang;

class SendUserRequestCreatedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $request;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        if ($this->request->isStatus(RequestStatus::NEW)) {
            $patient = $this->request->requestable;

            $users = app(UserService::class)->users([Role::RECEPTIONIST, Role::FOLLOW_UP]);
            $bodyAr = Lang::get('messages.requests.statuses.NEW', ['name' => $patient->name], 'ar');
            $bodyEn = Lang::get('messages.requests.statuses.NEW', ['name' => $patient->name], 'en');

            $requestsStatistics = (Array) app(RequestService::class)->statistics();
            $notificationData = $this->notificationData(
                $this->request->id,
                NotificationType::REQUEST,
                $bodyAr,
                $bodyEn,
                array('requests_statistics' => $requestsStatistics)
            );
            SendNotificationJob::dispatch($users, $notificationData);
        }
    }
}
