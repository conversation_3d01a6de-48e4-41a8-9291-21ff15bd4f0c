<?php

namespace App\Http\Resources\PatientLabTests;

use App\Http\Resources\Shipments\ShipmentResource;
use App\Http\Resources\TestResult\TestResultResource;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\OnlinePaymentResource;
use App\Enums\SamplingReservationType;
use App\Enums\PaymentMethod;
use App\Enums\LabTestStatus;
use App\Enums\RequestStatus;
use App\Enums\PaymentStatus;
use App\Enums\RequestStage;
use App\Traits\UserTrait;
use App\Models\Payment;
use App\Traits\PaymentTrait;
use Lang;

class PatientLabTestListResource extends JsonResource
{
    use UserTrait, PaymentTrait;
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'lab_test_name' => $this->name?? $this->labTest->name,
            'status' => $this->statusValue(),
            'status_name' => LabTestStatus::getDescription($this->statusValue()),
            'request_status' => $this->request->status(),
            'request_status_name' => RequestStatus::getDescription($this->request->status()),
            'request_stage' => $this->request->stage,
            'request_stage_name' => RequestStage::getDescription($this->request->stage),
            'is_approved' => $this->isApproved(),
            'payment_status' => $this->payment->status?? PaymentStatus::NOT_PAID,
            'payment_status_name' => PaymentStatus::getDescription($this->payment->status?? PaymentStatus::NOT_PAID),
            'can_pay' => $this->checkCanPay(),
            'receive_result_after' => $this->sample_date? $this->receive_result_after : 0,
            'payment' => $this->payment? $this->paymentData() : null,
            'samplingReservation' => $this->samplingReservation && !$this->isStatus(LabTestStatus::SAMPLE_NOT_DEPOSITED)?  $this->samplingReservationData() : null,
            'force_deposited' => $this->force_deposited,
            'shipments' => $this->shipments_count? $this->shipmentsData() : null,
            'reference_lab_name' => $this->labTest->referenceLab->name?? $this->labTest->referenceLab->translate('en')->name,
            'patient_city_id' => $this->patient->city_id,
            'patient_region_id' => $this->patient->city->region_id,
            'test_result' => $this->testResultCategories->isNotEmpty() ? new TestResultResource($this->buildTestResultData()) : null,
        );
    }

    public function checkCanPay()
    {
        $user = $this->systemUser();
        return Payment::canTakeAction($user->role_id) && $this->payment && $this->payment->canPay();
    }

    public function paymentData()
    {
        return array(
            'id' => $this->payment->id,
            'status' => $this->payment->status,
            'payment_method' => $this->payment->payment_method,
            'payment_method_name' => PaymentMethod::getDescription($this->payment->payment_method),
            'paymentable' => $this->payment->paymentable?
                $this->getPaymentableResource($this->payment->payment_method, $this->payment->paymentable) : null,
        );
    }

    public function samplingReservationData()
    {
        return array(
            'id' => $this->samplingReservation->id,
            'agency_id' => $this->samplingReservation->agency_id,
            'agency_name' => $this->samplingReservation->agency->name,
            'city_id' => $this->samplingReservation->agency->city_id,
            'city_name' => Lang::get($this->samplingReservation->agency->city->name),
            'region_id' => $this->samplingReservation->agency->city->region_id,
            'status' => $this->samplingReservation->status,
            'type' => $this->samplingReservation->type,
            'type_name' => SamplingReservationType::getDescription($this->samplingReservation->type),
            'date' => $this->samplingReservation->date_converted,
            'notes' => $this->notes,
        );
    }

    public function shipmentsData()
    {
        return ShipmentResource::collection($this->shipments);
    }

    private function buildTestResultData()
    {
        return (object) [
            'id' => null,
            'patient_lab_test_id' => $this->id,
            'general_introduction' => $this->general_introduction,
            'general_conclusion' => $this->general_conclusion,
            'categories' => $this->testResultCategories,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
