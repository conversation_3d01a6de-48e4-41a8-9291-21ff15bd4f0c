<?php

namespace App\Http\Requests\Results;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\ResultStatus;
use Lang;
use Auth;

class ChangeResultStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('changeStatus', [Result::class, $this->patient_lab_test]);  
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => 'required|in:'.ResultStatus::NOT_RECEIVED.','. ResultStatus::RECEIVED,
            'notes' => 'nullable',
        ];
    }

    public function messages()
    {
        return [
            'status.required' => Lang::get('validation.custom.results.status.required'),
            'status.in' => Lang::get(
                'validation.custom.results.status.in',
                ['in' => ResultStatus::NOT_RECEIVED.','. ResultStatus::RECEIVED]
            ),
        ];
    }
}
