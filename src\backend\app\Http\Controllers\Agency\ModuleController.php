<?php

namespace App\Http\Controllers\Agency;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\ModuleService;
use App\Http\Resources\ModuleResource;

class ModuleController extends Controller
{
    private $moduleService;

    public function __construct(ModuleService $moduleService)
    {
        $this->moduleService = $moduleService;
    }

    public function sideMenu()
    {
        return ModuleResource::collection($this->moduleService->sideMenu());
    }
}
