<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\PatientLabTestService;
use App\Http\Resources\PatientLabTests\PatientLabTestDashboardStatisticsResource;
use App\Http\Resources\PatientLabTests\SamplingReservationFilterCollection;
use App\Http\Requests\PatientLabTests\StoreAndSendPatientLabTestRequest;
use App\Http\Resources\PatientLabTests\PatientLabTestFilterCollection;
use App\Http\Resources\PatientLabTests\PatientLabTestCollection;
use App\Http\Resources\PatientLabTests\PatientLabTestListResource;
use App\Http\Requests\PatientLabTests\StorePatientLabTestRequest;
use App\Http\Requests\PatientLabTests\UpdatePatientLabTestRequest;
use App\Http\Requests\Shipments\ChangeShipmentStatusRequest;
use App\Http\Requests\Results\ChangeResultStatusRequest;
use App\Http\Filters\PatientLabTestFilter;
use App\Services\ShipmentService;
use App\Services\ResultService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Patient;
use App\Models\PatientLabTest;
use Lang;

class PatientLabTestController extends Controller
{
    private $patientLabTestService;

    public function __construct(PatientLabTestService $patientLabTestService)
    {
        $this->patientLabTestService = $patientLabTestService;
    }

    public function list(Patient $patient)
    {
        $result = $this->patientLabTestService->list($patient);
        return PatientLabTestListResource::collection($result);
    }

    public function index(PatientLabTestFilter $filter, Patient $patient)
    {
        $result = $this->patientLabTestService->filter($filter, $patient);
        return new PatientLabTestCollection($result);
    }

    public function filterList(PatientLabTestFilter $filter)
    {
        $this->authorize('filterList', [PatientLabTest::class]);
        $result = $this->patientLabTestService->filter($filter, null, array());
        return new PatientLabTestFilterCollection($result);
    }

    public function store(StorePatientLabTestRequest $request, Patient $patient)
    {
        $this->patientLabTestService->create($request->validated(), $patient);
        return response()->json(
            ['message' => Lang::get('messages.patient_lab_tests.success.created')], Response::HTTP_OK
        );
    }

    public function createAndSend(StoreAndSendPatientLabTestRequest $request, Patient $patient)
    {
        $this->patientLabTestService->createAndSend($request->validated(), $patient);
        return response()->json(
            ['message' => Lang::get('messages.patient_lab_tests.success.create_send')], Response::HTTP_OK
        );
    }

    public function update(UpdatePatientLabTestRequest $request, Patient $patient, PatientLabTest $patientLabTest)
    {
        $this->patientLabTestService->update($request->validated(), $patientLabTest);
        return response()->json(
            ['message' => Lang::get('messages.patient_lab_tests.success.updated'), 'patient_id' => $patient->id],
            Response::HTTP_OK
        );
    }

    public function destroy(Patient $patient, PatientLabTest $patientLabTest)
    {
        $this->authorize('delete', $patientLabTest);
        $this->patientLabTestService->delete($patientLabTest);
        return response()->json(
            ['message' => Lang::get('messages.patient_lab_tests.success.deleted'), 'patient_id' => $patient->id],
            Response::HTTP_OK
        );
    }

    public function send(Patient $patient)
    {
        $this->authorize('send', [PatientLabTest::class, $patient]);
        $this->patientLabTestService->send($patient);
        return response()->json(['message' => Lang::get('messages.patient_lab_tests.success.send')], Response::HTTP_OK);
    }

    public function forceDeposited(PatientLabTest $patientLabTest)
    {
        $this->authorize('forceDeposited', [PatientLabTest::class, $patientLabTest]);
        $this->patientLabTestService->forceDeposited($patientLabTest);
        return response()->json(
            ['message' => Lang::get('messages.sampling-reservations.success.created')], Response::HTTP_OK
        );
    }

    public function changeShipmentStatus(ChangeShipmentStatusRequest $request, PatientLabTest $patientLabTest)
    {
        app(ShipmentService::class)->changeShipmentStatus($request->validated(), $patientLabTest);
        return response()->json(['message' => Lang::get('messages.shipments.success.status')], Response::HTTP_OK);
    }

    public function dashboardStatistics(Request $request)
    {
        $this->authorize('dashboardStatistics', PatientLabTest::class);
        $statistics = $this->patientLabTestService->dashboardStatistics($request->all());
        return response()->json(
            ['statistics' => new PatientLabTestDashboardStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function samplingReservationsFilter(PatientLabTestFilter $filter)
    {
        $this->authorize('samplingReservationsFilter', PatientLabTest::class);
        $result = $this->patientLabTestService->samplingReservationsFilter($filter);
        return new SamplingReservationFilterCollection($result);
    }

    public function changeResultStatus(ChangeResultStatusRequest $request, PatientLabTest $patientLabTest)
    {
        app(ResultService::class)->changeStatus($request->validated(), $patientLabTest);
        return response()->json(['message' => Lang::get('messages.results.success.status')], Response::HTTP_OK);
    }
}
