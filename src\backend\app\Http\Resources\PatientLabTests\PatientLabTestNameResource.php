<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\LabTestStatus;
use App\Enums\RequestStatus;
use App\Enums\PaymentStatus;
use App\Enums\RequestStage;
use Lang;

class PatientLabTestNameResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'lab_test_name' => $this->name?? $this->labTest->name, 
            'created_at' => $this->created_at_converted
        );
    }
}
