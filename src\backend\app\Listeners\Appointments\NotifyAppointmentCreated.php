<?php

namespace App\Listeners\Appointments;

use App\Jobs\Appointments\MissedAppointmentReminderJob;
use App\Jobs\SendPatientAppointmentNotificationJob;
use App\Jobs\SendUserAppointmentNotificationJob;
use App\Events\Appointments\AppointmentCreated;
use App\Jobs\GenerateAppointmentShortLinkJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Jobs\MissedAppointmentJob;
use App\Traits\MailTrait;

class NotifyAppointmentCreated
{
    use MailTrait;
    
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Appointments\AppointmentCreated $event
     * @return void
     */
    public function handle(AppointmentCreated $event)
    {
        $event->appointment->isVideo()?
            GenerateAppointmentShortLinkJob::dispatch($event->appointment->onlineMeeting)
            ->onQueue(config('queue.queues.notifications'))
            : null;
        SendUserAppointmentNotificationJob::dispatch($event->appointment, 'create')
            ->onQueue(config('queue.queues.notifications'));
        SendPatientAppointmentNotificationJob::dispatch($event->appointment, 'create')
            ->onQueue(config('queue.queues.notifications'));
        MissedAppointmentReminderJob::dispatch($event->appointment)
            ->delay($event->appointment->start_date->addMinutes(10))
            ->onQueue(config('queue.queues.notifications'));
        MissedAppointmentJob::dispatch($event->appointment)->delay($event->appointment->end_date)
            ->onQueue(config('queue.queues.requests'));
        $this->sendAppointmentMail($event->appointment, 'create');
    }
}
