<?php

namespace App\Http\Requests\Notes;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class StoreNoteRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'note' => 'required|max:2500',
            'users' => 'nullable|array',
            'users.*' => 'required|exists:users,id,deleted_at,NULL|distinct',
        ];
    }

    public function messages()
    {
        return [
            'note.required' => Lang::get('validation.custom.notes.note.required'),
            'note.max' => Lang::get('validation.custom.notes.note.max', ['max' => 2500]),
            'users.array' => Lang::get('validation.custom.notes.users.array'),
            'users.*.exists' => Lang::get('validation.custom.notes.users.exists'),
            'users.*.distinct' => Lang::get('validation.custom.notes.users.distinct'),
        ];
    }
}
