<?php

namespace App\Events\SamplingReservations;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SamplingReservationStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $samplingReservation;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($samplingReservation)
    {
        $this->samplingReservation = $samplingReservation;
    }
}
