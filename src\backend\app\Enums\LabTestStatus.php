<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static NEW()
 * @method static static SENT_TO_PATIENT()
 */
final class LabTestStatus extends Enum implements LocalizedEnum
{
    const NEW =                       1;
    const SENT_TO_PATIENT =           2;
    const APPROVED =                  3;
    const PAID =                      4;
    const CANCELED =                  5;
    const SAMPLING_RESERVATION =      6;
    const SAMPLE_DEPOSITED =          7;
    const SAMPLE_NOT_DEPOSITED =      8;
    const SAMPLE_SHIPPED =            9;
    const SAMPLE_NOT_SHIPPED =        10;
    const REFUNDED =                  11;    
    const RESULT_RECEIVED =           12;
    const RESULT_NOT_RECEIVED =       13;
    const COMPLETED =                 15;    
    //TODO add status for un completed lab test after complete request
}
