<?php

namespace App\Http\Requests\Users;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class UpdateProfileImageRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'profile_image' => 'required|mimes:jpeg,jpg,png|max:'.config('app.profile_max_size'),
        ];
    }

    public function messages()
    {
        return [
            'profile_image.required' => Lang::get('validation.custom.profile.profile_image.required'),
            'profile_image.mimes' => Lang::get(
                'validation.custom.profile.profile_image.mimes', ['values' => 'jpeg,jpg,png']
            ),
            'profile_image.max' => Lang::get(
                'validation.custom.profile.profile_image.max', ['max' => config('app.profile_max_size')]
            ),
        ];
    }
}