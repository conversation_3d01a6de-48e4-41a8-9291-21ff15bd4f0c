<?php

namespace App\Http\Requests\Users;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class ValidatePhoneNumberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA|
                unique:users,phone_number,NULL,id,deleted_at,NULL',        
        ];
    }

    public function messages()
    {
        return [
            'phone_number.required' => Lang::get('validation.custom.requests.phone_number.required'),
            'phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.invalid'),
            'phone_number.unique' => Lang::get('validation.custom.patient-register.phone_number.unique'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
        ];
    }
}