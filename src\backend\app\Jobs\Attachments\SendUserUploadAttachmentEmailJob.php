<?php

namespace App\Jobs\Attachments;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Enums\AppointmentReason;
use App\Enums\RequestStatus;
use App\Traits\MailTrait;
use App\Models\Patient;
use App\Enums\Role;
use Lang;

class SendUserUploadAttachmentEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $patient, $creatable;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patient, $creatable)
    {
        $this->patient = $patient;
        $this->creatable = $creatable;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $appointment = $this->getAppointment();
        if ($this->isWaitReportsReason($appointment) && ($this->isPatient() || $this->isCoordinator())) {
            $key = $this->isPatient()? 'upload_attachment' : 'upload_attachment_user';
            $this->sendUploadAttachmentMail($this->patient, $appointment->user, $key);
        }
    }

    public function getAppointment()
    {
        $request = $this->patient->lastRequest;
        $obligationNotSent = $request? $request->isStatus(RequestStatus::OBLIGATION_NOT_SENT) : null;
        return $obligationNotSent? $request->lastAppointment : null;
    }

    public function isWaitReportsReason($appointment)
    {
        $appointmentReasonId = $appointment->appointment_reason_id?? null;
        return $appointmentReasonId == AppointmentReason::WAIT_REPORTS;
    }

    public function isPatient()
    {
        return get_class($this->creatable) == Patient::class;
    }

    public function isCoordinator()
    {
        return $this->creatable->role_id == Role::FOLLOW_UP;
    }
}
