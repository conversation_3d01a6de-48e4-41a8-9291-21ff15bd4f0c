<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Services\UserService;
use App\Enums\LabTestStatus;
use App\Enums\PaymentStatus;
use App\Enums\Role;
use Lang;

class SendLabTestNotPaidNotificationToUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        NotificationTrait;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        $payment = $this->patientLabTest->payment;
        if ($this->patientLabTest->isStatus(LabTestStatus::APPROVED) 
            && $payment->isStatus(PaymentStatus::NOT_PAID)
        ) {
            $patient = $this->patientLabTest->patient;
            $nameAr= $patient->translate('ar')->name;
            $nameEn= $patient->translate('en')->name;
            $bodyAr = Lang::get('translations.mails.body.unpaid_reminder', ['name' => $nameAr], 'ar');
            $bodyEn = Lang::get('translations.mails.body.unpaid_reminder', ['name' => $nameEn], 'en');
            $users = app(UserService::class)->users([Role::FOLLOW_UP, Role::ACCOUNTANT]);
            $notificationData = $this->notificationData(
                $patient->id, NotificationType::PATIENT, $bodyAr, $bodyEn, array()
            );
            SendNotificationJob::dispatch($users, $notificationData);
        }
    }
}
