<?php

namespace App\Http\Requests\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Auth;
use Lang;

class StoreRequestByUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('createByUser', [Request::class, $this->patient]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'message_text' => 'required|max:1000',
        ];
    }

    public function messages()
    {
        return [
            'message_text.required' => Lang::get('validation.custom.requests.message_text.required'),
            'message_text.max' => Lang::get('validation.custom.requests.message_text.max'),
        ];
    }
}
