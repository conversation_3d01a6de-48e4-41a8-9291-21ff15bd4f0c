<?php

namespace App\Http\Requests\Appointments;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\AppointmentStatus;
use App\Rules\CheckAppointmentReasonType;
use App\Services\TimeZoneConverter;
use App\Enums\AppointmentReason;
use Carbon\Carbon;
use Lang;
use Auth;

class UpdateAppointmentReasonRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('updateReason', [Appointment::class, $this->appointment]); 
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $date = TimeZoneConverter::convertFromUtc(Carbon::now());
        $this->startOfDate = Carbon::parse($date)->format('Y-m-d');
        
        return [
            'appointment_reason_id' => ['nullable','required_without:reason',
                'exists:appointment_reasons,id,deleted_at,NULL',
                new CheckAppointmentReasonType($this->appointment->status)],
            'completion_due_date' => 'nullable|required_if:appointment_reason_id,'.AppointmentReason::WAIT_REPORTS.
                '|date|date_format:Y-m-d|after_or_equal:'.$this->startOfDate,
            'reason' => 'nullable|required_without:appointment_reason_id|
                unique:appointment_reasons,reason,NULL,id,deleted_at,NULL',
            'notes' => 'nullable',
        ];
    }

    public function messages()
    {
        return [
            'status.required' => Lang::get('validation.custom.appointments.status.required'),
            'status.in' => Lang::get(
                'validation.custom.appointments.status.in',
                ['in' => AppointmentStatus::CONFIRMED.','. AppointmentStatus::CANCELED]
            ),
            'appointment_reason_id.required_without' => Lang::get(
                'validation.custom.appointments.appointment_reason_id.required_without'
            ),
            'appointment_reason_id.exists' => Lang::get('validation.custom.appointments.appointment_reason_id.exists'),
            'reason.required_without' => Lang::get('validation.custom.appointments.reason.required_without'),
            'reason.regex' => Lang::get('validation.custom.appointments.reason.regex'),
            'reason.unique' => Lang::get('validation.custom.appointments.reason.unique'),
            'reason.max' => Lang::get('validation.custom.appointments.reason.max', ['max' => 150]),
            'completion_due_date.required_if' => Lang::get(
                'validation.custom.appointments.completion_due_date.required_if'
            ),
            'completion_due_date.date' => Lang::get('validation.custom.appointments.completion_due_date.date'),
            'completion_due_date.date_format' => Lang::get(
                'validation.custom.appointments.completion_due_date.date_format', ['format' => 'Y-m-d']
            ),
            'completion_due_date.after_or_equal' => Lang::get(
                'validation.custom.appointments.completion_due_date.after', ['date' => $this->startOfDate]
            ),
        ];
    }
}
