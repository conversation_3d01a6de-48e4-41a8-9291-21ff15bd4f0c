<?php

namespace App\Http\Controllers\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\Schedules\UpdateRequest;
use App\Http\Controllers\Controller;
use App\Services\ScheduleService;
use App\Models\User;
use App\Models\Schedule;
use Lang;

class ScheduleController extends Controller
{
    private $scheduleService;

    public function __construct(ScheduleService $scheduleService)
    {
        $this->scheduleService = $scheduleService;
    }

    public function update(UpdateRequest $request, User $user)
    {
        $this->authorize('update', [Schedule::class, $user]);
        $this->scheduleService->update($request->validated(), $user);
        return response()->json(['message' => Lang::get('messages.schedules.success.updated')], Response::HTTP_OK);
    }
}
