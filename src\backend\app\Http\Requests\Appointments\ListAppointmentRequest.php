<?php

namespace App\Http\Requests\Appointments;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class ListAppointmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'user_id' => 'required|exists:users,id,deleted_at,NULL',
            'start_date' => 'required|date|date_format:Y-m-d',
            'end_date' => 'required|date|date_format:Y-m-d|after_or_equal:start_date',
        ];
    }

    public function messages()
    {
        return [
            'user_id.required' => Lang::get('validation.custom.appointments.user_id.required'),
            'user_id.exists' => Lang::get('validation.custom.appointments.user_id.exists'),
            'start_date.required' => Lang::get('validation.custom.appointments.date.required'),
            'start_date.date' => Lang::get('validation.custom.appointments.date.date'),
            'start_date.date_format' => Lang::get(
                'validation.custom.appointments.date.date_format', ['format' => 'Y-m-d']
            ),
            'end_date.required' => Lang::get('validation.custom.appointments.end_date.required'),
            'end_date.date' => Lang::get('validation.custom.appointments.end_date.date'),
            'end_date.date_format' => Lang::get(
                'validation.custom.appointments.end_date.date_format', ['format' => 'Y-m-d']
            ),
            'end_date.after_or_equal' => Lang::get('validation.custom.appointments.end_date.after_or_equal'),
        ];
    }
}
