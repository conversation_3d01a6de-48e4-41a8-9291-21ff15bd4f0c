<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\AppointmentReasonType;
use App\Enums\AppointmentStatus;
use Lang;

class AppointmentReasonStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'name' => Lang::get($this->reason),
            'count' => $this->appointments_count,
            'status' => $this->type == AppointmentReasonType::ON_HOLD? AppointmentStatus::CONFIRMED : AppointmentStatus::CANCELED,
            'appointment_reason_id' => $this->id,
        );
    }
}
