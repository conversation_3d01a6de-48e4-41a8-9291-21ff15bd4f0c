<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\RequestSource;
use App\Enums\RequestStatus;
use App\Enums\RequestStage;
use App\Models\Request;
use Lang;

class CustomRequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $historyWithReason = $this->requestHistories->first(
            function ($history) {
                return in_array($history->request_status, array(RequestStatus::REJECTED, RequestStatus::CANCELED));
            }
        );
        $reason = $historyWithReason? ($historyWithReason->rejectionReason?? $historyWithReason->cancellationReason) : null;
        RequestHistoryResource::patientName($this->requestable->name);
        
        return array(
            'id' => $this->id,
            'message_text' => $this->message_text,
            'patient_name' => $this->requestable->name,
            'patient_phone' => $this->requestable->phone_number,
            'patient_image' => $this->requestable->profile_image,
            'request_source' => Lang::get('translations.request_sources.' .RequestSource::getKey($this->request_source)),
            'request_status' => RequestStatus::getDescription($this->status()),
            'created_at' => $this->created_at_converted,
            'reject_reason_title' => $this->isStatus(RequestStatus::REJECTED)? Lang::get($reason->title?? '') : null,
            'reject_reason_message' => $this->isStatus(RequestStatus::REJECTED)? Lang::get($reason->message?? '', ['name' => $this->requestable->name, 'app_url' => config('app.patient_app_url')]) : null,
            'cancel_reason_title' => $this->isStatus(RequestStatus::CANCELED)? Lang::get($reason->title?? '') : null,
            'cancel_reason_message' => $this->isStatus(RequestStatus::CANCELED)? ($historyWithReason->cancellation_note?? null) : null,
            'stage' => $this->stage,
            'stage_name' => RequestStage::getDescription($this->stage),
            'can_cancel' => in_array($this->request_status, Request::canCancelStatuses()),
            'history' => RequestHistoryResource::collection($this->requestHistories),
        );
    }
}
