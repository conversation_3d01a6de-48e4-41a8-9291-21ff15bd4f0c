<?php

namespace App\Http\Controllers\Patient;

use App\Http\Requests\Patients\UpdateCurrentPatientRequest;
use App\Http\Requests\Patients\DeleteCurrentPatientRequest;
use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\PatientService;
use App\Http\Resources\PatientResource;
use Lang;

class PatientController extends Controller
{
    private $patientService;

    public function __construct(PatientService $patientService)
    {
        $this->patientService = $patientService;
    }

    public function patient()
    {
        return new PatientResource($this->patientService->patient());
    }

    public function lastUpdatedNotification()
    {
        return $this->patientService->lastRequest();
    }

    public function update(UpdateCurrentPatientRequest $request)
    {
        $this->patientService->updateByPatient($request->validated());
        return response()->json(['message' => Lang::get('messages.patients.success.updated')], Response::HTTP_OK);
    }

    public function destroyMe(DeleteCurrentPatientRequest $request)
    {
        $this->patientService->delete($request->validated());
        return response()->json(['message' => Lang::get('messages.patients.success.deleted')], Response::HTTP_OK);
    }
    public function destroyMeConfirm(Request $request)
    {
        return view('patient.destroy-confirm');

    }
    public function destroyMeConfirmPost(Request $request)
    {
        return response()->json(['message' => Lang::get('messages.patients.success.deleted')], Response::HTTP_OK);

    }
}
