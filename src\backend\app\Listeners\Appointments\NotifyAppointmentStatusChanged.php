<?php

namespace App\Listeners\Appointments;

use App\Jobs\Appointments\SendUserAppointmentStatusNotificationJob;
use App\Jobs\Appointments\AppointmentStatusChangedAutoTaskJob;
use App\Jobs\Appointments\WaitReferenceLabAutoTaskJob;
use App\Jobs\SendAppointmentStatusChangedToPatientJob;
use App\Events\Appointments\AppointmentStatusChanged;
use App\Jobs\Appointments\WaitReportsAutoTaskJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\TimeZoneConverter;
use App\Enums\AppointmentReason;
use App\Enums\AppointmentStatus;
use Carbon\Carbon;

class NotifyAppointmentStatusChanged
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Appointments\AppointmentStatusChanged $event
     * @return void
     */
    public function handle(AppointmentStatusChanged $event)
    {
        if ($this->isReasonChanged($event) || $this->isCompletionDueDateChanged($event)) {
            $this->autoTaskAndNotification($event);
            SendAppointmentStatusChangedToPatientJob::dispatch($event->appointment, $event->status, $event->reason->id)
                ->onQueue(config('queue.queues.notifications'));
        }
    }

    public function autoTaskAndNotification($event)
    {
        if ($event->reason->id == AppointmentReason::WAIT_REFERENCE_LAB) {
            WaitReferenceLabAutoTaskJob::dispatch($event->appointment, $event->user)
                ->onQueue(config('queue.queues.tasks'));
            return;
        } else if ($event->reason->id == AppointmentReason::WAIT_REPORTS) {
            WaitReportsAutoTaskJob::dispatch($event->appointment)
                ->onQueue(config('queue.queues.tasks'));
            return;
        }

        $patient = $event->appointment->patient;
        AppointmentStatusChangedAutoTaskJob::dispatch($patient, $event->status, $event->reason)
            ->onQueue(config('queue.queues.tasks'));
        SendUserAppointmentStatusNotificationJob::dispatch($patient, $event->status, $event->reason)
            ->onQueue(config('queue.queues.notifications'));
    }

    public function isReasonChanged($event)
    {
        return $event->appointment->appointment_reason_id != $event->reason->id;
    }

    public function isCompletionDueDateChanged($event)
    {
        $newCompletionDueDate = $event->completionDueDate;
        $oldCompletionDueDate = $event->appointment->completion_due_date;
        return $event->reason->id == AppointmentReason::WAIT_REPORTS &&
            !$oldCompletionDueDate->eq(Carbon::parse(TimeZoneConverter::convertToUtc($newCompletionDueDate)));
    }
}
