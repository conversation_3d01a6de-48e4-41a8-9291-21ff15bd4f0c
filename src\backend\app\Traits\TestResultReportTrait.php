<?php

namespace App\Traits;

use Illuminate\Support\Facades\File;
use PDF;
use Carbon\Carbon;

trait TestResultReportTrait
{
    /**
     * Get PDF data for test result report
     *
     * @param object $patientLabTest
     * @param string $filePath
     * @return array
     */
    public function getTestResultPdfData($patientLabTest, $filePath)
    {
        return [
            'patient' => [
                'name' => $patientLabTest->patient->translate('ar')->name ?? $patientLabTest->patient->translate('en')->name,
                'id' => $patientLabTest->patient->id,
                'age' => $patientLabTest->patient->age ?? 'N/A',
                'gender' => $patientLabTest->patient->gender ?? 'N/A',
            ],
            'lab_test' => [
                'name' => $patientLabTest->name ?? $patientLabTest->labTest->name,
                'code' => $patientLabTest->labTest->code ?? 'N/A',
            ],
            'general_introduction' => $patientLabTest->general_introduction,
            'general_conclusion' => $patientLabTest->general_conclusion,
            'categories' => $this->formatCategoriesForPdf($patientLabTest->testResultCategories),
            'date' => Carbon::now()->format('d/m/Y'),
            'report_id' => 'RPT-' . str_pad($patientLabTest->id, 6, '0', STR_PAD_LEFT),
        ];
    }

    /**
     * Format categories data for PDF
     *
     * @param \Illuminate\Database\Eloquent\Collection $categories
     * @return array
     */
    private function formatCategoriesForPdf($categories)
    {
        return $categories->map(function ($category) {
            return [
                'id' => $category->id,
                'introduction' => $category->introduction,
                'conclusion' => $category->Conclusion,
                'genes' => $category->genes->map(function ($gene) {
                    return [
                        'gene_name' => $gene->gene->name ?? 'N/A',
                        'inheritance_pattern' => $gene->transmissionMethod->name ?? 'N/A',
                        'disease' => $gene->illness->name ?? 'N/A',
                        'genetic_risk' => $gene->geneResult->name ?? 'N/A',
                        'mutation_site' => $gene->mutationSite->name ?? 'N/A',
                        'gene_category' => $gene->geneCategory->name ?? 'N/A',
                    ];
                })->toArray(),
            ];
        })->toArray();
    }

    /**
     * Create test result PDF report
     *
     * @param object $patientLabTest
     * @return string
     */
    public function createTestResultReportPdf($patientLabTest)
    {
        $name = 'test_result_report';
        $filename = time() . '_' . $patientLabTest->id . '_' . $name . '.pdf';
        $filePath = '/uploads/pdf/' . $filename;
        $path = public_path() . '/uploads/pdf/';
        $fullPath = $path . $filename;
        
        if (!is_dir($path)) {
            File::makeDirectory($path, 0777, true);
        }
        
        $data = $this->getTestResultPdfData($patientLabTest, $filePath);
        PDF::loadView('pdf.test-result-report', ['data' => $data])->save($fullPath);
        
        return $filePath;
    }
}
