<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Enums\SamplingReservationStatus;
use App\Models\PatientLabTest;
use App\Enums\RequestStatus;
use App\Enums\LabTestStatus;
use App\Traits\RequestTrait;
use App\Models\Patient;
use Carbon\Carbon;
use Auth;

class PatientLabTestFilter extends Filter
{
    use RequestTrait;

    public $activeCurrentRequest = -1;
    public $previousRequest =      -2;
    public $activeRequestStatus = -1;
    public $fields = array('status', 'created_at', 'updated_at');
    public $allPaidStatus = -1;
    public $allSamplingReservationStatus = 1;
    public $samplingNotBooked = 2;
    public $samplingNotBookedWithDelay = 3;
    public $allSampleStatus = -1;
    public $sampleDeposite = 1;
    public $notDepositeWithoutDelay = 2;
    public $notDepositeWithDelay = 3;

    /**
     * @param string $requestStatus
     */
    public function requestStatus(string $requestStatus)
    {
        $this->builder->when(
            $requestStatus == $this->activeRequestStatus, function ($query) {
                return $query->activeRequests();
            }
        )->when(
            $requestStatus > 0, function ($query) use ($requestStatus) {
                    return $query->requestStatus($requestStatus);
            }
        );
    }

    /**
     * @param string $startDate
     */
    public function startDate(string $startDate)
    {
        $this->builder->whereDate('created_at', '>=', $startDate);
    }

    /**
     * @param string $endDate
     */
    public function endDate(string $endDate)
    {
        $this->builder->whereDate('created_at', '<=', $endDate);
    }
    
    /**
     * @param string $requestId
     */
    public function requestId(string $requestId)
    {
        $this->builder->where('request_id', $requestId);
    }

    /**
     * @param string $labTestId
     */
    public function labTestId(string $labTestId)
    {
        $this->builder->where('lab_test_id', $labTestId);
    }

    /**
     * @param string $status
     */
    public function status(string $status)
    {
        $request = get_class(Auth::user()) == Patient::class? $this->getActiveRequest(Auth::user()) : null;
        $this->builder->when(
            $status == $this->activeCurrentRequest, function ($query) use ($request) {
                return $query->requestId($request->id?? null)->notStatus(LabTestStatus::NEW);
            }
        )->when(
            $status == $this->previousRequest, function ($query) use ($request) {
                return $query->notRequestId($request->id?? null)->status(LabTestStatus::COMPLETED);
            }
        )->when(
            $status == PatientLabTest::$approved, function ($query) {
                return $query->whereIn('status', PatientLabTest::$approvedStatuses);
            }
        )->when(
            $status == PatientLabTest::$notApproved, function ($query) {
                return $query->notApproved();
            }
        )->when(
            $status == PatientLabTest::$notApprovedAndDelay, function ($query) {
                return $query->notApprovedWithDelay();
            }
        )->when(
            $status > 0, function ($query) use ($status) {
                    return $query->status($status);
            }
        );
    }

    /**
     * @param string $samplingReservationStatus
     */
    public function samplingReservationStatus(string $status)
    {
        $this->builder->when(
            $status == $this->allPaidStatus, function ($query) {
                return $query->statuses(PatientLabTest::$paidStatuses);
            }
        )->when(
            $status == $this->allSamplingReservationStatus, function ($query) {
                return $query->statuses(PatientLabTest::$samplingReservationStatuses);
            }
        )->when(
            $status == $this->samplingNotBooked, function ($query) {
                return $query->status(LabTestStatus::PAID)
                    ->where('updated_at', '>=', Carbon::now()->subHours(24));
            }
        )->when(
            $status == $this->samplingNotBookedWithDelay, function ($query) {
                return $query->status(LabTestStatus::PAID)
                    ->where('updated_at', '<', Carbon::now()->subHours(24));
            }
        );
    }

    /**
     * @param string $sampleStatus
     */
    public function sampleStatus(string $status)
    {
        $this->builder->when(
            $status == $this->allSampleStatus, function ($query) {
                return $query->statuses(PatientLabTest::$samplingReservationStatuses);
            }
        )->when(
            $status == $this->sampleDeposite, function ($query) {
                return $query->statuses(PatientLabTest::$sampleDepositeStatus);
            }
        )->when(
            $status == $this->notDepositeWithoutDelay, function ($query) {
                return $this->sampleNotDepositeStatusWithoutDelay($query);
            }
        )->when(
            $status == $this->notDepositeWithDelay, function ($query) {
                return $this->sampleNotDepositeStatusWithDelay($query);
            }
        );
    }

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->whereHas(
            'patient', function (Builder $query) use ($search) {
                $query->where(
                    function ($query) use ($search) {
                        $query->where('phone_number', 'like', '%'.$search.'%')
                            ->orWhere('identification_number', 'like', '%'.$search.'%')
                            ->orWhereHas(
                                'patientTranslations', function (Builder $query) use ($search) {
                                    $query->where('name', 'like', '%'.$search.'%');
                                }
                            );
                    }
                );
            }
        );
    }
    
    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }

    public function sampleNotDepositeStatusWithoutDelay($query)
    {
        return $query->status(LabTestStatus::SAMPLING_RESERVATION)
            ->whereHas(
                'samplingReservation', function ($query) {
                    $query->where('date', '>=', Carbon::now()->subHours(24));
                }
            );
    }

    public function sampleNotDepositeStatusWithDelay($query)
    {
        return $query->statuses([LabTestStatus::SAMPLING_RESERVATION, LabTestStatus::SAMPLE_NOT_DEPOSITED,])
            ->whereHas(
                'samplingReservation', function ($query) {
                    $query->where(
                        function ($query) {
                            $query->where('status', SamplingReservationStatus::NEW)
                                ->where('date', '<', Carbon::now()->subHours(24));
                        }
                    )->orWhere('status', SamplingReservationStatus::NOT_DEPOSITED);
                }
            );
    }
}