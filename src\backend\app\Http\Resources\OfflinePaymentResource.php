<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Lang;

class OfflinePaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'name' => $this->name,
            'url' => $this->url,
            'creatable_type' => $this->creatable_type,
            'creatable_id' => $this->creatable_id,
            'created_at'  => $this->created_at_converted,
            'offline_payment_method_id' => $this->offline_payment_method_id,
            'offline_payment_method_name' => Lang::get($this->offlinePaymentMethod->name),
        );
    }
}
