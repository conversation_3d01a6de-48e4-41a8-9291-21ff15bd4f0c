<?php

namespace App\Http\Requests\LabTests;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\CheckShippingLocation;
use App\Enums\ShippingLocation;
use App\Enums\ShippingCompany;
use App\Enums\TubeType;
use App\Enums\ObligationType;
use App\Enums\Day;
use Auth;
use Lang;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('create', LabTest::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'code' => 'required|regex:/^[a-zA-Z0-9-]*$/|unique:lab_tests,code,NULL,id,deleted_at,NULL',
            'name.ar' => 'required',
            'name.en' => 'required',
            'sample_type.ar' => 'required|regex:/^(?!.*\d)[\p{Arabic}\s]+$/iu',
            'sample_type.en' => 'required|regex:/^(?!.*\d)[a-zِِA-Z\s]+$/iu',
            'sample_quantity' => 'required',
            'tube_type' => 'required|in:'.implode(',', TubeType::getValues()),
            'tubes_count' => 'required|integer',
            'price' => 'required|numeric|min:0',
            'price_after_discount' => 'required|numeric|lte:price',
            'reference_lab_id' => 'required|exists:reference_labs,id,deleted_at,NULL',
            'obligation_type' => 'required|in:'.implode(',', ObligationType::getValues()),
            'result_due_days' => 'required|numeric|min:0',
            'lab_test_shipping_locations' => ['required', 'array', new CheckShippingLocation],
            'lab_test_shipping_locations.*.from' => 'required|distinct|in:'.implode(
                ',',
                array(ShippingLocation::AGENCY_LOCATION, ShippingLocation::GENOME_LOCATION)
            ),
            'lab_test_shipping_locations.*.to' => 'required|distinct|in:'.implode(
                ',',
                array(ShippingLocation::GENOME_LOCATION, ShippingLocation::REFERENCE_LAB_LOCATION)
            ),
            'lab_test_shipping_locations.*.shipping_company' => 'required|in:'.
                implode(',', ShippingCompany::getValues()),
            'working_days' => 'required|array',
            'working_days.*.day' => 'required|distinct|in:'.implode(',', Day::getValues()),
        ];
    }

    public function messages()
    {
        return [
            'code.required' => Lang::get('validation.custom.lab_tests.code.required'),
            'code.unique' => Lang::get('validation.custom.lab_tests.code.unique'),
            'code.regex' => Lang::get('validation.custom.lab_tests.code.regex'),
            'name.ar.required' => Lang::get('validation.custom.lab_tests.name.ar.required'),
            'name.en.required' => Lang::get('validation.custom.lab_tests.name.en.required'),
            'sample_type.ar.required' => Lang::get('validation.custom.lab_tests.sample_type.ar.required'),
            'sample_type.ar.regex' => Lang::get('validation.custom.lab_tests.sample_type.ar.regex'),
            'sample_type.en.required' => Lang::get('validation.custom.lab_tests.sample_type.en.required'),
            'sample_type.en.regex' => Lang::get('validation.custom.lab_tests.sample_type.en.regex'),
            'sample_quantity.required' => Lang::get('validation.custom.lab_tests.sample_quantity.required'),
            'tube_type.required' => Lang::get('validation.custom.lab_tests.tube_type.required'),
            'tube_type.in' => Lang::get(
                'validation.custom.lab_tests.tube_type.in', ['in' => implode(',', TubeType::getValues())]
            ),
            'tubes_count.required' => Lang::get('validation.custom.lab_tests.tubes_count.required'),
            'tubes_count.integer' => Lang::get('validation.custom.lab_tests.tubes_count.integer'),
            'price.required' => Lang::get('validation.custom.lab_tests.price.required'),
            'price.min' => Lang::get('validation.custom.lab_tests.price.min'),
            'price.numeric' => Lang::get('validation.custom.lab_tests.price.numeric'),
            'price_after_discount.required' => Lang::get('validation.custom.lab_tests.price_after_discount.required'),
            'price_after_discount.numeric' => Lang::get('validation.custom.lab_tests.price_after_discount.numeric'),
            'price_after_discount.lte' => Lang::get(
                'validation.custom.lab_tests.price_after_discount.lte', ['value' => $this->price]
            ),
            'reference_lab_id.required' => Lang::get('validation.custom.lab_tests.reference_lab_id.required'),
            'reference_lab_id.exists' => Lang::get('validation.custom.lab_tests.reference_lab_id.exists'),
            'obligation_type.required' => Lang::get('validation.custom.lab_tests.obligation.required'),
            'obligation_type.in' => Lang::get(
                'validation.custom.lab_tests.obligation.in', ['in' => implode(',', ObligationType::getValues())]
            ),
            'result_due_days.required' => Lang::get('validation.custom.lab_tests.result_due_days.required'),
            'result_due_days.min' => Lang::get('validation.custom.lab_tests.result_due_days.min'),
            'result_due_days.numeric' => Lang::get('validation.custom.lab_tests.result_due_days.numeric'),
            'lab_test_shipping_locations.*.shipping_company.required' => Lang::get(
                'validation.custom.lab_tests.shipping_company.required'
            ),
            'lab_test_shipping_locations.*.shipping_company.in' => Lang::get(
                'validation.custom.lab_tests.shipping_company.in', ['in' => implode(',', ShippingCompany::getValues())]
            ),
            'lab_test_shipping_locations.required' => Lang::get(
                'validation.custom.lab_tests.lab_test_shipping_locations.required'
            ),
            'lab_test_shipping_locations.array' => Lang::get(
                'validation.custom.lab_tests.lab_test_shipping_locations.array'
            ),
            'lab_test_shipping_locations.*.from.required' => Lang::get('validation.custom.lab_tests.from.required'),
            'lab_test_shipping_locations.*.from.distinct' => Lang::get('validation.custom.lab_tests.from.distinct'),
            'lab_test_shipping_locations.*.from.in' => Lang::get(
                'validation.custom.lab_tests.from.in',
                ['in' => ShippingLocation::AGENCY_LOCATION.','.ShippingLocation::GENOME_LOCATION]
            ),
            'lab_test_shipping_locations.*.to.required' => Lang::get('validation.custom.lab_tests.to.required'),
            'lab_test_shipping_locations.*.to.distinct' => Lang::get('validation.custom.lab_tests.to.distinct'),
            'lab_test_shipping_locations.*.to.in' => Lang::get(
                'validation.custom.lab_tests.to.in',
                ['in' => ShippingLocation::GENOME_LOCATION.','.ShippingLocation::REFERENCE_LAB_LOCATION]
            ),
            'working_days.required' => Lang::get('validation.custom.lab_tests.working_days.required'),
            'working_days.array' => Lang::get('validation.custom.lab_tests.working_days.array'),
            'working_days.*.day.required' => Lang::get('validation.custom.lab_tests.day.required'),
            'working_days.*.day.distinct' => Lang::get('validation.custom.lab_tests.day.distinct'),
            'working_days.*.day.in' => Lang::get(
                'validation.custom.lab_tests.day.in', ['in' => implode(',', Day::getValues())]
            ),
        ];
    }
}
