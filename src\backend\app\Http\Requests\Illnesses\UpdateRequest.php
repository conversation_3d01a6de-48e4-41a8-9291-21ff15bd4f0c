<?php

namespace App\Http\Requests\Illnesses;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Illness;
use Auth;
use Lang;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('update', Illness::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name.ar' => 'required',
            'name.en' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'name.ar.required' => Lang::get('validation.custom.genes.name.ar.required'),
            'name.en.required' => Lang::get('validation.custom.genes.name.en.required'),
        ];
    }
}
