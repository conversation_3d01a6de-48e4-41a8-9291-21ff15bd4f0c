[2025-05-26 10:46:50] local.ERROR: Property [id] does not exist on this collection instance. {"userId":1,"exception":"[object] (Exception(code: 0): Property [id] does not exist on this collection instance. at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Traits\\EnumeratesValues.php:969)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Support\\Collection->__get('id')
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(18): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('id')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#4 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(37): Illuminate\\Routing\\ResponseFactory->json(Array, 200, Array, 0)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#69 {main}
"} 
[2025-05-26 11:19:54] local.ERROR: Call to undefined method App\Http\Resources\PatientLabTests\PatientLabTestListResource::TestResult() {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource::TestResult() at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:71)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Http\\Resources\\Json\\JsonResource::throwBadMethodCallException('TestResult')
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(152): Illuminate\\Http\\Resources\\Json\\JsonResource->forwardCallTo(Object(App\\Models\\PatientLabTest), 'TestResult', Array)
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource.php(98): Illuminate\\Http\\Resources\\Json\\JsonResource->__call('TestResult', Array)
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource.php(52): App\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource->testResultCategoriesData()
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(60): App\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource->toArray(Object(Illuminate\\Http\\Request))
#5 [internal function]: Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource), 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(695): array_map(Object(Closure), Array, Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(59): Illuminate\\Support\\Collection->map(Object(Closure))
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(102): Illuminate\\Support\\HigherOrderCollectionProxy->__call('toArray', Array)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toArray(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(39): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#70 {main}
"} 
[2025-05-26 11:20:26] local.ERROR: Call to undefined method App\Http\Resources\PatientLabTests\PatientLabTestListResource::TestResult() {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource::TestResult() at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:71)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Http\\Resources\\Json\\JsonResource::throwBadMethodCallException('TestResult')
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(152): Illuminate\\Http\\Resources\\Json\\JsonResource->forwardCallTo(Object(App\\Models\\PatientLabTest), 'TestResult', Array)
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource.php(98): Illuminate\\Http\\Resources\\Json\\JsonResource->__call('TestResult', Array)
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource.php(52): App\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource->testResultCategoriesData()
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(60): App\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource->toArray(Object(Illuminate\\Http\\Request))
#5 [internal function]: Illuminate\\Support\\HigherOrderCollectionProxy->Illuminate\\Support\\{closure}(Object(App\\Http\\Resources\\PatientLabTests\\PatientLabTestListResource), 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(695): array_map(Object(Closure), Array, Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\HigherOrderCollectionProxy.php(59): Illuminate\\Support\\Collection->map(Object(Closure))
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(102): Illuminate\\Support\\HigherOrderCollectionProxy->__call('toArray', Array)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toArray(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(39): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#70 {main}
"} 
[2025-05-26 12:12:13] local.ERROR: Attempt to read property "content" on string {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"content\" on string at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php:56)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(56): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\laragon\\\\www\\\\...', 56)
#1 [internal function]: App\\Http\\Resources\\TestResult\\TestResultResource->App\\Http\\Resources\\TestResult\\{closure}(Object(App\\Models\\TestResultCategory), 0)
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(695): array_map(Object(Closure), Array, Array)
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php(350): Illuminate\\Support\\Collection->map(Object(Closure))
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(23): Illuminate\\Database\\Eloquent\\Collection->map(Object(Closure))
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(184): App\\Http\\Resources\\TestResult\\TestResultResource->App\\Http\\Resources\\TestResult\\{closure}()
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\ConditionallyLoadsAttributes.php(105): value(Object(Closure))
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(22): Illuminate\\Http\\Resources\\Json\\JsonResource->when(true, Object(Closure))
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#10 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(37): Illuminate\\Routing\\ResponseFactory->json(Array, 200, Array, 0)
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#75 {main}
"} 
[2025-05-26 12:12:47] local.ERROR: Attempt to read property "content" on string {"userId":1,"exception":"[object] (ErrorException(code: 0): Attempt to read property \"content\" on string at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php:56)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(56): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Attempt to read...', 'C:\\\\laragon\\\\www\\\\...', 56)
#1 [internal function]: App\\Http\\Resources\\TestResult\\TestResultResource->App\\Http\\Resources\\TestResult\\{closure}(Object(App\\Models\\TestResultCategory), 0)
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(695): array_map(Object(Closure), Array, Array)
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php(350): Illuminate\\Support\\Collection->map(Object(Closure))
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(23): Illuminate\\Database\\Eloquent\\Collection->map(Object(Closure))
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(184): App\\Http\\Resources\\TestResult\\TestResultResource->App\\Http\\Resources\\TestResult\\{closure}()
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\ConditionallyLoadsAttributes.php(105): value(Object(Closure))
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(22): Illuminate\\Http\\Resources\\Json\\JsonResource->when(true, Object(Closure))
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#10 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(37): Illuminate\\Routing\\ResponseFactory->json(Array, 200, Array, 0)
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#72 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#73 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#74 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#75 {main}
"} 
[2025-05-26 13:12:27] local.ERROR: Undefined property: stdClass::$created_at {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$created_at at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(60): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('created_at')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#4 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Controllers\\Dashboard\\TestResultController.php(44): Illuminate\\Routing\\ResponseFactory->json(Array, 200)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TestResultController->show('17')
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('show', Array)
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(31): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->Sentry\\Laravel\\Tracing\\Routing\\{closure}()
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#72 {main}
"} 
[2025-05-26 13:20:11] local.ERROR: Undefined property: stdClass::$created_at {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$created_at at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(60): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('created_at')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#4 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Controllers\\Dashboard\\TestResultController.php(44): Illuminate\\Routing\\ResponseFactory->json(Array, 200)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TestResultController->show('17')
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('show', Array)
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(31): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->Sentry\\Laravel\\Tracing\\Routing\\{closure}()
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#72 {main}
"} 
[2025-05-26 13:22:00] local.ERROR: Undefined property: stdClass::$created_at {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$created_at at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(60): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('created_at')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#4 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Controllers\\Dashboard\\TestResultController.php(43): Illuminate\\Routing\\ResponseFactory->json(Array, 200)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TestResultController->show('17')
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('show', Array)
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(31): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->Sentry\\Laravel\\Tracing\\Routing\\{closure}()
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#72 {main}
"} 
[2025-05-26 13:22:52] local.ERROR: Undefined property: stdClass::$created_at {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$created_at at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(17): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('created_at')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#4 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Controllers\\Dashboard\\TestResultController.php(43): Illuminate\\Routing\\ResponseFactory->json(Array, 200)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TestResultController->show('17')
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('show', Array)
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(31): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->Sentry\\Laravel\\Tracing\\Routing\\{closure}()
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#72 {main}
"} 
[2025-05-26 13:23:35] local.ERROR: Undefined property: stdClass::$created_at {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$created_at at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(17): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('created_at')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#4 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Controllers\\Dashboard\\TestResultController.php(43): Illuminate\\Routing\\ResponseFactory->json(Array, 200)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TestResultController->show('17')
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('show', Array)
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(31): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->Sentry\\Laravel\\Tracing\\Routing\\{closure}()
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#71 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#72 {main}
"} 
[2025-05-26 13:24:47] local.ERROR: Too few arguments to function App\Http\Resources\TestResult\TestResultResource::toArray(), 0 passed in C:\laragon\www\saudigenome-Backend\src\backend\app\Http\Resources\TestResult\TestResultResource.php on line 17 and exactly 1 expected {"userId":1,"exception":"[object] (ArgumentCountError(code: 0): Too few arguments to function App\\Http\\Resources\\TestResult\\TestResultResource::toArray(), 0 passed in C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php on line 17 and exactly 1 expected at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php:15)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(17): App\\Http\\Resources\\TestResult\\TestResultResource->toArray()
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#3 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Controllers\\Dashboard\\TestResultController.php(43): Illuminate\\Routing\\ResponseFactory->json(Array, 200)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TestResultController->show('17')
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('show', Array)
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(31): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->Sentry\\Laravel\\Tracing\\Routing\\{closure}()
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#71 {main}
"} 
[2025-05-26 13:25:01] local.ERROR: Cannot use object of type stdClass as array {"userId":1,"exception":"[object] (Error(code: 0): Cannot use object of type stdClass as array at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:82)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(17): Illuminate\\Http\\Resources\\Json\\JsonResource->offsetGet(0)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#3 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Controllers\\Dashboard\\TestResultController.php(43): Illuminate\\Routing\\ResponseFactory->json(Array, 200)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\Dashboard\\TestResultController->show('17')
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(45): Illuminate\\Routing\\Controller->callAction('show', Array)
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(21): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher.php(31): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->Sentry\\Laravel\\Tracing\\Routing\\{closure}()
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing.php(20): Sentry\\Laravel\\Tracing\\Routing\\TracingRoutingDispatcher->wrapRouteDispatch(Object(Closure), Object(Illuminate\\Routing\\Route))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(261): Sentry\\Laravel\\Tracing\\Routing\\TracingControllerDispatcherTracing->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\Dashboard\\TestResultController), 'show')
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(721): Illuminate\\Routing\\Route->run()
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#71 {main}
"} 
[2025-05-26 18:34:53] local.ERROR: Undefined property: stdClass::$result_pdf {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$result_pdf at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(57): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('result_pdf')
#2 [internal function]: App\\Http\\Resources\\TestResult\\TestResultResource->App\\Http\\Resources\\TestResult\\{closure}(Object(App\\Models\\TestResultCategory), 0)
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\Collection.php(695): array_map(Object(Closure), Array, Array)
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Collection.php(350): Illuminate\\Support\\Collection->map(Object(Closure))
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(23): Illuminate\\Database\\Eloquent\\Collection->map(Object(Closure))
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Collections\\helpers.php(184): App\\Http\\Resources\\TestResult\\TestResultResource->App\\Http\\Resources\\TestResult\\{closure}()
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\ConditionallyLoadsAttributes.php(105): value(Object(Closure))
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(22): Illuminate\\Http\\Resources\\Json\\JsonResource->when(true, Object(Closure))
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#11 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(37): Illuminate\\Routing\\ResponseFactory->json(Array, 200, Array, 0)
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#70 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#71 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#72 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#73 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#74 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#75 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#76 {main}
"} 
[2025-05-26 18:35:15] local.ERROR: Undefined property: stdClass::$result_pdf {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$result_pdf at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(60): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('result_pdf')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#4 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(37): Illuminate\\Routing\\ResponseFactory->json(Array, 200, Array, 0)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#69 {main}
"} 
[2025-05-26 18:36:31] local.ERROR: Undefined property: stdClass::$result_pdf {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$result_pdf at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(22): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('result_pdf')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#4 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#5 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(37): Illuminate\\Routing\\ResponseFactory->json(Array, 200, Array, 0)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#69 {main}
"} 
[2025-05-26 18:38:21] local.ERROR: Call to undefined method App\Http\Resources\TestResult\TestResultResource::generatePdfUrl() {"userId":1,"exception":"[object] (BadMethodCallException(code: 0): Call to undefined method App\\Http\\Resources\\TestResult\\TestResultResource::generatePdfUrl() at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php:71)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\Traits\\ForwardsCalls.php(36): Illuminate\\Http\\Resources\\Json\\JsonResource::throwBadMethodCallException('generatePdfUrl')
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(152): Illuminate\\Http\\Resources\\Json\\JsonResource->forwardCallTo(Object(stdClass), 'generatePdfUrl', Array)
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(22): Illuminate\\Http\\Resources\\Json\\JsonResource->__call('generatePdfUrl', Array)
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#5 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(37): Illuminate\\Routing\\ResponseFactory->json(Array, 200, Array, 0)
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#70 {main}
"} 
[2025-05-26 18:40:17] local.ERROR: Undefined property: stdClass::$result_pdf {"userId":1,"exception":"[object] (ErrorException(code: 0): Undefined property: stdClass::$result_pdf at C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php:140)
[stacktrace]
#0 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\DelegatesToResource.php(140): Illuminate\\Foundation\\Bootstrap\\HandleExceptions->handleError(2, 'Undefined prope...', 'C:\\\\laragon\\\\www\\\\...', 140)
#1 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(77): Illuminate\\Http\\Resources\\Json\\JsonResource->__get('result_pdf')
#2 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Resources\\TestResult\\TestResultResource.php(22): App\\Http\\Resources\\TestResult\\TestResultResource->generatePdfUrl()
#3 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(94): App\\Http\\Resources\\TestResult\\TestResultResource->toArray(Object(Illuminate\\Http\\Request))
#4 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(242): Illuminate\\Http\\Resources\\Json\\JsonResource->resolve(Object(Illuminate\\Http\\Request))
#5 [internal function]: Illuminate\\Http\\Resources\\Json\\JsonResource->jsonSerialize()
#6 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(87): json_encode(Array, 0)
#7 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\symfony\\http-foundation\\JsonResponse.php(54): Illuminate\\Http\\JsonResponse->setData(Array)
#8 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\JsonResponse.php(32): Symfony\\Component\\HttpFoundation\\JsonResponse->__construct(Array, 200, Array, false)
#9 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ResponseFactory.php(99): Illuminate\\Http\\JsonResponse->__construct(Array, 200, Array, 0)
#10 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php(37): Illuminate\\Routing\\ResponseFactory->json(Array, 200, Array, 0)
#11 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php(231): Illuminate\\Http\\Resources\\Json\\ResourceResponse->toResponse(Object(Illuminate\\Http\\Request))
#12 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceCollection.php(117): Illuminate\\Http\\Resources\\Json\\JsonResource->toResponse(Object(Illuminate\\Http\\Request))
#13 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(802): Illuminate\\Http\\Resources\\Json\\ResourceCollection->toResponse(Object(Illuminate\\Http\\Request))
#14 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(789): Illuminate\\Routing\\Router::toResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#15 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(720): Illuminate\\Routing\\Router->prepareResponse(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\Resources\\Json\\AnonymousResourceCollection))
#16 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#17 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\UpdateUserTimezone.php(34): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\UpdateUserTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#19 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\CheckBlockUser.php(35): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#20 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\CheckBlockUser->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#21 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeDefaultGuard.php(22): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#22 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeDefaultGuard->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#23 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#24 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#25 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(127): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#26 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(93): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequest(Object(Illuminate\\Http\\Request), Object(Closure), Array)
#27 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\ThrottleRequests.php(55): Illuminate\\Routing\\Middleware\\ThrottleRequests->handleRequestUsingNamedLimiter(Object(Illuminate\\Http\\Request), Object(Closure), 'api', Object(Closure))
#28 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Routing\\Middleware\\ThrottleRequests->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'api')
#29 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(44): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'user')
#31 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(33): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->Laravel\\Sanctum\\Http\\Middleware\\{closure}(Object(Illuminate\\Http\\Request))
#33 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\sanctum\\src\\Http\\Middleware\\EnsureFrontendRequestsAreStateful.php(32): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#35 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Laravel\\Sanctum\\Http\\Middleware\\EnsureFrontendRequestsAreStateful->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(719): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#38 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(698): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#39 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(662): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#40 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(651): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#41 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(167): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#42 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(128): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#43 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestIpMiddleware.php(45): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestIpMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Http\\SetRequestMiddleware.php(31): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#46 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Http\\SetRequestMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#47 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\app\\Http\\Middleware\\ChangeLocalization.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#48 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): App\\Http\\Middleware\\ChangeLocalization->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#55 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#56 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#57 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#58 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#59 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\fruitcake\\laravel-cors\\src\\HandleCors.php(52): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#60 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Fruitcake\\Cors\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#61 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#62 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#63 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\sentry\\sentry-laravel\\src\\Sentry\\Laravel\\Tracing\\Middleware.php(87): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#64 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(167): Sentry\\Laravel\\Tracing\\Middleware->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#65 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(103): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#66 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(142): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#67 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(111): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#68 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\public\\index.php(51): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#69 C:\\laragon\\www\\saudigenome-Backend\\src\\backend\\server.php(21): require_once('C:\\\\laragon\\\\www\\\\...')
#70 {main}
"} 
