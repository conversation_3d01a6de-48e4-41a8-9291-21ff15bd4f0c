<?php

namespace App\Http\Requests\Introductions;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Gene;
use Auth;
use Lang;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('create', Gene::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
       return [
            'content' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'content.required' => Lang::get('validation.custom.introductions.content.required')
        ];
    }
}
