<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\ConclusionFilter;
use App\Http\Resources\ConclusionResource;
use App\Models\Conclusion;
use App\Http\Resources\ConclusionSearchResource;
use App\Http\Resources\CustomConclusionResource;
use App\Services\ConclusionService;
use App\Http\Requests\Conclusions\StoreRequest;
use App\Http\Requests\Conclusions\UpdateRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class ConclusionController extends Controller
{
    protected $conclusionService;

    public function __construct(ConclusionService $conclusionService)
    {
        $this->conclusionService = $conclusionService;
    }

    public function index(ConclusionFilter $filter)
    {
        $this->authorize('viewAny', Conclusion::class);
        $result = $this->conclusionService->filter($filter);
        return new ConclusionSearchResource($result);
    }

  

    public function show(Conclusion $conclusion)
    {
        $this->authorize('view', Conclusion::class);
        return new CustomConclusionResource($conclusion);
    }

    public function list()
    {
        $result = $this->conclusionService->list();
        return ConclusionResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->conclusionService->activeList();
        return ConclusionResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->conclusionService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.conclusions.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, Conclusion $conclusion)
    {
        $this->conclusionService->update($request->validated(), $conclusion);
        return response()->json(['message' => Lang::get('messages.conclusions.success.updated')], Response::HTTP_OK);
    }

    public function destroy(Conclusion $conclusion)
    {
        $this->authorize('delete', Conclusion::class);
        $this->conclusionService->delete($conclusion);
        return response()->json(['message' => Lang::get('messages.conclusions.success.deleted')], Response::HTTP_OK);
    }

    public function active(Conclusion $conclusion)
    {
        $this->authorize('active', Conclusion::class);
        $this->conclusionService->active($conclusion);
        return response()->json(['message' => Lang::get('messages.conclusions.success.actived')], Response::HTTP_OK);
    }

    public function deactive(Conclusion $conclusion)
    {
        $this->authorize('deactive', Conclusion::class);
        $this->conclusionService->deactive($conclusion);
        return response()->json(['message' => Lang::get('messages.conclusions.success.deactived')], Response::HTTP_OK);
    }

    // public function statistics()
    // {
    //     $this->authorize('statistics', Gene::class);
    //     $statistics = $this->geneService->statistics();
    //     return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    // }
}