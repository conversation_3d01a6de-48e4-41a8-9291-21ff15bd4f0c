<?php

namespace App\Enums;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * @method static static VISA()
 * @method static static APPLE_PAY()
 * @method static static MADA()
 * @method static static STC()
 */
final class OnlinePaymentMethod extends Enum implements LocalizedEnum
{
    const VISA =          1;
    const APPLE_PAY =     2;
    const MADA =          3;
    const STC =           4;
}