<?php

namespace App\Enums;

use BenSampo\Enum\Contracts\LocalizedEnum;
use BenSampo\Enum\Enum;

/**
 * @method static static AGENCY_LOCATION()
 * @method static static GENOME_LOCATION()
 * @method static static REFERENCE_LAB_LOCATION()
 */
final class ShippingLocation extends Enum implements LocalizedEnum
{
    const AGENCY_LOCATION =              1;
    const GENOME_LOCATION =              2;
    const REFERENCE_LAB_LOCATION =       3;
}
