<?php

namespace App\Http\Requests\Appointments;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\TimeZoneConverter;
use App\Enums\AppointmentType;
use App\Enums\AppointmentPurpose;
use Carbon\Carbon;
use Lang;

class ListAvailableAppointmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $date = TimeZoneConverter::convertFromUtc(Carbon::now());
        $startOfDate = Carbon::parse($date)->format('Y-m-d');
        return [
            'date' => 'required|date|date_format:Y-m-d|after_or_equal:'.$startOfDate,
        ];
    }

    public function messages()
    {
        return [
            'date.required' => Lang::get('validation.custom.appointments.date.required'),
            'date.date' => Lang::get('validation.custom.appointments.date.date'),
            'date.date_format' => Lang::get('validation.custom.appointments.date.date_format', ['format' => 'Y-m-d']),
            'date.after_or_equal' => Lang::get('validation.custom.appointments.date.after_or_equal'),
        ];
    }
}
