<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Connectors\FirebaseDynamicLinkConnector;
use App\Traits\MessageTrait;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Enums\MessageType;
use App\Enums\Role;
use Lang;

class SendSharingRecordUrlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MessageTrait, 
        NotificationTrait;

    protected $appointment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $message = $this->smsMessage();
        $patient = $this->appointment->patient;
        $appointmentId = $this->appointment->id;
        $requestStatus = $this->appointment->request->request_status;
        $notification = $this->notificationObject(NotificationType::APPOINTMENT, $appointmentId, null, $requestStatus);
        $this->createMessage($patient, $message, MessageType::BOTH, $notification);
    }

    public function smsMessage()
    {
        $user = $this->appointment->user;
        $date = $this->appointment->patient_date;
        $onlineMeeting = $this->appointment->onlineMeeting;
        $name = $user? $user->translate('ar')->name : Lang::get('translations.user');
        $position = Lang::get(
            'translations.roles.'.strtolower(Role::getKey($user->role_id?? Role::SPECIALIST)), [], 'ar'
        ); 
        $link = FirebaseDynamicLinkConnector::createShortLink($onlineMeeting->file_sharing_link);

        return Lang::get(
            'messages.messages.record_link',
            ['date' => $date, 'name' => $name, 'link' => $link, 'position' => $position],
            'ar'
        );
    }
}
