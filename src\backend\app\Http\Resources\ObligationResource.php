<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\ObligationType;
use App\Enums\LabTestStatus;

class ObligationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'lab_test_id' => $this->lab_test_id,
            'lab_test_name' => $this->labTest->name,
            'obligation_type' => $this->obligation_type,
            'obligation_type_name' => ObligationType::getDescription($this->obligation_type),
            'approval_date' => $this->approval_date_converted,
            'status' => $this->patientLabTest->status,
            'status_name' => LabTestStatus::getDescription($this->patientLabTest->status),
            'url' => $this->url,
        );
    }
}
