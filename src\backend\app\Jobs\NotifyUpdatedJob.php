<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;

class NotifyUpdatedJob
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    private $receivers, $notificationData;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($receivers, $notificationData)
    {
        $this->receivers = $receivers;
        $this->notificationData = $notificationData;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        foreach ($this->receivers as $receiver) {
            $this->notifyUpdated($receiver, $this->notificationData);
        }
    }
}
