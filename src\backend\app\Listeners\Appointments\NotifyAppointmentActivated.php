<?php

namespace App\Listeners\Appointments;

use App\Events\Appointments\AppointmentActivated;
use App\Jobs\ChangeAppointmentToConfirmedJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Jobs\GetAppointmentRecordUrlJob;

class NotifyAppointmentActivated
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Appointments\AppointmentActivated $event
     * @return void
     */
    public function handle(AppointmentActivated $event)
    {
        $appointment = $event->appointment;
        GetAppointmentRecordUrlJob::dispatch($appointment)->delay($appointment->end_date->addMinutes(10))
            ->onQueue(config('queue.queues.requests'));
        ChangeAppointmentToConfirmedJob::dispatch($appointment)->delay($appointment->end_date)
            ->onQueue(config('queue.queues.requests'));
    }
}
