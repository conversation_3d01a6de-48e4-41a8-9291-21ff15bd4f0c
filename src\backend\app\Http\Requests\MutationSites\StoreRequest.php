<?php

namespace App\Http\Requests\MutationSites;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\MutationSite;
use Auth;
use Lang;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('create', MutationSite::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|unique:mutation_sites,name',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => Lang::get('validation.custom.mutation_sites.name.required'),
            'name.unique' => Lang::get('validation.custom.mutation_sites.name.unique'),
        ];
    }
}
