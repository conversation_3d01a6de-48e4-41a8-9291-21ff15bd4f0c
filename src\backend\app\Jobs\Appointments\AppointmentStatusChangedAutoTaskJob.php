<?php

namespace App\Jobs\Appointments;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Enums\AppointmentReason;
use App\Enums\AppointmentStatus;
use App\Services\TaskService;
use App\Services\UserService;
use App\Enums\GenderType;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class AppointmentStatusChangedAutoTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $patient, $status,$reason;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patient, $status, $reason)
    {
        $this->patient = $patient;
        $this->status = $status;
        $this->reason = $reason;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $patientName = $this->patient->translate('ar')->name;
        $reasonKey = $this->getReasonKey();
        $users = app(UserService::class)->users([Role::FOLLOW_UP]);
        $gender = $this->patient->gender_type == GenderType::FEMALE? 'her' : 'him';
        $data = array(
            'description' => Lang::get(
                'messages.appointments.auto_tasks.'.$reasonKey,
                ['name' => $patientName, 'gender' => $gender, 'reason' => $this->reason->reason],
                'ar'
            ),
            'complete_date' => Carbon::now()->addDay(),
        );
        foreach ($users as $user) {
            app(TaskService::class)->createModel($data, $user);
        }
    }

    public function getReasonKey()
    {
        switch (true) {
        case in_array($this->reason->id, AppointmentReason::getValues()):
            return AppointmentReason::getKey((int) $this->reason->id);
            break;
        case $this->status == AppointmentStatus::CANCELED:
            return 'CANCELLATION_NEW_REASON';
            break;
        case $this->status == AppointmentStatus::CONFIRMED:
            return 'ON_HOLD_NEW_REASON';
            break;
        }
    }
}
