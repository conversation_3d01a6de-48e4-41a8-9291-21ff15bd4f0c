<?php

namespace App\Http\Requests\Auth\Agency;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class AgencyTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'username' => 'required|exists:agencies,username,deleted_at,NULL|max:50',
            'password' => 'required',
            'required_otp' => 'required|boolean',
            'otp_code' => 'required_if:required_otp,true|digits:4',
        ];
    }


    public function messages()
    {
        return [
            'username.required' => Lang::get('validation.custom.agencies.username.required'),
            'username.exists' => Lang::get('validation.custom.agencies.username.exists'),
            'username.max' => Lang::get('validation.custom.agencies.username.max', ['max' => 50]),
            'password.required'  => Lang::get('validation.custom.login.password.required'),
            'otp_code.required_if'  => Lang::get('validation.custom.login.otp_code.required'),
            'otp_code.digits'  => Lang::get('validation.custom.login.otp_code.digits'),
            'required_otp.required' => Lang::get('validation.custom.login.required_otp.required'),
            'required_otp.boolean' => Lang::get('validation.custom.login.required_otp.boolean'),
        ];
    }
}
