<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;

class ApprovedPatientLabTestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'request_status' => $this->request->request_status,
            'list' => PatientLabTestResource::collection($this->list),
        );
    }
}
