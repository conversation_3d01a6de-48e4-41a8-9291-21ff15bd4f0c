<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\AppointmentType;
use App\Enums\AppointmentPurpose;
use App\Enums\AppointmentStatus;
use App\Enums\RequestStatus;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class AppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $appointmentReasonId = in_array($this->status, array(AppointmentStatus::CONFIRMED, AppointmentStatus::CANCELED))?
            $this->appointment_reason_id  : null;
        $appointmentReason = $appointmentReasonId? $this->appointmentReason : null;

        return array(
            'id' => $this->id,
            'start_date' => $this->start_date_converted,
            'join_date' => $this->join_date_converted,
            'end_date' => $this->end_date_converted,
            'patient_id' => $this->patient_id,
            'patient_name' => $this->patient->name,
            'patient_image' => $this->patient->profile_image,
            'patient_phone_number' => $this->patient->phone_number,
            'patient_gender_type' => $this->patient->gender_type,
            'program_id' => $this->patient->program_id,
            'program_name' => $this->patient->program->name??  null,
            'type' => $this->type,
            'type_name' => AppointmentType::getDescription($this->type),
            'purpose' => $this->purpose,
            'purpose_name' => AppointmentPurpose::getDescription($this->purpose),
            'user_id' => $this->user_id,
            'user_name' => $this->user->name?? Lang::get('translations.user'),
            'role_id' => $this->user->role_id?? null,
            'role_name' => $this->user? Role::getDescription($this->user->role_id) : null,
            'status' => $this->status,
            'status_name' => $this->status == AppointmentStatus::CONFIRMED? $this->getStatus() : AppointmentStatus::getDescription($this->status),
            'request_status' => $this->request->request_status,
            'request_status_name' => RequestStatus::getDescription($this->request->request_status),
            'call_recording_url' => $this->onlineMeeting->call_recording_url?? null,
            'join_url'=> $this->onlineMeeting->join_url?? null,
            'file_sharing_link' =>  $this->onlineMeeting->file_sharing_link?? null,
            'message' => $this->statusMessage(),
            'appointment_reason_id' => $appointmentReasonId,
            'appointment_reason_name' => $appointmentReason? Lang::get($appointmentReason->reason): null
        );
    }

    public function statusMessage()
    {
        $name = $this->user? $this->user->translate('ar')->name : Lang::get('translations.user');
        $position = Role::getDescription($this->user->role_id?? Role::SPECIALIST);
        $purpose = $this->purposeName('ar');
        $type = $this->typeName('ar');

        return Lang::get(
            'messages.request_statuses.'. RequestStatus::getKey($this->request->request_status),
            ['name' => $name, 'purpose' => $purpose, 'type' => $type, 'position' => $position]
        );
    }

    public function getStatus()
    {
        $status = $this->completion_due_date && $this->completion_due_date->lte(Carbon::now())? 'confirmed_with_delay' : 'confirmed_without_delay';
        return Lang::get('translations.appointment_statuses.'.$status);
    }
}
