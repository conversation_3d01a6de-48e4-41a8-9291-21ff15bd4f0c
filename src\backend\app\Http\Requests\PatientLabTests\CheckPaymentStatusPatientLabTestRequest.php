<?php

namespace App\Http\Requests\PatientLabTests;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\OnlinePaymentMethod;
use App\Models\PatientLabTest;
use App\Enums\PaymentMethod;
use Lang;
use Auth;

class CheckPaymentStatusPatientLabTestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('paymentStatus', [PatientLabTest::class, $this->patient_lab_test]);   
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'checkout_id' => 'required|exists:online_payments,checkout_id,payment_id,'
                .$this->patient_lab_test->payment_id.',deleted_at,NULL',
        );
    }

    public function messages()
    {
        return array(
            'checkout_id.required' => Lang::get('validation.custom.payments.checkout_id.required'),
            'checkout_id.exists' => Lang::get('validation.custom.payments.checkout_id.exists'),
        );
    }
}
