<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Http\Requests\StoreFcmTokenRequest;
use App\Http\Requests\UpdateFcmTokenRequest;
use App\Services\FcmTokenService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lang;

class FcmTokenController extends Controller
{
    public function __invoke(StoreFcmTokenRequest $request)
    {
        app(FcmTokenService::class)->create($request->validated());
        return response()->json(['message' => Lang::get('messages.fcm-tokens.success.created')], Response::HTTP_OK);
    }
}
