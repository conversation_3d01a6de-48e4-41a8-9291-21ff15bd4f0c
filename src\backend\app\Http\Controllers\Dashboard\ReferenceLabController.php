<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Requests\ReferenceLabs\StoreRequest;
use App\Http\Requests\ReferenceLabs\UpdateRequest;
use App\Services\ReferenceLabService;
use App\Services\CountryService;
use App\Http\Resources\ReferenceLabResource;
use App\Http\Resources\ReferenceLabStatisticsResource;
use App\Http\Resources\ReferenceLabCollection;
use App\Http\Resources\CountryResource;
use App\Http\Filters\ReferenceLabFilter;
use App\Models\ReferenceLab;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lang;

class ReferenceLabController extends Controller
{
    private $referenceLabService;

    public function __construct(ReferenceLabService $referenceLabService)
    {
        $this->referenceLabService = $referenceLabService;
    }

    public function show(ReferenceLab $referenceLab)
    {
        $this->authorize('view', ReferenceLab::class);
        return new ReferenceLabResource($referenceLab);
    }

    public function index(ReferenceLabFilter $filter)
    {
        $this->authorize('viewAny', ReferenceLab::class);
        $result = $this->referenceLabService->filter($filter);
        return new ReferenceLabCollection($result);
    }

    public function list()
    {
        $result = $this->referenceLabService->list();
        return ReferenceLabResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->referenceLabService->activeList();
        return ReferenceLabResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->referenceLabService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.reference_labs.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, ReferenceLab $referenceLab)
    {
        $this->referenceLabService->update($request->validated(), $referenceLab);
        return response()->json(['message' => Lang::get('messages.reference_labs.success.updated')], Response::HTTP_OK);
    }

    public function statistics()
    {
        $this->authorize('statistics', ReferenceLab::class);
        $statistics = $this->referenceLabService->statistics();
        return response()->json(['statistics' => new ReferenceLabStatisticsResource($statistics)], Response::HTTP_OK);
    }

    public function countries()
    {
        $this->authorize('countries', ReferenceLab::class);
        $countries = app(CountryService::class)->referenceLabsCountries();
        return CountryResource::collection($countries);
    }

    public function active(ReferenceLab $referenceLab)
    {
        $this->authorize('active', ReferenceLab::class);
        $this->referenceLabService->active($referenceLab);
        return response()->json(['message' => Lang::get('messages.reference_labs.success.actived')], Response::HTTP_OK);
    }

    public function deactive(ReferenceLab $referenceLab)
    {
        $this->authorize('deactive', ReferenceLab::class);
        $this->referenceLabService->deactive($referenceLab);
        return response()->json(
            ['message' => Lang::get('messages.reference_labs.success.deactived')], Response::HTTP_OK
        );
    }

    public function destroy(ReferenceLab $referenceLab)
    {
        $this->authorize('delete', ReferenceLab::class);
        $this->referenceLabService->delete($referenceLab);
        return response()->json(['message' => Lang::get('messages.reference_labs.success.deleted')], Response::HTTP_OK);
    }
}
