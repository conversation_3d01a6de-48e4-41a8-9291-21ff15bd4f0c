<?php

namespace App\Http\Requests\Genes;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class MutaionSiteNameRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|min:3|max:150|unique:mutation_sites,name,NULL,id,deleted_at,NULL',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => Lang::get('validation.custom.families.name.required'),
            'name.min' => Lang::get('validation.custom.families.name.min', ['min' => 3]),
            'name.max' => Lang::get('validation.custom.families.name.max', ['max' => 150]),
            'name.unique' => Lang::get('validation.custom.families.name.unique'),
        ];
    }
}
