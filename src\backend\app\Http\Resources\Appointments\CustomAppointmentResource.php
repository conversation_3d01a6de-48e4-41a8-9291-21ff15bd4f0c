<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\AppointmentType;
use Lang;

class CustomAppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'startDate' => $this->start_date_converted,
            'endDate' => $this->end_date_converted,
            'title' => $this->patient->name,
            'type' => $this->type,
            'type_name' => AppointmentType::getDescription($this->type),
            'user_id' => $this->user_id,
            'user_name' => $this->user->name?? Lang::get('translations.user'),
        );
    }
}
