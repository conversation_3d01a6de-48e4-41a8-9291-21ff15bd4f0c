<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\OfflinePayment;
use App\Models\OnlinePayment;
use App\Models\Payment;

class PaymentFilter extends Filter
{
    public $activeRequestStatus = -1;
    public $fields = array('created_at', 'updated_at');

    /**
     * @param string $status
     */
    public function status(string $status)
    {
        $this->builder->when(
            $status == Payment::$notPaid, function ($query) {
                return $query->notPaid();
            }
        )->when(
            $status == Payment::$notPaidWithDelay, function ($query) {
                return $query->notPaidWithDelay();
            }
        )->when(
            $status > 0, function ($query) use ($status) {
                    return $query->status($status);
            }
        );
    }

    /**
     * @param string $requestStatus
     */
    public function requestStatus(string $requestStatus)
    {
        $this->builder->when(
            $requestStatus == $this->activeRequestStatus, function ($query) {
                return $query->activeRequests();
            }
        )->when(
            $requestStatus > 0, function ($query) use ($requestStatus) {
                    return $query->requestStatus($requestStatus);
            }
        );
    }

    /**
     * @param string $startDate
     */
    public function startDate(string $startDate)
    {
        $this->builder->whereDate('created_at', '>=', $startDate);
    }

    /**
     * @param string $endDate
     */
    public function endDate(string $endDate)
    {
        $this->builder->whereDate('created_at', '<=', $endDate);
    }

    /**
     * @param string $paymentMethod
     */
    public function paymentMethod($paymentMethod)
    {
        $this->builder->when(
            $paymentMethod < 0, function ($query) use ($paymentMethod) {
                return $query ->whereHasMorph(
                    'paymentable', [OnlinePayment::class], function (Builder $query) use ($paymentMethod) {
                        $query->where('online_payment_method', abs($paymentMethod));
                    }
                );
            }
        )->when(
            $paymentMethod > 0, function ($query) use ($paymentMethod) {
                return $query->whereHasMorph(
                    'paymentable', [OfflinePayment::class], function (Builder $query) use ($paymentMethod) {
                        $query->where('offline_payment_method_id', $paymentMethod);
                    }
                );
            }
        );
    }

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->whereHas(
            'patient', function (Builder $query) use ($search) {
                $query->where(
                    function ($query) use ($search) {
                        $query->where('phone_number', 'like', '%'.$search.'%')
                            ->orWhere('identification_number', 'like', '%'.$search.'%')
                            ->orWhereHas(
                                'patientTranslations', function (Builder $query) use ($search) {
                                    $query->where('name', 'like', '%'.$search.'%');
                                }
                            );
                    }
                );
            }
        );
    }

    /**
     * Sort the attachments by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}