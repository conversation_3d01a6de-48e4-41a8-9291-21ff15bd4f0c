<?php

namespace App\Jobs\Shipments;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Queueable;
use App\Services\UserService;
use App\Traits\MailTrait;
use App\Enums\Role;

class SendShipmentEmailToCoordinator<PERSON><PERSON> implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $patientLabTest;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        $users = app(UserService::class)->users([Role::FOLLOW_UP]);            
        foreach ($users as $user) {
            $this->sendShipmentMail($this->patientLabTest, $user);
        }
    }
}
