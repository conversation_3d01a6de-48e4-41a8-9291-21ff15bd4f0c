<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lang;

class CheckNotificationAccess
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        if (isset($request->notification)) {
            $user = $request->user();
            $userNotification = $user->notifications()->where('id', $request->notification->id)->first();
            if (!$userNotification) {
                abort(Response::HTTP_FORBIDDEN, Lang::get('messages.notifications.errors.authorization'));
            }
        }

        return $next($request);
    }
}
