<?php

namespace App\Http\Controllers\Dashboard\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\SystemUserAuthRequest;
use App\Http\Requests\SystemUserTokenRequest;
use App\Services\LoginService;
use Illuminate\Http\Response;
use Lang;

class LoginController extends Controller
{
    private $loginService;

    public function __construct(LoginService $loginService)
    {
        $this->loginService = $loginService;
    }

    public function sendOtpToSystemUser(SystemUserAuthRequest $request)
    {
        $expiredAt = $this->loginService->sendOtpToSystemUser($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.otp.success.send'), 'expired_at' => $expiredAt], Response::HTTP_OK
        );
    }

    public function systemUserToken(SystemUserTokenRequest $request)
    {
        return $this->loginService->systemUserToken($request->validated());
    }
}
