<?php

namespace App\Events\PatientLabTests;

use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Queue\SerializesModels;
use Illuminate\Broadcasting\Channel;

class PatientLabTestCreatedAndSent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $patientLabTest;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }
}
