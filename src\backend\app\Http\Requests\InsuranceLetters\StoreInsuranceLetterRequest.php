<?php

namespace App\Http\Requests\InsuranceLetters;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\CheckLabTestsRequest;
use Auth;
use Lang;

class StoreInsuranceLetterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $this->patient = Auth::user();
        return $this->patient && $this->patient->can('create', InsuranceLetter::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'is_current_request' => 'required|boolean',
            'patient_lab_tests' => ['required', 'array', new CheckLabTestsRequest($this->is_current_request)],
            'patient_lab_tests.*.patient_lab_test_id' => 'required|
                exists:patient_lab_tests,id,deleted_at,NULL,patient_id,'.$this->patient->id.'|distinct',
        ];
    }

    public function messages()
    {
        return [
            'is_current_request.required' => Lang::get(
                'validation.custom.insurance_letters.is_current_request.required'
            ),
            'is_current_request.boolean' => Lang::get('validation.custom.insurance_letters.is_current_request.boolean'),
            'patient_lab_tests.required' => Lang::get('validation.custom.insurance_letters.patient_lab_tests.required'),
            'patient_lab_tests.array' => Lang::get('validation.custom.insurance_letters.patient_lab_tests.array'),
            'patient_lab_tests.*.patient_lab_test_id.required' => Lang::get(
                'validation.custom.insurance_letters.patient_lab_test_id.required'
            ),
            'patient_lab_tests.*.patient_lab_test_id.exists' => Lang::get(
                'validation.custom.insurance_letters.patient_lab_test_id.exists'
            ),
            'patient_lab_tests.*.patient_lab_test_id.distinct' => Lang::get(
                'validation.custom.insurance_letters.patient_lab_test_id.distinct'
            ),
        ];
    }
}
