<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\Role;
use Lang;

class CustomPatientLabTestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $user = $this->request->lastAppointment->user;
        $position = Role::getDescription($user->role_id?? Role::SPECIALIST);
        
        return array(
            'request_status' => $this->request->request_status,
            'message' => Lang::get('messages.patient_lab_tests.sent_to_patien', ['position' => $position]),
            'list' => PatientLabTestResource::collection($this->list),
        );
    }
}
