<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Jobs\SendNotificationJob;
use App\Enums\NotificationType;
use Lang;

class SendAgencySamplingReservationNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $samplingReservation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($samplingReservation)
    {
        $this->samplingReservation = $samplingReservation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->samplingReservation->refresh();
        $agency = $this->samplingReservation->agency;
        $bodyAr = $this->agencyNotification('ar');
        $bodyEn = $this->agencyNotification('en');

        $notificationData = $this->notificationData(
            $this->samplingReservation->id,
            NotificationType::SAMPLING_RESERVATION,
            $bodyAr,
            $bodyEn,
            array()
        );
        SendNotificationJob::dispatch(array($agency), $notificationData);
    }


    public function agencyNotification($lang)
    {
        $patientName = $this->samplingReservation->patient->translate($lang)->name;

        return Lang::get(
            'translations.mails.body.create_sampling_reservation',
            ['name' => $patientName], $lang
        );
    }
}
