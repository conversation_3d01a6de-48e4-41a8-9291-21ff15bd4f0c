<?php

namespace App\Http\Requests\Tasks;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\TimeZoneConverter;
use Carbon\Carbon;
use Lang;

class StoreRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'complete_date' => 'required|date|date_format:Y-m-d H:i:s|after:'.
                TimeZoneConverter::convertFromUtc(Carbon::now()->subMinute()->startOfMinute()),
            'description' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'complete_date.required' => Lang::get('validation.custom.tasks.complete_date.required'),
            'complete_date.date' => Lang::get('validation.custom.tasks.complete_date.date'),
            'complete_date.date_format' => Lang::get(
                'validation.custom.tasks.complete_date.date_format', ['format' => 'Y-m-d H:i:s']
            ),
            'complete_date.after' => Lang::get('validation.custom.tasks.complete_date.after'),
            'description.required' => Lang::get('validation.custom.tasks.description.required'),
            'description.max' => Lang::get('validation.custom.tasks.description.max', ['max' => 1000]),
            'description.min'  => Lang::get('validation.custom.task.description.min', ['max' => 3]),
        ];
    }
}
