<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\PatientLabTest;
use App\Enums\LabTestStatus;
use Carbon\Carbon;
use Lang;

class SamplingReservationFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'patient_id' => $this->patient_id,
            'patient_name' => $this->patient->name,
            'lab_test_id' => $this->lab_test_id,
            'lab_test_name' => $this->name?? $this->labTest->name,
            'sampling_date' => $this->samplingReservation->date_converted?? null,
            'agency_id' => $this->samplingReservation->agency_id?? null,
            'agency_name' => $this->samplingReservation->agency->name?? null,
            'status_name' => $this->getStatus(),
        );
    }

    public function getStatus()
    {
        switch (true) {
        case $this->samplingReservation:
            return Lang::get('translations.patient_lab_test_status.sampling_reservation');
                    break;
        case $this->isNotSamplingReservation():
            return Lang::get('translations.patient_lab_test_status.not_sampling_reservation');
                    break;
        case $this->isNotSamplingReservationWithDelay():
            return Lang::get('translations.patient_lab_test_status.lated');
                    break;
        default:
            return LabTestStatus::getDescription($this->status);
                    break;
        }
    }

    public function isNotSamplingReservation()
    {
        return $this->status == LabTestStatus::PAID && 
            $this->payment->updated_at->gte(Carbon::now()->subHours(24));
    }

    public function isNotSamplingReservationWithDelay()
    {
        return $this->status == LabTestStatus::PAID && 
            $this->payment->updated_at->lt(Carbon::now()->subHours(24));
    }
}
