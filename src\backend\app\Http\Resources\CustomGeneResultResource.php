<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CustomGeneResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'is_active' => $this->is_active,
            'name' => array(
                'ar' => $this->translate('ar')->name,
                'en' => $this->translate('en')->name
            ),
            'can_delete' => false, //TODO: check if the gene is used in any program or not
        );
    }
}
