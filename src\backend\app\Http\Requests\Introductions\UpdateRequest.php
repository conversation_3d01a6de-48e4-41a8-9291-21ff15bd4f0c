<?php

namespace App\Http\Requests\Introductions;
use App\Models\Introduction;
use Illuminate\Foundation\Http\FormRequest;
use Auth;
use Lang;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('update', Introduction::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'content' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'content.required' => Lang::get('validation.custom.introductions.content.required')
        ];
           
    }
}
