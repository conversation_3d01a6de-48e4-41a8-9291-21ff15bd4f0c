<?php

namespace App\Http\Controllers\Patient\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\PatientAuthRequest;
use App\Http\Requests\SystemUserTokenRequest;
use App\Http\Requests\PatientTokenRequest;
use App\Services\LoginService;
use Illuminate\Http\Response;
use Lang;

class LoginController extends Controller
{
    private $loginService;

    public function __construct(LoginService $loginService)
    {
        $this->loginService = $loginService;
    }

    public function sendOtp(PatientAuthRequest $request)
    {
        $expiredAt = $this->loginService->sendOtpToPatient($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.otp.success.send'), 'expired_at' => $expiredAt], Response::HTTP_OK
        );
    }

    public function token(PatientTokenRequest $request)
    {
        return $this->loginService->patientToken($request->validated());
    }

    public function authOtp(PatientAuthRequest $request)
    {
        $expiredAt = $this->loginService->sendAuthOtpToPatient($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.otp.success.send'), 'expired_at' => $expiredAt], Response::HTTP_OK
        );
    }
}