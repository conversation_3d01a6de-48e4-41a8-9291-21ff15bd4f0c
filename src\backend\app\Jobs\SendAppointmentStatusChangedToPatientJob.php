<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Traits\AppointmentTrait;
use App\Traits\MessageTrait;
use App\Enums\AppointmentStatus;
use App\Enums\NotificationType;
use App\Enums\MessageType;
use App;

class SendAppointmentStatusChangedToPatientJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        MessageTrait, NotificationTrait, AppointmentTrait;

    protected $appointment, $status, $reasonId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment, $status, $reasonId)
    {
        $this->appointment = $appointment;
        $this->status = $status;
        $this->reasonId = $reasonId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->appointment->refresh();
        App::setlocale('ar');
        $message = $this->smsMessage();
        $patient = $this->appointment->patient;
        $requestStatus = $this->appointment->request->request_status;
        $notification = $this->notificationObject(
            NotificationType::APPOINTMENT, $this->appointment->id, null, $requestStatus
        );
        $this->createMessage($patient, $message, MessageType::BOTH, $notification);
    }

    public function smsMessage()
    {
        return $this->status == AppointmentStatus::CONFIRMED?
            $this->confirmedAppointmentMessage($this->appointment)
            : $this->canceledAppointmentMessage($this->appointment);
    }
}
