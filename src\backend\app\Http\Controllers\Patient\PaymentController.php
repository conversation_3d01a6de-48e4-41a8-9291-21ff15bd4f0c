<?php

namespace App\Http\Controllers\Patient;

use App\Http\Resources\PaymentCollection;
use App\Http\Controllers\Controller;
use App\Http\Filters\PaymentFilter;
use App\Services\PaymentService;
use Illuminate\Http\Request;

class PaymentController extends Controller
{
    private $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    public function index(PaymentFilter $filter)
    {
        $result = $this->paymentService->filterByPatient($filter);
        return new PaymentCollection($result);
    }
}
