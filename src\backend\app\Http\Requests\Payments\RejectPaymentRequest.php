<?php

namespace App\Http\Requests\Payments;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Payment;
use Lang;
use Auth;

class RejectPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('reject', [Payment::class, $this->payment]);   
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'payment_reason_id' => 'nullable|required_without:reason|exists:payment_reasons,id,deleted_at,NULL',
            'reason' => 'nullable|required_without:payment_reason_id|regex:/^(?!.*\d)[a-zِِA-Z\p{Arabic}\s]+$/iu|
                unique:payment_reasons,reason,NULL,id,deleted_at,NULL|max:50',
        );
    }

    public function messages()
    {
        return array(
            'payment_reason_id.required_without'  => Lang::get(
                'validation.custom.payments.payment_reason_id.required_without'
            ),
            'payment_reason_id.exists'  => Lang::get('validation.custom.payments.payment_reason_id.exists'),
            'reason.required_without'  => Lang::get(
                'validation.custom.payments.reason.required_without'
            ),
            'reason.unique'  => Lang::get('validation.custom.payments.reason.unique'),
            'reason.regex'  => Lang::get('validation.custom.payments.reason.regex'),
            'reason.max'  => Lang::get('validation.custom.payments.reason.max', ['max' => 50]),
        );
    }
}
