<?php

namespace App\Jobs\PatientLabTests;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use Illuminate\Bus\Queueable;
use App\Enums\RequestStatus;
use App\Traits\MessageTrait;
use App\Enums\MessageType;
use Carbon\Carbon;
use Lang;

class NotifyPatientLabTestIsCreatedAndSendJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        NotificationTrait, MessageTrait;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $patient = $this->patientLabTest->patient;
        $requestStatus = $this->patientLabTest->request->request_status;
        $message = Lang::get('messages.messages.new_obligation_sent', [], 'ar');
        $notification = $this->notificationObject(
            NotificationType::PATIENT_LAB_TEST, $this->patientLabTest->id, null, $requestStatus
        );
        $this->createMessage($patient, $message, MessageType::BOTH, $notification); 
    }
}
