<?php

namespace App\Http\Resources\Agencies;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\Day;
use App;

class CustomAgencyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'name' => $this->translate(App::getlocale())->name,
            'city_id' => $this->city_id,
            'schedules' => $this->schedulesData(),
        );
    }

    public function schedulesData()
    {
        if ($this->schedules->count() != 7) {
            $workingDays = $this->schedules->pluck('day')->toArray();
            $days = Day::getValues();
            $offDays = array_diff($days, $workingDays);

            foreach ($offDays as $offDay) {
                $this->schedules->push((object)['day' => $offDay, 'from_hour' => null, 'to_hour' => null]);
            }
        }
        return ShowAgencyScheduleResource::collection($this->schedules);
    }
}
