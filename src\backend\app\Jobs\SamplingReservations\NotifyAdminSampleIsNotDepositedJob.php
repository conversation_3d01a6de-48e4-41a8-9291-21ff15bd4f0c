<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use App\Enums\SamplingReservationStatus;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Jobs\SendNotificationJob;
use App\Enums\NotificationType;
use App\Services\UserService;
use App\Enums\Role;
use Lang;

class NotifyAdminSampleIsNotDepositedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $samplingReservation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($samplingReservation)
    {
        $this->samplingReservation = $samplingReservation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->samplingReservation->refresh();
        $users = app(UserService::class)->users([Role::ADMIN]);
        $bodyAr = $this->agencyNotification('ar');
        $bodyEn = $this->agencyNotification('en');

        $notificationData = $this->notificationData(
            $this->samplingReservation->patient_id,
            NotificationType::PATIENT,
            $bodyAr,
            $bodyEn,
            array()
        );
        SendNotificationJob::dispatch($users, $notificationData);
    }


    public function agencyNotification($lang)
    {
        $patientName = $this->samplingReservation->patient->translate($lang)->name;

        return Lang::get(
            'translations.mails.body.NOT_DEPOSITED', ['name' => $patientName], $lang
        );
    }
}
