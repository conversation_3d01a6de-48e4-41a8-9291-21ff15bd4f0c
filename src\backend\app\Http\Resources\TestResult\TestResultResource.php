<?php

namespace App\Http\Resources\TestResult;

use Illuminate\Http\Resources\Json\JsonResource;

class TestResultResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray($request)
    {
        return [
            'id' => $this->id,
            'patient_lab_test_id' => $this->patient_lab_test_id,
            'general_introduction' => $this->when(isset($this->general_introduction), $this->general_introduction),
            'general_conclusion' => $this->when(isset($this->general_conclusion), $this->general_conclusion),
            'categories' => $this->when(isset($this->categories), function () {
                return $this->categories->map(function ($category) {
                    return [
                        'id' => $category->id,
                        'introduction' => $category->introduction,
                        'genes' => $category->genes->map(function ($gene) {
                            return [
                                'id' => $gene->id,
                                'gene' => $gene->gene ? [
                                    'id' => $gene->gene->id,
                                    'name' => $gene->gene->name,
                                ] : null,
                                'mutation_site' => $gene->mutationSite ? [
                                    'id' => $gene->mutationSite->id,
                                    'name' => $gene->mutationSite->name,
                                ] : null,
                                'gene_result' => $gene->geneResult ? [
                                    'id' => $gene->geneResult->id,
                                    'name' => $gene->geneResult->name,
                                ] : null,
                                'transmission_method' => $gene->transmissionMethod ? [
                                    'id' => $gene->transmissionMethod->id,
                                    'name' => $gene->transmissionMethod->name,
                                ] : null,
                                'gene_category' => $gene->geneCategory ? [
                                    'id' => $gene->geneCategory->id,
                                    'name' => $gene->geneCategory->name,
                                ] : null,
                                'illness' => $gene->illness ? [
                                    'id' => $gene->illness->id,
                                    'name' => $gene->illness->name,
                                ] : null,
                            ];
                        }),
                        'conclusion' => $category->conclusion ? $category->conclusion : null,
                    ];
                });
            }),
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}