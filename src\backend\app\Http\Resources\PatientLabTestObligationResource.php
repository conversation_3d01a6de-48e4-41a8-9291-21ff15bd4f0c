<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PatientLabTestObligationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'patient' => array(
                'id' => $this->patient->id,
                'identification_number' => $this->patient->identification_number,
                'name' => array(
                    'ar' => $this->patient->translate('ar')->name,
                    'en' => $this->patient->translate('en')->name
                ),
                'phone_number' => $this->patient->phone_number,
                'birthday' => $this->patient->birthday? $this->patient->birthday->format('Y/m/d') : null,
            ),
            'lab_test' => array(
                'id' => $this->labTest->id,
                'name' => $this->labTest->name,
                'price' => $this->labTest->price_after_discount,
            ),
            'obligation' => $this->obligation,
        );
    }
}
