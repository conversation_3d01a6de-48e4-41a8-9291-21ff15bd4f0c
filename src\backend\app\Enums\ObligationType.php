<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static INDIVIDUAL_GUARDIAN_OBLIGATION()
 * @method static static CHROMOSOMES_OBLIGATION()
 * @method static static NIPT_OBLIGATION()
 */
final class ObligationType extends Enum implements LocalizedEnum
{
    const INDIVIDUAL_GUARDIAN_OBLIGATION =   1;
    const CHROMOSOMES_OBLIGATION =           2;
    const NIPT_OBLIGATION =                  3;
}
