<?php

namespace App\Http\Controllers\Dashboard;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Filters\MessageFilter;
use App\Services\MessageService;
use App\Http\Resources\MessageCollection;

class MessageController extends Controller
{
    private $messageService;

    public function __construct(MessageService $messageService)
    {
        $this->messageService = $messageService;
    }

    public function index(MessageFilter $filter)
    {
        $result = $this->messageService->filter($filter);
        return new MessageCollection($result);
    }
}
