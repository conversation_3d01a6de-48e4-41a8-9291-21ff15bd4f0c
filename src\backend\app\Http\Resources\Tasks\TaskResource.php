<?php

namespace App\Http\Resources\Tasks;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Traits\TaskTrait;
use App\Enums\TaskStatus;
use App\Enums\Role;
use Lang;

class TaskResource extends JsonResource
{
    use TaskTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'description' => $this->description,
            'status_id' => $this->status,
            'status' => TaskStatus::getDescription($this->status),
            'complete_date' => $this->complete_date_converted,
            'created_at' => $this->created_at_converted,
            'created_by' => $this->creatable->name?? Lang::get('translations.user'),
            'creatable_role' => $this->creatable? Role::getDescription($this->creatable->role_id) : Lang::get('translations.user'),
            'can_updated' => $this->notCompLeted($this->resource),
            'can_deleted' => $this->new($this->resource),
            'history' => TaskHistoryResource::collection($this->taskStatusHistories),
        );
    }
}
