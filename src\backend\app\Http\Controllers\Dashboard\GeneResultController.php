<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\GeneResultFilter;
use App\Http\Resources\GeneResultResource;
use App\Models\Gene;
use App\Http\Resources\GeneResultSearchResource;
use App\Http\Resources\CustomGeneResultResource;
use App\Models\GeneResult;
use App\Services\GeneResultService;
use App\Http\Requests\GeneResults\StoreRequest;
use App\Http\Requests\GeneResults\UpdateRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class GeneResultController extends Controller
{
    protected $geneResultService;

    public function __construct(GeneResultService $geneResultService)
    {
        $this->geneResultService = $geneResultService;
    }

    public function index(GeneResultFilter $filter)
    {
        $this->authorize('viewAny', Gene::class);
        $result = $this->geneResultService->filter($filter);
        return new GeneResultSearchResource($result);
    }

    public function show(GeneResult $geneResult)
    {
        $this->authorize('view', GeneResult::class);
        return new CustomGeneResultResource($geneResult);
    }

    public function list()
    {
        $result = $this->geneResultService->list();
        return GeneResultResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->geneResultService->activeList();
        return GeneResultResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->geneResultService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.gene_results.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, GeneResult $geneResult)
    {
        $this->geneResultService->update($request->validated(), $geneResult);
        return response()->json(['message' => Lang::get('messages.gene_results.success.updated')], Response::HTTP_OK);
    }

    public function destroy(GeneResult $geneResult)
    {
        $this->authorize('delete', GeneResult::class);
        $this->geneResultService->delete($geneResult);
        return response()->json(['message' => Lang::get('messages.gene_results.success.deleted')], Response::HTTP_OK);
    }

    public function active(GeneResult $geneResult)
    {
        $this->authorize('active', GeneResult::class);
        $this->geneResultService->active($geneResult);
        return response()->json(['message' => Lang::get('messages.gene_results.success.actived')], Response::HTTP_OK);
    }

    public function deactive(GeneResult $geneResult)
    {
        $this->authorize('deactive', GeneResult::class);
        $this->geneResultService->deactive($geneResult);
        return response()->json(['message' => Lang::get('messages.gene_results.success.deactived')], Response::HTTP_OK);
    }

    // public function statistics()
    // {
    //     $this->authorize('statistics', Gene::class);
    //     $statistics = $this->geneService->statistics();
    //     return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    // }
}