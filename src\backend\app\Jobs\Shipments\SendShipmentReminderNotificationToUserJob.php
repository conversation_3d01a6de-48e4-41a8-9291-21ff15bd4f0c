<?php

namespace App\Jobs\Shipments;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Jobs\SendNotificationJob;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use Illuminate\Bus\Queueable;
use App\Services\UserService;
use App\Enums\LabTestStatus;
use App\Enums\Role;
use Lang;

class SendShipmentReminderNotificationToUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $patientLabTest;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        $statuses = array(LabTestStatus::SAMPLE_DEPOSITED, LabTestStatus::SAMPLE_NOT_SHIPPED);
        if (in_array($this->patientLabTest->status, $statuses)) {
            $patient = $this->patientLabTest->patient;
            $nameAr = $patient->translate('ar')->name;
            $nameEn = $patient->translate('en')->name;
            $users = app(UserService::class)->users([Role::FOLLOW_UP, Role::ADMIN]);            
            $bodyAr = Lang::get('translations.mails.body.shipment_reminder', ['name' => $nameAr], 'ar');
            $bodyEn = Lang::get('translations.mails.body.shipment_reminder', ['name' => $nameEn], 'en');

            $notificationData = $this->notificationData(
                $patient->id, NotificationType::PATIENT, $bodyAr, $bodyEn, array()
            );
            SendNotificationJob::dispatch($users, $notificationData);
        }
    }
}
