<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\ReminderService;
use Carbon\Carbon;

class SendAppointmentReminderToInternalUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $appointment, $reminderDuration;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment,$reminderDuration)
    {
        $this->appointment = $appointment;
        $this->reminderDuration = $reminderDuration;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if (!$this->appointment->trashed()) {
            $this->appointment->refresh();
            $isReminderTime = $this->checkCurrentTimeEqualToReminderTime();
            $isNow = $this->reminderDuration > 0? false : true;
            $isReminderTime? 
                app(ReminderService::class)
                ->sendAppointmentReminderToInternalUser($this->appointment, $this->reminderDuration, $isNow)
                : null;
        }
    }

    public function checkCurrentTimeEqualToReminderTime()
    {
        $reminderTime = $this->appointment->start_date->subMinutes($this->reminderDuration);
        return $reminderTime->diffInMinutes(Carbon::now()) <= 10;
    }
}
