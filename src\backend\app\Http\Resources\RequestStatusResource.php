<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\ENums\RequestStatus;
use Lang;

class RequestStatusResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->resource,
            'name' => Lang::get('translations.request_statuses.'. RequestStatus::getKey($this->resource)),
        );
    }
}
