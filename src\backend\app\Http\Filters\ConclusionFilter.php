<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;

class ConclusionFilter extends Filter
{
    public $fields = array('content','is_active', 'created_at', 'updated_at');

    /**
     * @param string $search
     */
    public function search(string $search)
    {
         $this->builder->where(
            function ($query) use ($search) {
                $query->where('content', 'like', '%'.$search.'%');
            }
        );
    }

    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}