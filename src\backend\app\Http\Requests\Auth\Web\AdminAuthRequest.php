<?php

namespace App\Http\Requests\Auth\Web;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class AdminAuthRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'username' => 'required|max:50',
            'password' => 'required',
        ];
    }


    public function messages()
    {
        return [
            'username.required' => Lang::get('validation.custom.agencies.username.required'),
            'username.max' => Lang::get('validation.custom.agencies.username.max'),
            'password.required'  => Lang::get('validation.custom.login.password.required'),
        ];
    }
}
