<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PaymentDashboardStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'total' => $this->total,
            'paid' => $this->paid,
            'pending' => $this->pending,
            'not_paid' => $this->not_paid,
            'not_paid_with_delay' => $this->not_paid_with_delay,
            'rejected' => $this->rejected,
        );
    }
}
