<?php

namespace App\Http\Controllers\Patient\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\OtpVerificationService;
use App\Http\Requests\GeneratePatientOtpRequest;
use Lang;

class OtpController extends Controller
{

    public function __invoke(GeneratePatientOtpRequest $request)
    {
        $expiredAt = app(OtpVerificationService::class)->resendOtpForPatient($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.otp.success.send'), 'expired_at' => $expiredAt], Response::HTTP_OK
        );
    }
}