<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\Day;
use Carbon\Carbon;

class ScheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'day' => $this->day,
            'day_name' => Day::getDescription($this->day),
            'is_working_day' => true,
            'from_hour' => Carbon::parse($this->from_hour)->format('H:i'),
            'to_hour' => Carbon::parse($this->to_hour)->format('H:i'),
        );
    }
}
