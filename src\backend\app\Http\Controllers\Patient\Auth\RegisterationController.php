<?php

namespace App\Http\Controllers\Patient\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\RegisterService;
use App\Http\Requests\RegisterPatientRequest;
use App\Http\Requests\ValidateIdentificationNumberRequest;
use App\Http\Requests\ValidatePhoneNumberRequest;
use Lang;

class RegisterationController extends Controller
{
    public function register(RegisterPatientRequest $request)
    {
        $expiredAt = app(RegisterService::class)->register($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.register.success.created'), 'expired_at' => $expiredAt], Response::HTTP_OK
        );
    }

    public function validateIdentificationNumber(ValidateIdentificationNumberRequest $request)
    {
        return response()->json(
            ['message' => Lang::get(
                'messages.validate.success.identification-number', 
                ['data' => $request['identification_number']]
            )],
            Response::HTTP_OK
        );
    }

    public function validatePhoneNumber(ValidatePhoneNumberRequest $request)
    {
        return response()->json(
            ['message' => Lang::get('messages.validate.success.phone-number', ['data' => $request['phone_number']])],
            Response::HTTP_OK
        );
    }
}