<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\ContactFilter;
use App\Http\Resources\ContactCollection;
use App\Http\Resources\ContactResource;
use App\Http\Requests\Contacts\StoreContactRequest;
use App\Http\Requests\Contacts\UpdateContactRequest;
use App\Services\ContactService;
use App\Models\Contact;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lang;

class ContactController extends Controller
{
    private $contactService;

    public function __construct(ContactService $contactService)
    {
        $this->contactService = $contactService;
    }

    public function show(Contact $contact)
    {
        return new ContactResource($contact);
    }

    public function index(ContactFilter $filter) 
    {
        $result = $this->contactService->filter($filter);
        return new ContactCollection($result);
    }

    public function store(StoreContactRequest $request)
    {
        $this->contactService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.contacts.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateContactRequest $request, Contact $contact)
    {
        $this->contactService->update($request->validated(), $contact);
        return response()->json(['message' => Lang::get('messages.contacts.success.updated')], Response::HTTP_OK);
    }

    public function destroy(Contact $contact)
    {
        $this->authorize('delete', [Contact::class]);
        $this->contactService->delete($contact);
        return response()->json(['message' => Lang::get('messages.contacts.success.deleted')], Response::HTTP_OK);
    }
}
