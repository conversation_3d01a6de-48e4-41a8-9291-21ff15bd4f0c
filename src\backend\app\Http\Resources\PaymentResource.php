<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\PaymentMethod;
use App\Enums\PaymentStatus;
use App\Http\Resources\Tamara\InstallmentPaymentResource;
use App\Traits\UserTrait;
use App\Models\Payment;
use App\Traits\PaymentTrait;
use Auth;
use Lang;

class PaymentResource extends JsonResource
{
    use UserTrait,PaymentTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $user = Auth::user();

        return array(
            'id' => $this->id,
            'patient_id' => $this->patient_id,
            'patient_name' => $this->patient->translate()->name,
            'lab_test_id' => $this->lab_test_id,
            'lab_test_name' => $this->labTest->translate()->name,
            'price' => $this->price,
            'payment_method' => $this->payment_method,
            'payment_method_name' => !$this->paymentMethod(PaymentMethod::MANUAL_PAYMENT)? PaymentMethod::getDescription($this->payment_method) : Lang::get($this->paymentable->offlinePaymentMethod->name),
            'status' => $this->status,
            'status_name' => $this->getStatus(),
            'created_at' => $this->created_at_converted,
            'notes' => Lang::get($this->notes),
            'paymentable' => $this->getPaymentableResource($this->payment_method, $this->paymentable),
            'is_new' => $this->checkIsPaymentNew(),
            'invoice_url' => $this->invoice->url?? null,
            'can_refund' => $this->patientLabTest->canRefund() && Payment::canRefund($user->role_id),
        );
    }

    public function checkIsPaymentNew()
    {
        $user = $this->systemUser();
        return $user->payments_last_seen? $this->updated_at->gte($user->payments_last_seen) : true;
    }

    public function getStatus()
    {
        switch (true) {
        case $this->isNotPaid():
            return Lang::get('translations.payment_status.not_paid');
                        break;
        case $this->isNotPaidWithDelay():
            return Lang::get('translations.payment_status.lated');
                        break;
        default:
            return PaymentStatus::getDescription($this->status);
                        break;
        }
    }
}
