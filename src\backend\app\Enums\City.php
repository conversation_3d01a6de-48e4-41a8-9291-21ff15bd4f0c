<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static ARAR()
 * @method static static TAIF()
 */
final class City extends Enum implements LocalizedEnum
{
    const AL_BAHAH =            1;
    const ARAR =                15; 
    const RIYADH =              21;
    const WADIAD_DAWASIR =      23;
    const BRAYDAH =             60;
    const UNAYZAH =             61; 
    const AR_RASS =             62;
    const MEDINA =              79;
    const DAMMAM =              89;
    const JUBAIL =              91;
    const HAFAR_AL_BATIN =      92;
    const AL_KHABAR =           94;
    const SAIHAT =              97;
    const BUQAYQ =              101;
    const TABUK =               147;
    const UMLUJ =               148;
    const JAZAN =               156;
    const HAIL =                188; 
    const KHAMIS_MUSHAIT =      195;
    const ABHA =                196;
    const JEDDAH =              211;
    const TAIF =                213;
    const NAJRAN =              240;    
    const AL_KHARJ =            246;
    const AL_AHSA =             247;   
}
