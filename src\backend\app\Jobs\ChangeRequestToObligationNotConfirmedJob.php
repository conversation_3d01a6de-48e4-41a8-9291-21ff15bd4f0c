<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\RequestService;
use App\Enums\RequestStatus;

class ChangeRequestToObligationNotConfirmedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $request;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        if ($this->request->isStatus(RequestStatus::OBLIGATION_SENT)) {
            app(RequestService::class)
                ->updateRequestStatus($this->request, RequestStatus::OBLIGATION_NOT_CONFIRMED);
        }
    }
}
