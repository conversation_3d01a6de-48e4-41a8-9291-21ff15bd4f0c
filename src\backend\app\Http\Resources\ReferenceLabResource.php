<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\TubeType;
use Lang;

class ReferenceLabResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'code' => $this->code,
            'name' => array(
                'ar' => $this->translate('ar')->name?? null,
                'en' => $this->translate('en')->name?? null,
            ),
            'user_name' => array(
                'ar' => $this->translate('ar')->user_name?? null,
                'en' => $this->translate('en')->user_name?? null,
            ),
            'email' => $this->email,
            'phone_number' => $this->phone_number,
            'user_position' => $this->user_position,
            'map_link' => $this->map_link,
            'city_name' => $this->city_name,
            'country_id' => $this->country_id,
            'country_name' => Lang::get($this->country->name?? ''),
            'is_active' => $this->is_active,
            'can_delete' => $this->lab_tests_count? false : true,
        );
    }
}
