<?php

namespace App\Http\Requests\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\RequestSource;
use Lang;

class StoreRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'message_text' => 'required|max:1000',
            'request_source' => 'required|in:'
                .RequestSource::WEBSITE.','. RequestSource::ANDROID_APP.','. RequestSource::IOS_APPS,
        ];
    }

    public function messages()
    {
        return [
            'message_text.required' => Lang::get('validation.custom.requests.message_text.required'),
            'message_text.max' => Lang::get('validation.custom.requests.message_text.max'),
            'request_source.required' => Lang::get('validation.custom.requests.request_source.required'),
            'request_source.in' => Lang::get(
                'validation.custom.requests.request_source.in',
                ['in' => RequestSource::WEBSITE.','. RequestSource::ANDROID_APP.','. RequestSource::IOS_APPS]
            ),
        ];
    }
}
