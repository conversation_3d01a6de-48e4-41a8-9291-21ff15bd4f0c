<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;

class CalendarResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'appointments' => CustomAppointmentResource::collection($this->appointments),
            'holiydays' => $this->holiydays,
            'schedules' => ScheduleResource::collection($this->schedules),
        );
    }
}
