<?php

namespace App\Listeners\Appointments;

use App\Jobs\Appointments\MissedAppointmentReminderJob;
use App\Jobs\SendChangeAppointmentUserNotificationJob;
use App\Jobs\SendPatientAppointmentNotificationJob;
use App\Jobs\SendUserAppointmentNotificationJob;
use App\Events\Appointments\AppointmentUpdated;
use App\Jobs\GenerateAppointmentShortLinkJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Jobs\MissedAppointmentJob;
use App\Services\ReminderService;
use App\Traits\MailTrait;

class NotifyAppointmentUpdated
{
    use MailTrait;
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Appointments\AppointmentUpdated $event
     * @return void
     */
    public function handle(AppointmentUpdated $event)
    {
        $event->updatedAppointment->isVideo()?
            GenerateAppointmentShortLinkJob::dispatch($event->updatedAppointment->onlineMeeting)
            ->onQueue(config('queue.queues.notifications'))
            : null;
        SendPatientAppointmentNotificationJob::dispatch($event->updatedAppointment, 'update')
            ->onQueue(config('queue.queues.notifications'));
        $this->sendUserNotification($event->oldAppointment, $event->updatedAppointment);
        MissedAppointmentReminderJob::dispatch($event->updatedAppointment)
            ->delay($event->updatedAppointment->start_date->addMinutes(10))
            ->onQueue(config('queue.queues.notifications'));
        MissedAppointmentJob::dispatch($event->updatedAppointment)->delay($event->updatedAppointment->end_date)
            ->onQueue(config('queue.queues.requests'));
    }

    public function sendUserNotification($oldAppointment, $updatedAppointment)
    {
        $isTheSameUser = $oldAppointment->user == $updatedAppointment->user;
        if ($isTheSameUser) {
            SendUserAppointmentNotificationJob::dispatch($updatedAppointment, 'update')
                ->onQueue(config('queue.queues.notifications'));
            $this->sendAppointmentMail($updatedAppointment, 'update');
            return true;
        }
        SendChangeAppointmentUserNotificationJob::dispatch(
            $oldAppointment->user,
            $oldAppointment->patient,
            $oldAppointment->user_date,
            $oldAppointment->user_hour,
            $oldAppointment->type,
            $oldAppointment->purpose,
            $oldAppointment->id
        )->onQueue(config('queue.queues.notifications'));
        SendUserAppointmentNotificationJob::dispatch($updatedAppointment, 'create')
            ->onQueue(config('queue.queues.notifications'));
        $this->sendAppointmentMail($updatedAppointment, 'create');
    }
}
