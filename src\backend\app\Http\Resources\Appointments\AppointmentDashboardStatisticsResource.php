<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;

class AppointmentDashboardStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'total' => new AppointmentStatusStatisticsResource($this->total),
            'confirmed' => new AppointmentStatusStatisticsResource($this->confirmed),
            'completed' => new AppointmentStatusStatisticsResource($this->completed),
            'canceled' => new AppointmentStatusStatisticsResource($this->canceled),
            'missed' => new AppointmentStatusStatisticsResource($this->missed),
        );
    }
}
