<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\RequestSource;
use App\Enums\RequestStatus;
use App\Enums\RequestStage;
use App\Models\Patient;
use App\Models\Request;
use Lang;

class RequestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $reason = $this->lastRequestHistory->cancellation_reason_id?
            $this->lastRequestHistory->cancellationReason : null;

        return array(
            'id' => $this->id,
            'message_text' => $this->message_text,
            'patient_id' => get_class($this->requestable) == Patient::class? $this->requestable->id : null,
            'patient_name' => $this->requestable->name,
            'patient_phone' => $this->requestable->phone_number,
            'patient_image' => $this->requestable->profile_image,
            'patient_gender_type' => $this->requestable->gender_type,
            'request_source' => Lang::get('translations.request_sources.' .RequestSource::getKey($this->request_source)),
            'request_status' => $this->status(),
            'request_status_name' => RequestStatus::getDescription($this->status()),
            'created_at' => $this->created_at_converted,
            'stage' => $this->stage,
            'stage_name' => RequestStage::getDescription($this->stage),
            'can_cancel' => in_array($this->request_status, Request::canCancelStatuses()),
            'cancel_reason_title' => $this->isStatus(RequestStatus::CANCELED)? Lang::get($reason->title?? '') : null,
        );
    }
}
