<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\Requests\StoreRequestRequest;
use App\Http\Requests\Requests\StoreAnonymousRequestRequest;
use App\Services\RequestService;
use App\Models\Request as RequestModel;
use Lang;

class RequestController extends Controller
{
 
    private $requestService;

    public function __construct(RequestService $requestService)
    {
        $this->requestService = $requestService;
    }
    
    public function store(StoreRequestRequest $request)
    {
        $this->authorize('create', RequestModel::class);
        return $this->requestService->createByPatient($request->validated());
    }

    public function storeByAnonymous(StoreAnonymousRequestRequest $request)
    {
        return $this->requestService->createByAnonymous($request->validated());
    }

    public function notify(RequestModel $request)
    {
        $this->authorize('notify', [RequestModel::class, $request]);
        $this->requestService->notify($request);
        return response()->json(['message' => Lang::get('messages.requests.success.notify')], Response::HTTP_OK);
    }

    public function activeRequest()
    {
        return $this->requestService->activeRequest();
    }
}
