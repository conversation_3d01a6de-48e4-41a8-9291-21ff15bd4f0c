<?php

namespace App\Http\Requests\SamplingReservations;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\CheckInAgencyWorkingHours;
use App\Rules\CheckLabTestWorkingDay;
use App\Services\TimeZoneConverter;
use App\Models\SamplingReservation;
use Carbon\Carbon;
use Lang;
use Auth;

class UpdateSamplingReservationRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can(
            'updatebyPatient', 
            [SamplingReservation::class, $this->patient_lab_test, $this->sampling_reservation]
        );  
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agency_id' => 'required|exists:agencies,id,deleted_at,NULL',
            'date' => ['required', 'date', 'date_format:Y-m-d H:i:s', 'after:'.
                TimeZoneConverter::convertFromUtc(Carbon::now()->startOfMinute()),
                new CheckInAgencyWorkingHours($this->agency_id),
                new CheckLabTestWorkingDay($this->agency_id, $this->patient_lab_test)],
        ];
    }

    public function messages()
    {
        return [
            'agency_id.required' => Lang::get('validation.custom.sampling-reservations.agency_id.required'),
            'agency_id.exists' => Lang::get('validation.custom.sampling-reservations.agency_id.exists'),
            'date.required' => Lang::get('validation.custom.sampling-reservations.date.required'),
            'date.date' => Lang::get('validation.custom.sampling-reservations.date.date'),
            'date.date_format' => Lang::get(
                'validation.custom.sampling-reservations.date.date_format', ['format' => 'Y-m-d H:i:s']
            ),
            'date.after' => Lang::get('validation.custom.sampling-reservations.date.after'),
        ];
    }
}
