<?php

namespace App\Jobs\Notes;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Jobs\SendNotificationJob;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use Illuminate\Bus\Queueable;
use App\Services\UserService;
use App\Enums\Role;
use Lang;

class SendNoteUsersNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $note;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($note)
    {
        $this->note = $note;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
            $user = $this->note->user;
            $users = $this->note->users;
            $nameAr = $user->translate('ar')->name;
            $nameEn = $user->translate('en')->name;
            $bodyAr = Lang::get('translations.notes.notification', ['name' => $nameAr], 'ar');
            $bodyEn = Lang::get('translations.notes.notification', ['name' => $nameEn], 'en');

            $notificationData = $this->notificationData(
                $this->note->patient->id, NotificationType::PATIENT, $bodyAr, $bodyEn, array()
            );
            SendNotificationJob::dispatch($users, $notificationData);
    }
}
