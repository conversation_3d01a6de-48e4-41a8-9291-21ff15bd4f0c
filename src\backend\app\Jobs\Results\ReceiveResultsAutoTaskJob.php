<?php

namespace App\Jobs\Results;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Queueable;
use App\Services\UserService;
use App\Services\TaskService;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;
use App;

class ReceiveResultsAutoTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        App::setlocale('ar');
        $patientName = $this->patientLabTest->patient->name;
        $receptionists = app(UserService::class)->users([Role::RECEPTIONIST]);
        $data = array(
            'description' => Lang::get(
                'messages.sampling-reservations.deposited_auto_task',
                ['name' => $patientName, 'days' => $this->patientLabTest->result_due_days],
                'ar'
            ),
            'complete_date' => $this->getResultDueDate(),
        );
        foreach ($receptionists as $receptionist) {
            app(TaskService::class)->createModel($data, $receptionist);  
        }
    }

    public function getResultDueDate()
    {
        return $this->patientLabTest->sample_date->addDays($this->patientLabTest->result_due_days);
    }
}
