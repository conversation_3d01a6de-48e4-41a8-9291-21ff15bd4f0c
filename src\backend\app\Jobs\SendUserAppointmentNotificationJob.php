<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\AppointmentService;
use Lang;

class SendUserAppointmentNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $appointment, $action;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment, $action)
    {
        $this->appointment = $appointment;
        $this->action = $action;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $user = $this->appointment->user;
        $bodyAr = $this->userNotification('ar');
        $bodyEn = $this->userNotification('en');

        app(AppointmentService::class)
            ->notifyNotification($this->appointment->patient_id, $user, $bodyAr, $bodyEn);
    }

    public function userNotification($lang)
    {
        $patientName = $this->appointment->patient->translate($lang)->name;
        $purpose = $this->appointment->purposeName($lang);
        $type = $this->appointment->typeName($lang);
        $hour = $this->appointment->user_hour;
        $date = $this->appointment->user_date;

        return Lang::get(
            'translations.mails.body.'.$this->action.'_appointment',
            ['purpose' => $purpose, 'type' => $type ,'name' => $patientName, 'hour' => $hour, 'date' => $date],
            $lang
        );
    }
}
