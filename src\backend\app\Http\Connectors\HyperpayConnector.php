<?php

namespace App\Http\Connectors;

use GuzzleHttp\Exception\GuzzleException;
use App\Enums\OnlinePaymentMethod;
use App\Enums\PaymentRequestType;
use App\Services\CountryService;
use App\Services\HyperService;
use Illuminate\Http\Response;
use App\Enums\Country;
use GuzzleHttp\Client;
use Lang;

class HyperpayConnector
{
    public static function sendRequest($method, $url, $data)
    {
        $body = array();
        $body['headers'] = self::getHeaders();
        $body['form_params'] = $data?? array();
        $body['http_errors'] = false;
        
        $client = new Client(['base_uri' => config('hyperPay.base_url')]);
        $response= $client->request($method, $url, $body);
        
        return  $response;
    }

    public static function getHeaders()
    {
        return array(
            'content-type' => 'application/x-www-form-urlencoded',
            'Authorization' => 'Bearer '. config('hyperPay.token'),
        );
    }

    public static function prepareCheckout($patient, $amount, $entityType, $id)
    {
        $body = self::checkoutData($patient, $amount, $entityType, $id);

        $response = self::sendRequest('POST', 'v1/checkouts', $body);
        return self::getResponse($response, $body, PaymentRequestType::CHECKOUT, $id, $patient->id);
    }

    public static function getPaymentStatus($checkoutId, $entityId, $paymentId, $patientId)
    {
        $body = array('checkout_id' => $checkoutId, 'entity_id' => $entityId);
        $response = self::sendRequest('GET', 'v1/checkouts/' .$checkoutId. '/payment?entityId='.$entityId, null);
        return self::getResponse($response, $body, PaymentRequestType::GET_PAYMENT, $paymentId, $patientId);
    }

    public static function getTransactionReport($entityId, $merchantTransactionId, $paymentId, $patientId)
    {
        $body = array('merchant_transaction_id' => $merchantTransactionId, 'entity_id' => $entityId);
        $response = self::sendRequest(
            'GET', 'v1/query?entityId='.$entityId.'&merchantTransactionId='.$merchantTransactionId, null
        );
        return self::getResponse($response, $body, PaymentRequestType::TRANSACTION_REPORT, $paymentId, $patientId);
    }
 
    private static function getResponse($response, $body, $type, $paymentId, $patientId)
    {
        $jsonResponse = json_decode($response->getBody()->getContents());
        $responseCode = $response->getStatusCode();
        $isSuccess = self::isSuccessCode($jsonResponse->result->code);
        $result = (object) array(
            'is_success' => $responseCode == config('hyperPay.success_code') && $isSuccess,
            'response' => $jsonResponse,
            'body' => $body
        );

        app(HyperService::class)->logData($result, $isSuccess, $type, $paymentId, $patientId);

        return $result;
    }

    public static function isSuccessCode($code)
    {
        $successPatterns = collect(config('hyperPay.success_pattern'));
        $pattern = $successPatterns->first(
            function ($pattern) use ($code) {
                return preg_match($pattern, $code);
            }
        );

        return $pattern? true : false;
    }

    private static function checkoutData($patient, $amount, $entityType, $id)
    {
        $patientName = $patient->translate('en')->name;
        $country = app(CountryService::class)->findBy('id', Country::SAUDI_ARABIA);
        $address = Lang::get($patient->city->region->name, [], 'en').'، '.
            Lang::get($patient->city->name, [], 'en').'، '.Lang::get($country->name, [], 'en');

        $data = array(
            'entityId' => config('hyperPay.entity_ID_'.$entityType),
            'amount' => ceil($amount),
            'currency' =>  config('hyperPay.currency'),
            'paymentType' => config('hyperPay.payment_type'),
            'merchantTransactionId' => time() . '_'.$id,
            'customer.email' => $patient->email?? $patient->dummy_email,
            'customer.givenName' => explode(' ', $patientName)[0],
            'customer.surname' => str_contains($patientName, ' ')? explode(' ', $patientName)[1] : $patientName,
            'billing.street1' => $address,
            'billing.city' => Lang::get($patient->city->name, [], 'en'),
            'billing.state' => Lang::get($country->name, [], 'en'),
            'billing.country' => $country->code,
            'billing.postcode' => $patient->city->postcode,
        ); 
        config('hyperPay.is_test') && $entityType == 'VISA'? 
            $data['testMode'] = config('hyperPay.test_mode') : null;
        return self::setPatientCards($patient, $data, $entityType);
    }

    public static function setPatientCards($patient, $data, $entityType)
    {
        $paymentMethod = OnlinePaymentMethod::getValue($entityType);
        $cards = $patient->cards()->whereNotNull('registration_id')
            ->where('online_payment_method', $paymentMethod)->get();
        foreach ($cards as $key => $patientCard) {
            $data['registrations['.$key.'].id'] = $patientCard->registration_id;
        }

        return $data;
    }
}
