<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\IntroductionFilter;
use App\Http\Resources\IntroductionResource;
use App\Models\Introduction;
use App\Http\Resources\IntroductionSearchResource;
use App\Http\Resources\CustomIntroductionResource;
use App\Services\IntroductionService;
use App\Http\Requests\Introductions\StoreRequest;
use App\Http\Requests\Introductions\UpdateRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class IntroductionController extends Controller
{
    protected $introductionService;

    public function __construct(IntroductionService $introductionService)
    {
        $this->introductionService = $introductionService;
    }

    public function index(IntroductionFilter $filter)
    {
        $this->authorize('viewAny', Introduction::class);
        $result = $this->introductionService->filter($filter);
        return new IntroductionSearchResource($result);
    }

  

    public function show(Introduction $introduction)
    {
        $this->authorize('view', Introduction::class);
        return new CustomIntroductionResource($introduction);
    }

    public function list()
    {
        $result = $this->introductionService->list();
        return IntroductionResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->introductionService->activeList();
        return IntroductionResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->introductionService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.introductions.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, Introduction $introduction)
    {
        $this->introductionService->update($request->validated(), $introduction);
        return response()->json(['message' => Lang::get('messages.introductions.success.updated')], Response::HTTP_OK);
    }

    public function destroy(Introduction $introduction)
    {
        $this->authorize('delete', Introduction::class);
        $this->introductionService->delete($introduction);
        return response()->json(['message' => Lang::get('messages.introductions.success.deleted')], Response::HTTP_OK);
    }

    public function active(Introduction $introduction)
    {
        $this->authorize('active', Introduction::class);
        $this->introductionService->active($introduction);
        return response()->json(['message' => Lang::get('messages.introductions.success.actived')], Response::HTTP_OK);
    }

    public function deactive(Introduction $introduction)
    {
        $this->authorize('deactive', Introduction::class);
        $this->introductionService->deactive($introduction);
        return response()->json(['message' => Lang::get('messages.introductions.success.deactived')], Response::HTTP_OK);
    }

    // public function statistics()
    // {
    //     $this->authorize('statistics', Gene::class);
    //     $statistics = $this->geneService->statistics();
    //     return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    // }
}