<?php

namespace App\Http\Resources\SamplingReservations;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\SamplingReservationStatus;
use App\Enums\ShippingCompany;
use App\Enums\TubeType;

class SamplingReservationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $shipment = $this->patientLabTest->shipments->first();
        return array(
            'id' => $this->id,
            'patient_name' => $this->patient->name,
            'date' => $this->date_converted,
            'status' => $this->status,
            'status_name' => SamplingReservationStatus::getDescription($this->status),
            'sample_quantity' => $this->patientLabTest->sample_quantity,
            'tube_type' => $this->patientLabTest->tube_type,
            'tube_type_name' => TubeType::getDescription($this->patientLabTest->tube_type),
            'tubes_count' => $this->patientLabTest->tubes_count,
            'sample_type' => $this->patientLabTest->sample_type,
            'lab_test_name' => $this->patientLabTest->name,
            'notes' => $this->notes,
            'shipping_company' => $shipment? $shipment->shipping_company : null,
            'shipping_company_name' => $shipment? ShippingCompany::getDescription($shipment->shipping_company) : null,
        );
    }
}
