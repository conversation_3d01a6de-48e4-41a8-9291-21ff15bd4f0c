<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Traits\RequestTrait;
use App\Models\Patient;
use Auth;

class InsuranceLetterFilter extends Filter
{
    use RequestTrait;

    public $activeRequest =        -1;
    public $previousRequest =      -2;
    public $fields = array('created_at', 'updated_at');

    /**
     * @param string $status
     */
    public function status(string $status)
    {
        $patient = Auth::user();
        $request = $this->getActiveRequest($patient);
        $this->builder->when(
            $status == $this->activeRequest, function ($query) use ($request) {
                return $query->requestId($request->id?? null);
            }
        )->when(
            $status == $this->previousRequest, function ($query) use ($request) {
                return $query->notRequestId($request->id?? null);
            }
        );
    }

    /**
     * Sort the insurance letters by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}