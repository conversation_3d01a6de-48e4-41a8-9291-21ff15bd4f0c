<?php

namespace App\Http\Resources\Notes;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\TubeType;

class NoteResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'note' => $this->note,
            'user_id' => $this->user_id,
            'patient_id' => $this->patient_id,
            'name' => $this->user->translate()->name,
            'profile_image' => $this->user->profile_image,
            'created_at' => $this->created_at_converted,
            'users' => UserResource::collection($this->users),
        );
    }
}
