<?php

namespace App\Events\Requests;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RequestObligationSent
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $request, $patient;
    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($request, $patient)
    {
        $this->request = $request;
        $this->patient = $patient;
    }
}
