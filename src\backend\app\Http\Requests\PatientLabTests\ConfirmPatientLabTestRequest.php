<?php

namespace App\Http\Requests\PatientLabTests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\PatientLabTest;
use Lang;
use Auth;

class ConfirmPatientLabTestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $this->patient = Auth::user();
        return $this->patient && $this->patient->can('confirm', [PatientLabTest::class, $this->approved_lab_tests]);   
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'approved_lab_tests' => 'required|array',
            'approved_lab_tests.*' => 'required|exists:patient_lab_tests,id,deleted_at,NULL,patient_id,'
                .$this->patient->id.'|distinct',
        );
    }

    public function messages()
    {
        return array(
            'approved_lab_tests.required' => Lang::get(
                'validation.custom.patient_lab_test.approved_lab_tests.required'
            ),
            'approved_lab_tests.array' => Lang::get('validation.custom.patient_lab_test.approved_lab_tests.array'),
            'approved_lab_tests.*.exists' => Lang::get('validation.custom.patient_lab_test.approved_lab_tests.exists'),
            'approved_lab_tests.*.distinct' => Lang::get(
                'validation.custom.patient_lab_test.approved_lab_tests.distinct'
            ),
        );
    }
}
