<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\UserService;
use App\Services\PatientService;
use App\Services\AgencyService;
use App\Traits\GuardTrait;
use Carbon\Carbon;
use Lang;

class BlockResendOtp
{
    use GuardTrait;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $guard)
    {
        $user = call_user_func(array($this, $guard), $request);
        if (!$user) {
            return $next($request);
        }

        $otpVerification = $user->otpVerification;
        if ($this->notAllowedToSendNow($otpVerification)) { 
            abort(Response::HTTP_TOO_MANY_REQUESTS, Lang::get('messages.too_many_attemps'));
        }

        if ($otpVerification 
            && $this->otpAtempsNumberNotValid($otpVerification) 
            && $this->otpDurationNotValid($otpVerification)
        ) {
            $service = $this->getService($guard);
            $service->block($user);
            $this->returnBlockedError($user);
        }

        return $next($request);
    }

    public function otpAtempsNumberNotValid($otpVerification)
    {
        return $otpVerification->attempt_number >= config('app.otp_attempts');
    }

    public function otpDurationNotValid($otpVerification)
    {
        return (Carbon::now())
            ->diffInMinutes($otpVerification->created_at) <= config('app.otp_attempts_duration');
    }

    public function notAllowedToSendNow($otpVerification)
    {
        return $otpVerification && $otpVerification->expired_at->gt(Carbon::now());
    }

    public function user($request)
    {
        return app(UserService::class)->findBy('username', $request['username']?? '');
    }

    public function patient($request)
    {
        $column = $request['phone_number']? 'phone_number' : 'identification_number';
        $value = $request['phone_number']? $request['phone_number'] : ($request['username']?? '');
        return app(PatientService::class)->findBy($column, $value);
    }

    public function agency($request)
    {
        return app(AgencyService::class)->findBy('username', $request['username']?? '');
    }

    public function returnBlockedError($user)
    {
        $user->refresh();
        $hours = $user->blocked_at->addMinutes(config('app.otp_block_time'))->diffInHours(Carbon::now());
        abort(Response::HTTP_FORBIDDEN, Lang::get('messages.blocked', ['hours' => $hours]));
    }
}
