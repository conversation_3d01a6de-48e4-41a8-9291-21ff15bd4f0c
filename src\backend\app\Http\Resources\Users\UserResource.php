<?php

namespace App\Http\Resources\Users;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\Role;
use App;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'username' => $this->username,
            'phone_number' => $this->phone_number,
            'role_name' => Role::getDescription($this->role_id),
            'role_id' => $this->role_id,
            'name' => $this->translate(App::getlocale())->name,
            'profile_image' => $this->profile_image,
            'email' => $this->email,
            'is_blocked' => $this->blocked_at? true : false,
        );
    }
}
