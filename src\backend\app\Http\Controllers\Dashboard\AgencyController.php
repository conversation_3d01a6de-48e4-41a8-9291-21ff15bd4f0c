<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\Agencies\CustomAgencyResource;
use App\Http\Resources\Agencies\AgencySearchResource;
use App\Http\Requests\Agencies\StoreAgencyRequest;
use App\Http\Requests\Agencies\UpdateAgencyRequest;
use App\Http\Resources\Agencies\AgencyResource;
use App\Http\Filters\AgencyFilter;
use App\Services\AgencyService;
use App\Models\Agency;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lang;

class AgencyController extends Controller
{
    private $agencyService;

    public function __construct(AgencyService $agencyService)
    {
        $this->agencyService = $agencyService;
    }

    public function show(Agency $agency)
    {
        $this->authorize('view', Agency::class);
        return new AgencyResource($agency);
    }

    public function index(AgencyFilter $filter)
    {
        $this->authorize('viewAny', Agency::class);
        $result = $this->agencyService->filter($filter);
        return new AgencySearchResource($result);
    }

    public function store(StoreAgencyRequest $request)
    {
        $this->agencyService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.agencies.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateAgencyRequest $request, Agency $agency)
    {
        $this->agencyService->update($request->validated(), $agency);
        return response()->json(['message' => Lang::get('messages.agencies.success.updated')], Response::HTTP_OK);
    }

    public function block(Agency $agency)
    {
        $this->authorize('block', Agency::class);
        $this->agencyService->blockByAdmin($agency);
        return response()->json(['message' => Lang::get('messages.agencies.success.block')], Response::HTTP_OK);
    }

    public function unlock(Agency $agency)
    {
        $this->authorize('unlock', Agency::class);
        $this->agencyService->unBlock($agency);
        return response()->json(['message' => Lang::get('messages.agencies.success.unlock')], Response::HTTP_OK);
    }

    public function list(AgencyFilter $filter)
    {
        $result = $this->agencyService->list($filter);
        return CustomAgencyResource::collection($result);
    }

    public function destroy(Agency $agency)
    {
        $this->authorize('delete', $agency);
        $this->agencyService->delete($agency);
        return response()->json(['message' => Lang::get('messages.agencies.success.deleted')], Response::HTTP_OK);
    }
}
