<?php

namespace App\Jobs\Requests;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\ResultService;
use App\Enums\LabTestStatus;
use App\Enums\ResultStatus;

class UpdateRequestToResultReceivedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $patient;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patient)
    {
        $this->patient = $patient;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patient->refresh();
        $request = $this->patient->lastRequest;

        $patientLabTests = $request->patientLabTests()
            ->where('status', LabTestStatus::SAMPLE_SHIPPED)->get();
        foreach ($patientLabTests as $patientLabTest) {
            app(ResultService::class)->changeStatus(['status' => ResultStatus::RECEIVED], $patientLabTest);
        }
    }
}
