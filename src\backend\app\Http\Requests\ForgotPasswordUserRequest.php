<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class ForgotPasswordUserRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'username' => 'required|exists:users,username,deleted_at,NULL|max:50',
            'otp_code' => 'required|digits:4',
        ];
    }

    public function messages()
    {
        return [
            'username.required' => Lang::get('validation.custom.login.username.required'),
            'username.exists' => Lang::get('validation.custom.login.username.exists'),
            'username.max' => Lang::get('validation.custom.login.username.max'),
            'otp_code.required'  => Lang::get('validation.custom.login.otp_code.required'),
            'otp_code.digits'  => Lang::get('validation.custom.login.otp_code.digits'),
        ];
    }
}
