<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Enums\SamplingReservationStatus;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Traits\MessageTrait;
use App\Enums\MessageType;
use Carbon\Carbon;
use Lang;

class SendSamplingReservationIsTodayToPatientJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        NotificationTrait, MessageTrait;

    protected $samplingReservation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($samplingReservation)
    {
        $this->samplingReservation = $samplingReservation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->samplingReservation->refresh();
        if ($this->samplingReservation->isStatus(SamplingReservationStatus::NEW) 
            && $this->samplingReservation->date->isToday()
        ) {
            $patient = $this->samplingReservation->patient;
            $patientLabTest = $this->samplingReservation->patientLabTest;
            $message = Lang::get('messages.sampling-reservations.patient_today_reminder', [], 'ar');
            $notification = $this->notificationObject(
                NotificationType::PATIENT_LAB_TEST, $patientLabTest->id, null, RequestStatus::SAMPLING_RESERVATION
            );
            $this->createMessage($patient, $message, MessageType::BOTH, $notification);     
        }
    }
}
