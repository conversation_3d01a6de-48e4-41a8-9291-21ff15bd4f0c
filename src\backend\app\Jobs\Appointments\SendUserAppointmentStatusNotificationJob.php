<?php

namespace App\Jobs\Appointments;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Jobs\SendNotificationJob;
use App\Traits\NotificationTrait;
use App\Services\RequestService;
use App\Enums\AppointmentReason;
use App\Enums\AppointmentStatus;
use App\Enums\NotificationType;
use App\Services\UserService;
use App\Enums\RequestStatus;
use App\Enums\GenderType;
use App\Enums\Role;
use Lang;

class SendUserAppointmentStatusNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $patient, $status, $reason;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patient, $status, $reason)
    {
        $this->patient = $patient;
        $this->status = $status;
        $this->reason = $reason;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $patientNameAr = $this->patient->translate('ar')->name;
        $patientNameEn = $this->patient->translate('en')->name;
        $users = app(UserService::class)->users([Role::FOLLOW_UP]);
        $reasonKey = $this->getReasonKey();
        $gender = $this->patient->gender_type == GenderType::FEMALE? 'her' : 'him';

        $bodyAr = Lang::get(
            'messages.appointments.auto_tasks.'.$reasonKey,
            ['name' => $patientNameAr, 'gender' => $gender, 'reason' => $this->reason->reason],
            'ar'
        );
        $bodyEn = Lang::get(
            'messages.appointments.auto_tasks.'.$reasonKey,
            ['name' => $patientNameEn, 'gender' => $gender, 'reason' => $this->reason->reason],
            'en'
        );

        $notificationData = $this->notificationData(
            $this->patient->id,
            NotificationType::PATIENT,
            $bodyAr,
            $bodyEn,
            array()
        );
        SendNotificationJob::dispatch($users, $notificationData);
    }

    public function getReasonKey()
    {
        switch (true) {
        case in_array($this->reason->id, AppointmentReason::getValues()):
            return AppointmentReason::getKey((int) $this->reason->id);
            break;
        case $this->status == AppointmentStatus::CANCELED:
            return 'CANCELLATION_NEW_REASON';
            break;
        case $this->status == AppointmentStatus::CONFIRMED:
            return 'ON_HOLD_NEW_REASON';
            break;
        }
    }
}
