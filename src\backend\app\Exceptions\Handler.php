<?php

namespace App\Exceptions;

use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Symfony\Component\HttpKernel\Exception\AccessDeniedHttpException;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Illuminate\Http\Exceptions\PostTooLargeException;
use Illuminate\Validation\ValidationException;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Http\Response;
use Throwable;
use Lang;

class Handler extends ExceptionHandler
{
    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(
            function (Throwable $e) {
                if (app()->bound('sentry') && $this->shouldReport($e)) {
                    app('sentry')->captureException($e);
                }
            }
        );

        $this->renderable(
            function (NotFoundHttpException $exception, $request) {
                return response()->json(['message' => Lang::get('messages.not_found')], Response::HTTP_NOT_FOUND);  
            }
        );
        $this->renderable(
            function (AuthenticationException $exception, $request) {
                return response()->json(
                    ['message' => Lang::get('messages.unauthorized')], Response::HTTP_UNAUTHORIZED
                );  
            }
        );
        $this->renderable(
            function (ValidationException $exception, $request) {
                if ($request->expectsJson()) {
                    $errors = array_values($exception->errors());
                    return response()->json(
                        ['message' => $errors[0][0]?? Lang::get('messages.validation_error'),
                        'errors' => $exception->errors()], Response::HTTP_UNPROCESSABLE_ENTITY
                    );
                }
            }
        );
        $this->renderable(
            function (AccessDeniedHttpException $exception, $request) {
                return response()->json(
                    ['message' => Lang::get('messages.access_denied')], Response::HTTP_FORBIDDEN
                );  
            }
        );
        $this->renderable(
            function (PostTooLargeException $exception, $request) {
                return response()->json(
                    ['message' => Lang::get(
                        'validation.custom.attachments.attachment.max', ['max' => config('app.profile_max_size')], 'ar'
                    )], Response::HTTP_REQUEST_ENTITY_TOO_LARGE
                );  
            }
        );
        $this->renderable(
            function (HttpException $exception, $request) {
                return response()->json(
                    ['message' => $exception->getMessage()], $exception->getStatusCode()
                );  
            }
        );
    }
}
