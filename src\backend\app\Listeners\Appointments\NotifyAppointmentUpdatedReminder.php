<?php

namespace App\Listeners\Appointments;

use App\Jobs\SendAppointmentReminderToInternalUserJob;
use App\Jobs\SendAppointmentReminderToPatientJob;
use App\Events\Appointments\AppointmentUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\ReminderService;
use App\Enums\ReminderType;
use App\Enums\UserType;

class NotifyAppointmentUpdatedReminder
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Appointments\AppointmentUpdated $event
     * @return void
     */
    public function handle(AppointmentUpdated $event)
    {
        $appointment = $event->updatedAppointment;
        $reminders = app(ReminderService::class)->list(ReminderType::APPOINTMENT);
        foreach ($reminders as $reminder) {
            $reminderTime = $appointment->start_date->subMinutes($reminder->duration);
            $reminder->user_type == UserType::INTERNAL_USER?
                SendAppointmentReminderToInternalUserJob::dispatch($appointment, $reminder->duration)
                ->onQueue(config('queue.queues.notifications'))->delay($reminderTime)
                : SendAppointmentReminderToPatientJob::dispatch($appointment, $reminder->duration)->delay($reminderTime)
                ->onQueue(config('queue.queues.notifications'));
        }
    }
}
