<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Traits\AppointmentTrait;
use App\Enums\AppointmentStatus;
use App\Models\PatientLabTest;
use App\Enums\RequestStatus;
use App\Enums\PaymentStatus;
use App\Enums\LabTestStatus;
use App\Enums\RejectReason;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class PatientLastNotificationResource extends JsonResource
{
    use AppointmentTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $this->approvedList = $this->patientLabTests()->approved()->with('payment')->get();
        $patientLabTest = $this->getPatientLabTest();
        $paymentStatus = $this->isStatus(RequestStatus::OBLIGATION_CONFIRMED)?
            $this->getPatientLabTestPaymentStatus() 
            : ($patientLabTest->payment->status?? PaymentStatus::NOT_PAID);

        return array(
            'id' => $this->id,
            'request_status' => $this->request_status,
            'message' => $this->message(),
            'is_notified' => $this->is_notified,
            'appointment_type' => $this->isStatus(RequestStatus::INTERVIEW)? $this->getAppointmentType() : null,
            'join_url' => $this->isStatus(RequestStatus::INTERVIEW)? $this->getJoinURL() : null,
            'appointment_reason_id' => $this->getAppointmentReasonId(),
            'patient_lab_test' =>
                array(
                    'id' => $patientLabTest->id?? null,
                    'payment_status' => $paymentStatus,
                    'status' =>$patientLabTest->status?? null,
                    'name' => $patientLabTest? ($patientLabTest->name?? $patientLabTest->labTest->name) : null,
                ),
        );
    }

    public function message()
    {
        switch($this->request_status) {
        case RequestStatus::REJECTED:
            return $this->rejectMessage();
                break;
        case RequestStatus::CANCELED:
            return $this->cancelMessage();
                break;
        case RequestStatus::NOT_INTERVIEWED:
            return $this->noInterviewMessage();
                break;
        case RequestStatus::OBLIGATION_NOT_SENT:
            return $this->confirmedAppointmentMessage($this->lastAppointment);
                break;
        case RequestStatus::OBLIGATION_CONFIRMED:
            return $this->confirmedObligationMessage();
                break;
        case RequestStatus::SAMPLING_RESERVATION:
            return $this->samplingReservationMessage();
                break;
        case RequestStatus::SAMPLE_DEPOSITED:
            return $this->sampleDepositedMessage();
                break;
        case RequestStatus::SAMPLE_SHIPPED:
            return $this->shipmentMessage();
                break;
        case RequestStatus::SAMPLE_NOT_SHIPPED:
            return $this->shipmentMessage();
                break;
        case RequestStatus::RESULT_RECEIVED:
            return $this->shipmentMessage();
                break;    
        case RequestStatus::RESULT_NOT_RECEIVED:
            return $this->shipmentMessage();
                break;
        default:
            return $this->statusMessage();
                break;
        }
    }

    public function statusMessage()
    {
        $user = $this->lastAppointment->user?? null;
        $appointment = $this->lastAppointment;
        $name = $user? $user->translate('ar')->name : Lang::get('translations.user');
        $position = Role::getDescription($user->role_id?? Role::SPECIALIST);
        $purpose = $appointment? $appointment->purposeName('ar') : '';
        $type = $appointment? $appointment->typeName('ar') : '';

        return Lang::get(
            'messages.request_statuses.'. RequestStatus::getKey($this->request_status),
            ['name' => $name, 'purpose' => $purpose, 'type' => $type, 'position' => $position]
        );
    }

    public function getJoinURL()
    {
        return $this->lastAppointment->onlineMeeting->join_url?? null;
    }

    public function getAppointmentType()
    {
        return $this->lastAppointment->type?? null;
    }

    public function rejectMessage()
    {
        $historyWithReason = $this->requestHistories()->latest()->first();
        $reason = $historyWithReason->rejectionReason;
        $isAddedBySystem = in_array($reason->id, RejectReason::getValues());
        return $isAddedBySystem? 
            Lang::get('translations.request_rejections.messages.' .RejectReason::getKey($reason->id))
            : Lang::get($reason->message);
    }

    public function cancelMessage()
    {
        return Lang::get('messages.request_statuses.CANCELED', [], 'ar');
    }
    
    public function noInterviewMessage()
    {
        $appointment = $this->lastAppointment;
        $position = Role::getDescription($appointment->user->role_id?? Role::SPECIALIST);
        if ($appointment->isStatus(AppointmentStatus::MISSED)) {
            return Lang::get(
                'messages.request_statuses.NOT_INTERVIEWED', ['position' => $position]
            );
        }
        return $this->canceledAppointmentMessage($this->lastAppointment);
    }

    public function getAppointmentReasonId()
    {
        $appointmentStage = in_array($this->request_status, [RequestStatus::NOT_INTERVIEWED, RequestStatus::OBLIGATION_NOT_SENT]);
        if (!$appointmentStage) {
            return null;
        }
        $history = $this->appointmentHistoryByStatus($this->lastAppointment, [AppointmentStatus::CONFIRMED, AppointmentStatus::CANCELED]);
        return $history->appointment_reason_id?? null;
    }

    public function getPatientLabTest()
    {
        return $this->patient_lab_tests_count == 1? 
        $this->approvedList->first(): null;
    }

    public function getPatientLabTestPaymentStatus()
    {
        $hasPendingPayment = $this->hasPendingPayment();

        return $hasPendingPayment? PaymentStatus::PENDING : PaymentStatus::NOT_PAID;
    }

    public function confirmedObligationMessage()
    {
        $hasPendingPayment = $this->hasPendingPayment();
        $rejectedPayment = $this->rejectedPayment();
        $lastPaymentHistory = $rejectedPayment->lastPaymentHistory?? null;
        $paymentReason = $lastPaymentHistory->paymentReason?? null;

        return $hasPendingPayment? 
            Lang::get('messages.request_statuses.OBLIGATION_CONFIRMED_WITH_PENDING_PAYMENT')
            : ($paymentReason? 
                Lang::get('messages.request_statuses.OBLIGATION_CONFIRMED_WITH_REJECTED_PAYMENT', ['reason' => $paymentReason->reason])
                : Lang::get('messages.request_statuses.OBLIGATION_CONFIRMED'));
    }

    public function hasPendingPayment()
    {
        $patientLabTest = $this->approvedList->first(
            function ($patientLabTest) {
                return $patientLabTest->payment->status == PaymentStatus::PENDING;
            }
        );

        return $patientLabTest? true : false;
    }

    public function rejectedPayment()
    {
        $patientLabTest = $this->approvedList->first(
            function ($patientLabTest) {
                return $patientLabTest->payment->status == PaymentStatus::REJECTED;
            }
        );
        return $patientLabTest? $patientLabTest->payment : null;
    }

    public function samplingReservationMessage()
    {
        $patientLabTest = $this->patientLabTests()->where('status', LabTestStatus::SAMPLING_RESERVATION)->first();
        $samplingReservation = $patientLabTest->samplingReservation;
        $reservationDate = Carbon::parse($samplingReservation->date_converted);
        $testName = $patientLabTest->labTest->name;
        $date = $reservationDate->format('Y-m-d');
        $time = $reservationDate->format('g:i a');
        $agencyName = $samplingReservation->agency->name;

        return Lang::get(
            'messages.request_statuses.'. RequestStatus::getKey($this->request_status),
            ['testName' => $testName, 'date' => $date, 'time' => $time, 'agencyName' => $agencyName]
        );
    }

    public function sampleDepositedMessage()
    {
        $patientLabTest = $this->patientLabTests()->where('status', LabTestStatus::SAMPLE_DEPOSITED)->first();
        $days = $patientLabTest->receive_result_after;
        return Lang::get(
            'messages.request_statuses.'. RequestStatus::getKey($this->request_status), ['days' => $days]
        );
    }

    public function shipmentMessage()
    {
        $patientLabTest = $this->patientLabTests()->whereIn('status', PatientLabTest::$sampleDepositeStatus)->first();
        $days = $patientLabTest->receive_result_after;
        return Lang::get(
            'messages.request_statuses.'. RequestStatus::getKey($this->request_status), ['days' => $days]
        );
    }
}
