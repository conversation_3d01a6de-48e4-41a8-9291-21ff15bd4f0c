<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Services\PaymentService;
use App\Enums\NotificationType;
use App\Services\UserService;
use App\Enums\Role;

class NotifyPaymentUpdatesJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $payment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->payment->refresh();
        $users = app(UserService::class)->users([Role::FOLLOW_UP, Role::ADMIN, Role::ACCOUNTANT])
            ->loadCount('unreadNotifications');
        $paymentsStatistics = (Array) app(PaymentService::class)->statistics();
        $notificationData = $this->notificationData(
            null,
            NotificationType::NOTIFICATION,
            '',
            '',
            array('payments_statistics' => $paymentsStatistics)
        );
        NotifyUpdatedJob::dispatch($users, $notificationData);
    }
}
