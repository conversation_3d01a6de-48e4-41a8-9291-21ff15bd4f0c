<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\PatientLabTest;
use App\Enums\LabTestStatus;
use Lang;

class PatientLabTestFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'patient_id' => $this->patient_id,
            'patient_name' => $this->patient->name,
            'send_date' => $this->send_date_converted,
            'lab_test_id' => $this->lab_test_id,
            'lab_test_name' => $this->name?? $this->labTest->name, 
            'lab_test_price' => $this->payment->price?? $this->labTest->price_after_discount,
            'status' => $this->status,
            'status_name' => $this->getStatus(),
            'request_status' => $this->request->status(),
            'request_stage' => $this->request->stage,
        );
    }

    public function getStatus()
    {
        switch (true) {
        case in_array($this->status, PatientLabTest::$approvedStatuses):
            return Lang::get('translations.patient_lab_test_status.approved');
                    break;
        case $this->isNotApproved():
            return Lang::get('translations.patient_lab_test_status.not_approved');
                    break;
        case $this->isNotApprovedWithDelay():
            return Lang::get('translations.patient_lab_test_status.lated');
                    break;
        default:
            return LabTestStatus::getDescription($this->status);
                    break;
        }
    }
}
