<?php

namespace App\Http\Resources\Notes;

use Illuminate\Http\Resources\Json\ResourceCollection;
use App\Http\Resources\CollectionResource;

class NoteCollection extends ResourceCollection
{
    use CollectionResource;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'current_page' => $this->currentPage(),
            'data' => $this->collection,
            'total' => $this->total(),
            'last_page' => $this->lastPage(),
            'per_page' => $this->perPage()
        );
    }
}
