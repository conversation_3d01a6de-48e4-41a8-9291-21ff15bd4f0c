<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static REQUEST()
 * @method static static NOTIFICATION()
 * @method static static TASK()
 */
final class NotificationType extends Enum
{
    const REQUEST =        'request';
    const NOTIFICATION =   'notification';
    const TASK =           'task';
    const APPOINTMENT =    'appointment';
    const PATIENT =        'patient';
    const PAYMENT =        'payment';
    const PATIENT_LAB_TEST =  'patientLabTest';
    const SAMPLING_RESERVATION =   'samplingReservation';
}
