<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class ChangePasswordRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'password' => 'required|min:8|regex:'. config('app.password_regex'),
            'otp_code' => 'required|digits:4',
        ];
    }

    public function messages()
    {
        return [
            'password.required'  => Lang::get('validation.custom.reset-password.password.required'),
            'password.regex'  => Lang::get('validation.custom.reset-password.password.regex'),
            'password.min'  => Lang::get('validation.custom.reset-password.password.min'),
            'otp_code.required'  => Lang::get('validation.custom.login.otp_code.required'),
            'otp_code.digits'  => Lang::get('validation.custom.login.otp_code.digits'),
        ];
    }
}
