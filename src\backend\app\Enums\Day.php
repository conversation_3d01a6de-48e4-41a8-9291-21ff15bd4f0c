<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static SUNDAY()
 * @method static static MONDAY()
 * @method static static TUESDAY()
 * @method static static WEDNESDAY()
 * @method static static THURSDAY()
 * @method static static FRIDAY()
* @method static static SATURDAY()
 */
final class Day extends Enum implements LocalizedEnum
{
    const SUNDAY =     1;
    const MONDAY =     2;
    const TUESDAY =    3;
    const WEDNESDAY =  4;
    const THURSDAY =   5;
    const FRIDAY =     6;
    const SATURDAY =   7;
}
