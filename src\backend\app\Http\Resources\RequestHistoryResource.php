<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\RequestStatus;
use Lang;

class RequestHistoryResource extends JsonResource
{
    protected static $patientName;

    public static function patientName($patientName)
    {
        static::$patientName = $patientName;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'request_status' =>  RequestStatus::getDescription($this->request_status),
            'reject_reason_title' => $this->when($this->rejectionReason, Lang::get($this->rejectionReason->title?? '')),
            'reject_reason_message' => $this->when($this->rejectionReason, Lang::get($this->rejectionReason->message?? '', ['name' => static::$patientName, 'app_url' => config('app.patient_app_url')])),
            'cancel_reason_title' => $this->when($this->cancellationReason, Lang::get($this->cancellationReason->title?? '')),
            'cancel_reason_message' => $this->when($this->cancellationReason, $this->cancellation_note),
            'created_at' => $this->created_at_converted,
        );
    } 
}
