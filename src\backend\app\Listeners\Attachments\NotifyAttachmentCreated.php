<?php

namespace App\Listeners\Attachments;

use App\Jobs\Attachments\SendAttachmentIsUploadedNotificationJob;
use App\Jobs\Attachments\SendUserUploadAttachmentEmailJob;
use App\Jobs\Requests\UpdateRequestToResultReceivedJob;
use App\Jobs\Attachments\UploadAttachmentAutoTaskJob;
use App\Events\Attachments\AttachmentCreated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Enums\AttachmentPurpose;

class NotifyAttachmentCreated
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Attachments\AttachmentCreated $event
     * @return void
     */
    public function handle(AttachmentCreated $event)
    {
        $patient = $event->attachment->attachmentable;
        $event->attachment->purpose == AttachmentPurpose::RESULT?
            UpdateRequestToResultReceivedJob::dispatch($patient)
            ->onQueue(config('queue.queues.requests')) : null;
        UploadAttachmentAutoTaskJob::dispatch($patient, $event->attachment->creatable)
            ->onQueue(config('queue.queues.tasks'));
        SendUserUploadAttachmentEmailJob::dispatch($patient, $event->attachment->creatable)
            ->onQueue(config('queue.queues.notifications'));
        SendAttachmentIsUploadedNotificationJob::dispatch($patient, $event->attachment->creatable)
            ->onQueue(config('queue.queues.notifications'));
    }
}
