<?php

namespace App\Jobs\Appointments;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\UserService;
use App\Services\AppointmentService;
use App\Enums\AppointmentStatus;
use App\Enums\RequestStatus;
use App\Traits\MailTrait;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class MissedAppointmentReminderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $appointment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->appointment->refresh();
        if (!$this->appointment->trashed() 
            && $this->appointment->isStatus(AppointmentStatus::NEW)
            && $this->hasTimePassed()
        ) {
            $this->notifyUser();
            $this->notifyCoordinators();
        }
    }

    public function hasTimePassed()
    {
        return $this->appointment->start_date->lte(Carbon::now());
    }
    
    public function notifyUser()
    {
        $user = $this->appointment->user;
        $bodyAr = $this->notificationMessage('ar', 'user');
        $bodyEn =  $this->notificationMessage('en', 'user');
        $this->sendMissedAppointmentReminderMail($user, null, false);
        app(AppointmentService::class)->notifyNotification($this->appointment->patient_id, $user, $bodyAr, $bodyEn);
    }

    public function notifyCoordinators()
    {
        $users = app(UserService::class)->users([Role::FOLLOW_UP]);
        $bodyAr = $this->notificationMessage('ar', 'admin');
        $bodyEn =  $this->notificationMessage('en', 'admin');
        foreach ($users as $user) {
            $this->sendMissedAppointmentReminderMail($user, $this->appointment->user, true);
            app(AppointmentService::class)->notifyNotification($this->appointment->patient_id, $user, $bodyAr, $bodyEn);
        }
    }

    public function notificationMessage($lang, $type)
    {
        $user = $this->appointment->user;
        $userName = $user->translate($lang)->name?? Lang::get('translations.user');
        $position = Lang::get(
            'translations.roles.'.strtolower(Role::getKey($this->appointment->user->role_id?? Role::SPECIALIST)),
            [],
            $lang
        );

        return Lang::get(
            'translations.mails.body.'.$type.'_missed_appointment_reminder',
            ['name' => $userName, 'position' => $position],
            $lang
        );
    }
}
