<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\AppointmentService;
use App\Enums\AppointmentPurpose;
use App\Enums\AppointmentType;
use App\Traits\MailTrait;
use Lang;

class SendChangeAppointmentUserNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $user, $patient, $date, $hour, $type, $purpose, $appointmentId;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($user, $patient, $date, $hour, $type, $purpose, $appointmentId)
    {
        $this->user = $user;
        $this->patient = $patient;
        $this->date = $date;
        $this->hour = $hour;
        $this->type = $type;
        $this->purpose = $purpose;
        $this->appointmentId = $appointmentId;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->notifyUser();
        $this->sendChangeAppointmentUserMail(
            $this->user, $this->patient, $this->date, $this->hour, $this->type, $this->purpose
        );
    }

    public function notifyUser()
    {
        $bodyAr = $this->userNotification('ar');
        $bodyEn = $this->userNotification('en');

        app(AppointmentService::class)
            ->notifyNotification($this->patient->id, $this->user, $bodyAr, $bodyEn);
    }

    public function userNotification($lang)
    {
        $patientName = $this->patient->translate($lang)->name;
        $purpose = Lang::get(
            'translations.appointment_purposes.'.AppointmentPurpose::getKey((int) $this->purpose), [], $lang
        );
        $type = Lang::get(
            'translations.appointment_types.'.AppointmentType::getKey((int) $this->type), [], $lang
        );

        return Lang::get(
            'translations.mails.body.delete_appointment',
            ['purpose' => $purpose, 'type' => $type ,'name' => $patientName,
            'hour' => $this->hour, 'date' => $this->date],
            $lang
        );
    }
}
