<?php

namespace App\Http\Resources\ShippingLocations;

use Illuminate\Http\Resources\Json\JsonResource;
use Lang;

class ShippingLocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'location' => Lang::get($this->location),
        );
    }
}
