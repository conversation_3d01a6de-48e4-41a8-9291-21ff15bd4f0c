<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\Agency;

class AgencyFilter extends Filter
{
    public $fields = array('code', 'blocked_at', 'created_at', 'updated_at', 'city_id');

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->where(
            function ($query) use ($search) {
                $query->where('code', 'like', '%'.$search.'%')
                    ->orWhereHas(
                        'agencyTranslations', function (Builder $query) use ($search) {
                            $query->where('name', 'like', '%'.$search.'%');
                        }
                    );
            }
        );
    }

    /**
     * @param string $roleId
     */
    public function roleId(string $roleId)
    {
        $this->builder->where('role_id', $roleId);
    }

    /**
     * @param string $cityId
     */
    public function cityId(string $cityId)
    {
        $this->builder->where('city_id', $cityId);
    }

    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }

    /**
     * Order the services by the given order and field.
     *
     * @param array $value
     */
    public function orderBy(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $value = isset($sortData[1])? $sortData[1] : null;
        $this->builder->orderByRaw("FIELD($field , $value) DESC");

    }
    
}