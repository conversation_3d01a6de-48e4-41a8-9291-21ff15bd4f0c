<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Traits\MessageTrait;
use App\Enums\RequestStatus;
use App\Enums\MessageType;
use Lang;

class SendPatientRequestAcceptedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait,
        MessageTrait;

    protected $request;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        $createdByUser = $this->request->created_by_user;
        if ($this->request->isStatus(RequestStatus::CONFIRMED) && !$createdByUser) {
            $patient = $this->request->requestable;
            $message = Lang::get('messages.messages.accept_request', ['name' => $patient->name], 'ar');
            $notification = $this->notificationObject(
                NotificationType::REQUEST, $this->request->id, null, RequestStatus::CONFIRMED
            );
            $this->createMessage($patient, $message, MessageType::BOTH, $notification);
        }
    }
}
