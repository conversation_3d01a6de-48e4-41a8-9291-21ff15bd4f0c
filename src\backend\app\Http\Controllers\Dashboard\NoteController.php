<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Requests\Notes\StoreNoteRequest;
use App\Http\Resources\Notes\NoteCollection;
use App\Http\Controllers\Controller;
use App\Http\Filters\NoteFilter;
use App\Services\NoteService;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Models\Patient;
use Lang;

class NoteController extends Controller
{
    private $service;

    public function __construct(NoteService $service)
    {
        $this->service = $service;
    }

    public function store(StoreNoteRequest $request, Patient $patient)
    {
        $this->service->create($request->validated(), $patient);
        return response()->json(['message' => Lang::get('messages.notes.success.created')], Response::HTTP_OK);
    }

    public function index(NoteFilter $filter, Patient $patient)
    {
        $result = $this->service->filter($filter, $patient);
        return new NoteCollection($result);
    }
}
