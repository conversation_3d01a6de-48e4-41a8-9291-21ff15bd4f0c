<?php

namespace App\Http\Controllers\Patient;

use App\Http\Requests\InsuranceLetters\StoreInsuranceLetterRequest;
use App\Http\Resources\InsuranceLetters\InsuranceLetterCollection;
use App\Http\Filters\InsuranceLetterFilter;
use App\Services\InsuranceLetterService;
use App\Http\Controllers\Controller;
use App\Models\InsuranceLetter;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class InsuranceLetterController extends Controller
{
    private $service;

    public function __construct(InsuranceLetterService $service)
    {
        $this->service = $service;
    }

    public function index(InsuranceLetterFilter $filter)
    {
        $result = $this->service->filter($filter);
        return new InsuranceLetterCollection($result);
    }

    public function store(StoreInsuranceLetterRequest $request)
    {
        $this->service->create($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.insurance-letters.success.created')],
            Response::HTTP_OK
        );
    }

    public function destroy(InsuranceLetter $insuranceLetter)
    {
        $this->authorize('delete', [InsuranceLetter::class, $insuranceLetter]);
        $this->service->delete($insuranceLetter);
        return response()->json(
            ['message' => Lang::get(
                'messages.insurance-letters.success.deleted'
            )], Response::HTTP_OK
        );
    }
}
