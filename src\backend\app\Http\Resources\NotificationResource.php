<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Traits\TimeZoneConverterTrait;

class NotificationResource extends JsonResource
{
    use TimeZoneConverterTrait;
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'data' => $this->data,
            'created_at' => $this->getCreatedAtConvertedAttribute()
        );
    }
}
