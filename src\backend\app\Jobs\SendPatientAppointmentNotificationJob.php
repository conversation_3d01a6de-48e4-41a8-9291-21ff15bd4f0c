<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Traits\MessageTrait;
use App\Enums\NotificationType;
use App\Enums\MessageType;
use App\Enums\Role;
use App;
use Lang;

class SendPatientAppointmentNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MessageTrait, NotificationTrait;

    protected $appointment, $action;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment, $action)
    {
        $this->appointment = $appointment;
        $this->action = $action;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        App::setlocale('ar');
        $message = $this->smsMessage();
        $patient = $this->appointment->patient;
        $requestStatus = $this->appointment->request->request_status;
        $notification = $this->notificationObject(
            NotificationType::APPOINTMENT, $this->appointment->id, null, $requestStatus
        );
        $this->createMessage($patient, $message, MessageType::BOTH, $notification);
    }

    public function smsMessage()
    {
        $user = $this->appointment->user;
        $patient = $this->appointment->patient;
        $date = $this->appointment->patient_date;
        $hour = $this->appointment->patient_hour;
        $onlineMeeting = $this->appointment->onlineMeeting;
        $patientName = $patient->first_name?? $patient->name;
        $name = $user? $user->translate('ar')->name : Lang::get('translations.user');
        $position = Lang::get(
            'translations.roles.'.strtolower(Role::getKey($user->role_id?? Role::SPECIALIST)), [], 'ar'
        );
        $meetingLink = $onlineMeeting->join_url?? null;
        $text = $this->appointment->isVideo()? '_appointment_with_online_meeting' : '_appointment';
        $shortenLink = $onlineMeeting->short_join_url?? null;

        return Lang::get(
            'messages.messages.'.$this->action.$text,
            ['patient_name' => $patientName, 'date' => $date, 'hour' => $hour, 'name' => $name,
            'purpose' => $this->appointment->purposeName('ar'), 'type' => $this->appointment->typeName('ar'),
            'link' => $shortenLink?? $meetingLink, 'position' => $position],
            'ar'
        );
    }
}
