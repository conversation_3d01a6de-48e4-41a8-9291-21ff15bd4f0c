<?php

namespace App\Listeners\Attachments;

use App\Jobs\Requests\UpdateRequestToResultReceivedJob;
use App\Events\Attachments\AttachmentUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Enums\AttachmentPurpose;

class NotifyAttachmentUpdated
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Attachments\AttachmentUpdated $event
     * @return void
     */
    public function handle(AttachmentUpdated $event)
    {
        $patient = $event->attachment->attachmentable;
        $event->attachment->purpose == AttachmentPurpose::RESULT?
            UpdateRequestToResultReceivedJob::dispatch($patient)
            ->onQueue(config('queue.queues.requests')) : null;
    }
}
