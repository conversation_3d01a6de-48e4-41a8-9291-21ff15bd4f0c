<?php

namespace App\Http\Requests\ReferenceLabs;

use Illuminate\Foundation\Http\FormRequest;
use Auth;
use Lang;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('update', ReferenceLab::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'code' => 'nullable|regex:/^[a-zA-Z0-9-]*$/|unique:reference_labs,code,'
                .$this->reference_lab->id.',id,deleted_at,NULL',
            'code.regex' => Lang::get('validation.custom.reference_labs.code.regex'),
            'name.ar' => 'sometimes',
            'name.en' => 'required',
            'map_link' => 'nullable|url',
            'country_id' => 'sometimes|exists:countries,id',
            'city_name' => 'sometimes',
            'user_name.ar' => 'sometimes',
            'user_name.en' => 'sometimes',
            'user_position' => 'sometimes',
            'phone_number' => 'nullable|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA',
            'email' => 'nullable|email:rfc,dns',
        ];
    }

    public function messages()
    {
        return [
            'code.unique' => Lang::get('validation.custom.reference_labs.code.unique'),
            'code.regex' => Lang::get('validation.custom.reference_labs.code.regex'),
            'name.en.required' => Lang::get('validation.custom.reference_labs.name.en.required'),
            'map_link.url' => Lang::get('validation.custom.reference_labs.map_link.url'),
            'country_id.exists' => Lang::get('validation.custom.reference_labs.country_id.exists'),
            'phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.invalid'),
            'phone_number.unique' => Lang::get('validation.custom.patient-register.phone_number.unique'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
            'email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'email.unique' => Lang::get('validation.custom.users.email.unique'),
        ];
    }
}
