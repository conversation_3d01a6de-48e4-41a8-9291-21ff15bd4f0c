<?php

namespace App\Http\Connectors;

use <PERSON><PERSON>it\Firebase\DynamicLink\CreateDynamicLink\FailedToCreateDynamicLink;
use <PERSON>reait\Firebase\DynamicLink\CreateDynamicLink;
use Kreait\Firebase\DynamicLink\AndroidInfo;
use Kreait\Firebase\DynamicLink\IOSInfo;
use Kreait\Firebase\Contract\DynamicLinks;
use Illuminate\Http\Response;
use Lang;
use Log;

class FirebaseDynamicLinkConnector
{
    public static function createShortLink(string $link)
    {
        try {
            $action = CreateDynamicLink::forUrl($link)
                ->withShortSuffix();
            return self::createDynamicLink($action);
        } catch (FailedToCreateDynamicLink $e) {
            self::log($e);
            return null;
        }
    }

    public static function createDeepLink(string $link)
    {
        try {
            $action = CreateDynamicLink::forUrl($link)
                ->withShortSuffix()
                ->withIOSInfo(
                    IOSInfo::new()
                        ->withBundleId(config('firebase.ios.bundle_id'))
                )
                ->withAndroidInfo(
                    AndroidInfo::new()
                        ->withPackageName(config('firebase.android.package_name'))
                );
            return self::createDynamicLink($action);
        } catch (FailedToCreateDynamicLink $e) {
            self::log($e);
            return null;
        }
    }

    public static function createDynamicLink($action)
    {
        $dynamicLinks = app('firebase.dynamic_links');          

        return $dynamicLinks->createDynamicLink($action);
    }

    public static function log($exception)
    {
        Log::channel('dynamic_link')->error($exception);
    }
}
