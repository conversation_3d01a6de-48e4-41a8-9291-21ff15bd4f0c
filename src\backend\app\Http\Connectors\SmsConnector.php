<?php

namespace App\Http\Connectors;

use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Response;
use GuzzleHttp\Client;
use Lang;
use Log;

class SmsConnector
{

    public static function sendRequest($method, $url, $data,$header)
    {
        $body = array();
        $body['headers'] = $header;
        $body['json'] = $data?? array();
        $body['http_errors'] = false;
        
        $client = new Client(['base_uri' => config('msegat.base_url')]);
        $response= $client->request($method, $url, $body);
        

        return  $response;
    }

    public static function sendOTP($numbers, $otpCode)
    {
        $header = SmsConnector::getHeaders();
        $message = Lang::get('messages.otp.message', ['otp' => $otpCode]);
        $body = SmsConnector::getBody($numbers, $message, config('msegat.message_sender'));

        return SmsConnector::sendSms($body, $header);
    }

    public static function sendMessage($numbers, $message)
    {
        $header = SmsConnector::getHeaders();
        $message = $message;
        $body = SmsConnector::getBody($numbers, $message, config('msegat.message_sender'));

        return SmsConnector::sendSms($body, $header);
    }

    public static function getResponse($response)
    {
        $jsonResponse = json_decode($response->getBody()->getContents(), true);

        if (!in_array($jsonResponse['code'], config('msegat.success_code'))) {
            abort(Response::HTTP_BAD_REQUEST, $jsonResponse['message']);
        }
    }

    public static function getHeaders()
    {
        return ['content-type' => 'application/json'];
    }

    private static function getBody(string $numbers,string $message,string $userSender)
    {
        return array(
            'userName' => config('msegat.user_name'),
            'apiKey' => config('msegat.api_key'),
            'numbers' => $numbers,
            'userSender' => $userSender,
            'msg' => $message,
            'msgEncoding' => config('msegat.msg_encoding')
        );
    }

    public static function sendSms($body, $header)
    {
        config('msegat.enable_sms')?
            SmsConnector::sendRequest('POST', config('msegat.send_sms'), $body, $header) : null;
    }
}
