<?php

namespace App\Http\Requests\Agencies;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\Agency;
use App\Enums\Day;
use Auth;
use Lang;

class UpdateAgencyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('update', Agency::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'username' => 'required|max:150|unique:agencies,username,'
                .$this->agency->id.',id,deleted_at,NULL',
            'role_id' => 'required|in:'. implode(',', Agency::agenciesRoles()),
            'city_id' => 'required|exists:cities,id,deleted_at,NULL',
            'phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA|
                unique:agencies,phone_number,'.$this->agency->id.',id,deleted_at,NULL',
            'email' => 'required|email:rfc,dns|unique:agencies,email,'.$this->agency->id.',id,deleted_at,NULL',
            'code' => 'nullable|regex:/^[a-zA-Z0-9-]*$/|unique:agencies,code,'.$this->agency->id.',id,deleted_at,NULL',
            'map_link' => 'required|url',
            'user_position' => 'required',
            'name.ar' => 'required|min:3|max:150|regex:/^(?!.*\d)[-_.\p{Arabic}\s]+$/iu|
                unique:agency_translations,name,'.$this->agency->id.',agency_id,deleted_at,NULL',
            'name.en' => 'required|min:3|max:150|regex:/^(?!.*\d)[a-zِِA-Z-_.\s]+$/iu|
                unique:agency_translations,name,'.$this->agency->id.',agency_id,deleted_at,NULL',
            'user_name.ar' => 'required|min:3|max:150|regex:/^(?!.*\d)[\p{Arabic}\s]+$/iu',
            'user_name.en' => 'required|min:3|max:150|regex:/^(?!.*\d)[a-zِِA-Z\s]+$/iu',
            'schedules' => 'required|array',
            'schedules.*.day' => 'required|distinct|in:'.implode(',', Day::getValues()),
            'schedules.*.from_hour' => 'required|date_format:H:i',
            'schedules.*.to_hour' => 'required|date_format:H:i',
        ];
    }

    public function messages()
    {
        return array_merge(
            $this->mainInformationMessages(),
            $this->extraInformationMessages(),
            $this->schedulesMessages(),
        );
    }

    public function mainInformationMessages()
    {
        return [
            'username.required' => Lang::get('validation.custom.agencies.username.required'),
            'username.min' => Lang::get('validation.custom.agencies.username.min', ['min' => 10]),
            'username.max' => Lang::get('validation.custom.agencies.username.max', ['max' => 150]),
            'username.unique' => Lang::get('validation.custom.agencies.username.unique'),
            'code.required' => Lang::get('validation.custom.agencies.code.required'),
            'code.unique' => Lang::get('validation.custom.agencies.code.unique'),
            'code.regex' => Lang::get('validation.custom.agencies.code.regex'),
            'city_id.required' => Lang::get('validation.custom.agencies.city_id.required'),
            'city_id.exists' => Lang::get('validation.custom.agencies.city_id.exists'),
            'role_id.required' => Lang::get('validation.custom.agencies.role_id.required'),
            'role_id.in' => Lang::get(
                'validation.custom.agencies.role_id.in', ['in' => implode(',', Agency::agenciesRoles())]
            ),
            'name.ar.required' => Lang::get('validation.custom.users.name.ar.required'),
            'name.ar.min' => Lang::get('validation.custom.users.name.ar.min', ['min' => 3]),
            'name.ar.max' => Lang::get('validation.custom.users.name.ar.max', ['max' => 150]),
            'name.ar.regex' => Lang::get('validation.custom.agencies.name.ar.regex'),
            'name.ar.unique' => Lang::get('validation.custom.agencies.name.ar.unique'),
            'name.en.required' => Lang::get('validation.custom.users.name.en.required'),
            'name.en.min' => Lang::get('validation.custom.users.name.en.min', ['min' => 3]),
            'name.en.max' => Lang::get('validation.custom.users.name.en.max', ['max' => 150]),
            'name.en.unique' => Lang::get('validation.custom.agencies.name.en.unique'),
            'name.en.regex' => Lang::get('validation.custom.agencies.name.en.regex'),
        ];
    }

    public function extraInformationMessages()
    {
        return [
            'phone_number.required' => Lang::get('validation.custom.requests.phone_number.required'),
            'phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.invalid'),
            'phone_number.unique' => Lang::get('validation.custom.patient-register.phone_number.unique'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
            'email.required' => Lang::get('validation.custom.users.email.required'),
            'email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'email.unique' => Lang::get('validation.custom.users.email.unique'),
            'map_link.url' => Lang::get('validation.custom.reference_labs.map_link.url'),
            'user_name.ar.required' => Lang::get('validation.custom.agencies.user_name.ar.required'),
            'user_name.ar.min' => Lang::get('validation.custom.agencies.user_name.ar.min', ['min' => 3]),
            'user_name.ar.max' => Lang::get('validation.custom.agencies.user_name.ar.max', ['max' => 150]),
            'user_name.ar.regex' => Lang::get('validation.custom.agencies.user_name.ar.regex'),
            'user_name.en.required' => Lang::get('validation.custom.agencies.user_name.en.required'),
            'user_name.en.min' => Lang::get('validation.custom.agencies.user_name.en.min', ['min' => 3]),
            'user_name.en.max' => Lang::get('validation.custom.agencies.user_name.en.max', ['max' => 150]),
            'user_name.en.regex' => Lang::get('validation.custom.agencies.user_name.en.regex'),
            'user_position.required' => Lang::get('validation.custom.agencies.user_position.required'),
            'map_link.required' => Lang::get('validation.custom.agencies.map_link.required'),
        ];
    }

    public function schedulesMessages()
    {
        return array(
            'schedules.required' => Lang::get('validation.custom.schedules.schedules.required'),
            'schedules.array' => Lang::get('validation.custom.schedules.schedules.array'),
            'schedules.*.day.required' => Lang::get('validation.custom.schedules.day.required'),
            'schedules.*.day.distinct' => Lang::get('validation.custom.schedules.day.distinct'),
            'schedules.*.day.in' => Lang::get(
                'validation.custom.schedules.day.in', ['in' => implode(',', Day::getValues())]
            ),
            'schedules.*.from_hour.required' => Lang::get('validation.custom.schedules.from_hour.required'),
            'schedules.*.from_hour.date_format' => Lang::get(
                'validation.custom.schedules.from_hour.date_format', ['format' => 'H:i']
            ),
            'schedules.*.to_hour.required' => Lang::get('validation.custom.schedules.to_hour.required'),
            'schedules.*.to_hour.date_format' => Lang::get(
                'validation.custom.schedules.to_hour.date_format', ['format' => 'H:i']
            ),
            'schedules.*.to_hour.after' => Lang::get('validation.custom.schedules.to_hour.after'),
        );
    }
}
