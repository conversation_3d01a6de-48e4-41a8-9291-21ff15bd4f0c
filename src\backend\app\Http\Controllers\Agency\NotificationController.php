<?php

namespace App\Http\Controllers\Agency;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\NotificationService;
use App\Models\Notification;
use App\Http\Resources\NotificationResource;
use Lang;

class NotificationController extends Controller
{
    private $notificationService;

    public function __construct(NotificationService $notificationService)
    {
        $this->notificationService = $notificationService;
    }

    public function notificationCount()
    {
        return $this->notificationService->notificationCount();
    }

    public function markAsRead(Notification $notification)
    {
        $this->notificationService->markAsRead($notification);
        return response()->json(['message' => Lang::get('messages.notifications.success.read')], Response::HTTP_OK);
    }

    public function unreaded()
    {
        $notifications = $this->notificationService->unreaded();
        return response()->json(
            ['notifications' => NotificationResource::collection($notifications)], Response::HTTP_OK
        );
    }

    public function readed()
    {
        $notifications = $this->notificationService->readed();
        return response()->json(
            ['notifications' => NotificationResource::collection($notifications)], Response::HTTP_OK
        );
    }
}