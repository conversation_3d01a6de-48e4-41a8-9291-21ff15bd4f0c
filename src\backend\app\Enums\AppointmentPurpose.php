<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static FIRST_APPOINTMENT()
 * @method static static INQUIRY()
 * @method static static RESULT_EXPLANATION()
 */
final class AppointmentPurpose extends Enum implements LocalizedEnum
{
    const FIRST_APPOINTMENT =      1;
    const INQUIRY =                2;
    const RESULT_EXPLANATION =     3;
    const COMPLEMENTARY =          4;
}
