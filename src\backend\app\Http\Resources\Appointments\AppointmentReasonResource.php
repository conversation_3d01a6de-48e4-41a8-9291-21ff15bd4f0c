<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\AppointmentReasonType;
use Lang;

class AppointmentReasonResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'reason' => Lang::get($this->reason),
            'type' => $this->type,
            'type_name' => AppointmentReasonType::getDescription($this->type),
        );
    }
}
