<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\PatientLabTestTrait;
use App\Enums\ShippingCompany;
use Illuminate\Bus\Queueable;
use App\Services\UserService;
use App\Services\TaskService;
use App\Enums\LabTestStatus;
use App\Enums\TaskStatus;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;
use App;

class ShippingAutoTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, PatientLabTestTrait;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        if ($this->patientLabTest->isStatus(LabTestStatus::SAMPLE_DEPOSITED)) {
            App::setlocale('ar');
            $coordinators = app(UserService::class)->users([Role::FOLLOW_UP]);
            $patientName = $this->patientLabTest->patient->name;
            $labTestShippingLocation = $this->patientLabTest->labTest->labTestShippingLocations()->first();
            $agency = $this->patientLabTest->samplingReservation->agency?? null;
            $referenceLab = $this->patientLabTest->labTest->referenceLab;
            $fromLocation = $this->getLocation($labTestShippingLocation->from, $agency, $referenceLab);
            $toLocation = $this->getLocation($labTestShippingLocation->to, $agency, $referenceLab);
            $shippingCompany = ShippingCompany::getDescription($labTestShippingLocation->shipping_company);
            $data = array(
                'description' => Lang::get(
                    'translations.mails.body.DEPOSITED',
                    ['name' => $patientName, 'from' => $fromLocation, 'to' => $toLocation, 'by' => $shippingCompany],
                    'ar'
                ),
                'complete_date' => Carbon::now()->addDay(),
            );
            foreach ($coordinators as $coordinator) {
                $task = app(TaskService::class)->createModel($data, $coordinator);  
                app(TaskService::class)->updateModel(['status' => TaskStatus::INPROGRESS], $task);
            }
        }        
    }
}
