<?php

namespace App\Http\Requests\Tasks;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use App\Services\TimeZoneConverter;
use App\Models\Task;
use App\Enums\TaskStatus;
use Carbon\Carbon;
use Auth;
use Lang;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('update', $this->task);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'complete_date' => 'required|date|date_format:Y-m-d H:i:s|after_or_equal:'.
                $this->minDate(),
            'description' => 'required',
            'reason' =>  Rule::when($this->reasonRequired(), ['required'])
        ];
    }

    public function messages()
    {
        return [
            'complete_date.required' => Lang::get('validation.custom.tasks.complete_date.required'),
            'complete_date.date' => Lang::get('validation.custom.tasks.complete_date.date'),
            'complete_date.date_format' => Lang::get(
                'validation.custom.tasks.complete_date.date_format', ['format' => 'Y-m-d H:i:s']
            ),
            'complete_date.after_or_equal' => Lang::get(
                'validation.custom.tasks.complete_date.after_or_equal', 
                ['date' => $this->minDate()]
            ),
            'description.required' => Lang::get('validation.custom.tasks.description.required'),
            'description.max' => Lang::get('validation.custom.tasks.description.max', ['max' => 1000]),
            'description.min'  => Lang::get('validation.custom.task.description.min', ['max' => 3]),
            'reason.required' => Lang::get('validation.custom.tasks.reason.required'),
        ];
    }

    public function minDate()
    {
        $completeDate = TimeZoneConverter::convertFromUtc($this->task->complete_date);
        $currentDate = TimeZoneConverter::convertFromUtc(Carbon::now()->subMinute()->startOfMinute());
        return $this->task->complete_date->gt(Carbon::now())? $currentDate : $completeDate;
    }

    public function reasonRequired()
    {
        return $this->taskInprogress() && $this->completeDateChanged();
    }

    public function completeDateChanged()
    {
        $completeDate = TimeZoneConverter::convertFromUtc($this->task->complete_date);
        return !Carbon::parse($this->complete_date)->eq($completeDate);
    }

    public function taskInprogress()
    {
        return in_array($this->task->status, Task::inprogressStatuses());
    }
}
