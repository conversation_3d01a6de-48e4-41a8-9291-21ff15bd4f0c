<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static APPROVED
 * @method static static AUTHORISED
 * @method static static CANCELED
 * @method static static CAPTURED
 */
final class TamaraWebhookEventStatus extends Enum
{
    const APPROVED = 'order_approved';
    const AUTHORISED = 'order_authorised';
    const CANCELED = 'order_canceled';
    const CAPTURED = 'order_captured';
}
