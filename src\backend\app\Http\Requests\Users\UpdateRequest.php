<?php

namespace App\Http\Requests\Users;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\NonOverlapSecheduleSection;
use App\Models\User;
use App\Enums\Day;
use Auth;
use Lang;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('update', User::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name.ar' => 'required|min:3|max:150|regex:/^(?!.*\d)[\p{Arabic}\s]+$/iu',
            'name.en' => 'required|min:3|max:150|regex:/^(?!.*\d)[a-zِِA-Z\s]+$/iu',
            // 'profile_image' => 'nullable|mimes:jpeg,jpg,png|max:'.config('app.profile_max_size'),
            'role_id' => 'required|in:'. implode(',', User::internalUsersRoles()),
            'phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA|unique:users,phone_number,'
                .$this->user->id.',id,deleted_at,NULL',
            'email' => 'required|email:rfc,dns|unique:users,email,'.$this->user->id.',id,deleted_at,NULL',
            'schedules' => 'required_if:role_id,'.implode(',', User::usersHaveSchedules()).'|array',
            'schedules.*.day' => 'required|distinct|in:'.implode(',', Day::getValues()),
            'schedules.*.from_hour' => 'required|date_format:H:i',
            'schedules.*.to_hour' => 'required|date_format:H:i|after:schedules.*.from_hour',
            'schedules.*.schedule_sections' => ['nullable','array', new NonOverlapSecheduleSection],
            'schedules.*.schedule_sections.*.from_hour' => 'required|date_format:H:i|
                after_or_equal:schedules.*.from_hour|before_or_equal:schedules.*.to_hour',
            'schedules.*.schedule_sections.*.to_hour' => 'required|date_format:H:i|
                after:schedules.*.schedule_sections.*.from_hour|before_or_equal:schedules.*.to_hour',
        ];
    }

    public function messages()
    {
        return array_merge(
            $this->mainInformationMessages(),
            $this->extraInformationMessages(),
            $this->schedulesMessages(),
            $this->scheduleSectionsMessages(),
        );
    }

    public function mainInformationMessages()
    {
        return [
            'username.required' => Lang::get('validation.custom.users.username.required'),
            'username.unique' => Lang::get('validation.custom.users.username.unique'),
            'name.ar.required' => Lang::get('validation.custom.users.name.ar.required'),
            'name.ar.min' => Lang::get('validation.custom.users.name.ar.min', ['min' => 3]),
            'name.ar.max' => Lang::get('validation.custom.users.name.ar.max', ['max' => 150]),
            'name.ar.regex' => Lang::get('validation.custom.users.name.ar.regex'),
            'name.en.required' => Lang::get('validation.custom.users.name.en.required'),
            'name.en.min' => Lang::get('validation.custom.users.name.en.min', ['min' => 3]),
            'name.en.max' => Lang::get('validation.custom.users.name.en.max', ['max' => 150]),
            'name.en.regex' => Lang::get('validation.custom.users.name.en.regex'),
            'profile_image.required' => Lang::get('validation.custom.profile.profile_image.required'),
            'profile_image.mimes' => Lang::get(
                'validation.custom.profile.profile_image.mimes', ['values' => 'jpeg,jpg,png']
            ),
            'profile_image.max' => Lang::get(
                'validation.custom.profile.profile_image.max', ['max' => config('app.profile_max_size')]
            ),
            'role_id.required' => Lang::get('validation.custom.users.role_id.required'),
            'role_id.in' => Lang::get(
                'validation.custom.users.role_id.in', ['in' => implode(',', user::internalUsersRoles())]
            ),
        ];
    }

    public function extraInformationMessages()
    {
        return [
            'phone_number.required' => Lang::get('validation.custom.requests.phone_number.required'),
            'phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.phone'),
            'phone_number.unique' => Lang::get('validation.custom.patient-register.phone_number.unique'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
            'email.required' => Lang::get('validation.custom.users.email.required'),
            'email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'email.unique' => Lang::get('validation.custom.users.email.unique'),
        ];
    }

    public function schedulesMessages()
    {
        return array(
            'schedules.required_if' => Lang::get(
                'validation.custom.schedules.schedules.required_if', ['in' => implode(',', user::usersHaveSchedules())]
            ),
            'schedules.array' => Lang::get('validation.custom.schedules.schedules.array'),
            'schedules.*.day.required' => Lang::get('validation.custom.schedules.day.required'),
            'schedules.*.day.distinct' => Lang::get('validation.custom.schedules.day.distinct'),
            'schedules.*.day.in' => Lang::get(
                'validation.custom.schedules.day.in', ['in' => implode(',', Day::getValues())]
            ),
            'schedules.*.from_hour.required' => Lang::get('validation.custom.schedules.from_hour.required'),
            'schedules.*.from_hour.date_format' => Lang::get(
                'validation.custom.schedules.from_hour.date_format', ['format' => 'H:i']
            ),
            'schedules.*.to_hour.required' => Lang::get('validation.custom.schedules.to_hour.required'),
            'schedules.*.to_hour.date_format' => Lang::get(
                'validation.custom.schedules.to_hour.date_format', ['format' => 'H:i']
            ),
            'schedules.*.to_hour.after' => Lang::get('validation.custom.schedules.to_hour.after'),
        );
    }

    public function scheduleSectionsMessages()
    {
        return array(
            'schedules.*.schedule_sections.required' => 
                Lang::get('validation.custom.schedules.schedule_sections.required'),
            'schedules.*.schedule_sections.array' => Lang::get('validation.custom.schedules.schedule_sections.array'),
            'schedules.*.schedule_sections.*.from_hour.required' => 
                Lang::get('validation.custom.schedules.from_hour.required'),
            'schedules.*.schedule_sections.*.from_hour.date_format' => Lang::get(
                'validation.custom.schedules.from_hour.date_format', ['format' => 'H:i']
            ),
            'schedules.*.schedule_sections.*.from_hour.after_or_equal' => 
                Lang::get('validation.custom.schedules.from_hour.after_or_equal'),
            'schedules.*.schedule_sections.*.from_hour.before_or_equal' => 
                Lang::get('validation.custom.schedules.from_hour.before_or_equal'),
            'schedules.*.schedule_sections.*.to_hour.required' => 
                Lang::get('validation.custom.schedules.to_hour.required'),
            'schedules.*.schedule_sections.*.to_hour.date_format' => Lang::get(
                'validation.custom.schedules.to_hour.date_format', ['format' => 'H:i']
            ),
            'schedules.*.schedule_sections.*.to_hour.after' => Lang::get('validation.custom.schedules.to_hour.after'),
            'schedules.*.schedule_sections.*.to_hour.before_or_equal' => 
                Lang::get('validation.custom.schedules.to_hour.before_or_equal'),
        );
    }
}
