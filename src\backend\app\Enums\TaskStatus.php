<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static NEW()
 * @method static static INPROGRESS()
 * @method static static COMPLETED()
 */
final class TaskStatus extends Enum implements LocalizedEnum
{
    const NEW =                       1;
    const INPROGRESS =                2;
    const INPROGRESS_WITH_DELAY =     3;
    const COMPLETED =                 4;
    const COMPLETED_WITH_DELAY =      5;
}
