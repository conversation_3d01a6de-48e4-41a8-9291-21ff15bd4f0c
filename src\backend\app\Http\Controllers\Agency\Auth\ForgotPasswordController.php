<?php

namespace App\Http\Controllers\Agency\Auth;

use App\Http\Requests\Auth\Agency\ForgotPasswordAgencyRequest;
use App\Services\ForgotPasswordService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class ForgotPasswordController extends Controller
{
    public function __invoke(ForgotPasswordAgencyRequest $request)
    {
        return app(ForgotPasswordService::class)->getAgencyResetToken($request->validated());
    }
}