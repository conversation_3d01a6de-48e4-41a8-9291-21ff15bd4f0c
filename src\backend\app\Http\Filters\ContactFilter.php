<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\Patient;

class ContactFilter extends Filter
{
    public $fields = array('created_at', 'updated_at');

    /**
     * @param string $patientId
     */
    public function patientId(string $patientId)
    {
        $this->builder->whereHas(
            'contactable', function (Builder $query) use ($patientId) {
                $query->where('contactable_id', $patientId)
                    ->where('contactable_type', Patient::class);
            }
        );
    }

    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}