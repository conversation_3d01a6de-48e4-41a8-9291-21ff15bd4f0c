<?php

namespace App\Http\Controllers\Agency;

use App\Http\Resources\SamplingReservations\SamplingReservationStatisticsResource;
use App\Http\Requests\SamplingReservations\ChangeSamplingReservationStatusRequest;
use App\Http\Requests\SamplingReservations\UpdateSamplingReservationNotesRequest;
use App\Http\Resources\SamplingReservations\SamplingReservationCollection;
use App\Http\Filters\SamplingReservationFilter;
use App\Services\SamplingReservationService;
use App\Http\Controllers\Controller;
use App\Models\SamplingReservation;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class SamplingReservationController extends Controller
{
    private $service;

    public function __construct(SamplingReservationService $service)
    {
        $this->service = $service;
    }

    public function index(SamplingReservationFilter $filter)
    {
        $result = $this->service->myFilter($filter);
        return new SamplingReservationCollection($result);
    }

    public function statistics()
    {
        $statistics = $this->service->myStatistics();
        return response()->json(
            ['statistics' => new SamplingReservationStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function changeStatus(
        ChangeSamplingReservationStatusRequest $request, SamplingReservation $samplingReservation
    ) {
        $this->service->changeStatus($request->validated(), $samplingReservation);
        return response()->json(
            ['message' => Lang::get('messages.sampling-reservations.success.change_status')], Response::HTTP_OK
        );
    }

    public function changeNotes(
        UpdateSamplingReservationNotesRequest $request, SamplingReservation $samplingReservation
    ) {
        $this->service->changeNotes($request->validated(), $samplingReservation);
        return response()->json(
            ['message' => Lang::get('messages.sampling-reservations.success.change_notes')], Response::HTTP_OK
        );
    }
}
