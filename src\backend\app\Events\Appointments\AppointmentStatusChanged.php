<?php

namespace App\Events\Appointments;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class AppointmentStatusChanged
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $appointment, $user, $reason, $status, $completionDueDate;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($appointment, $user, $reason, $status, $completionDueDate)
    {
        $this->appointment = $appointment;
        $this->user = $user;
        $this->reason = $reason;
        $this->status = $status;
        $this->completionDueDate = $completionDueDate;
    }
}
