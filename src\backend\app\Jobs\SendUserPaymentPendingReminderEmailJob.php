<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\UserService;
use App\Enums\PaymentStatus;
use App\Traits\MailTrait;
use App\Enums\Role;
use Lang;

class SendUserPaymentPendingReminderEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $payment;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->payment->refresh();
        if ($this->payment->isStatus(PaymentStatus::PENDING)) {
            $users = app(UserService::class)->users([Role::FOLLOW_UP, Role::ADMIN, Role::ACCOUNTANT]);
            foreach ($users as $user) {
                $this->sendPendingPaymentReminderMail($this->payment, $user);
            }
        }
    }
}
