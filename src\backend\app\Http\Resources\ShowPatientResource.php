<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\PatientSource;
use App\Traits\UserTrait;
use App\Models\Patient;
use Lang;

class ShowPatientResource extends JsonResource
{
    use UserTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $user = $this->systemUser();

        return array(
            'id' => $this->id,
            'identification_number' => $this->identification_number,
            'first_name' => $this->first_name,
            'name' => array(
                'ar' => $this->translate('ar')->name,
                'en' => $this->translate('en')->name
            ),
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'profile_image' => $this->profile_image,
            'gender_type' => $this->gender_type,
            'birthday' => $this->birthday? $this->birthday->format('Y-m-d') : null,
            'program_id' => $this->program_id,
            'program_name' => $this->program->name??  null,
            'reference_number' => $this->reference_number,
            'social_status' => $this->social_status,
            'source' => $this->source,
            'source_name' => Lang::get('translations.request_sources.'. PatientSource::getKey($this->source)),
            'city_id' => $this->city_id,
            'city_name' => $this->city_id? Lang::get($this->city->name) : null,
            'region_id' => $this->city->region_id?? null,
            'region_name' => $this->city_id? Lang::get($this->city->region->name) : null,
            'address' => $this->address,
            'birthplace_id' => $this->birthplace_id,
            'birthplace_name' => $this->birthplace_id? Lang::get($this->birthplace->name) : null,
            'origin_city_id' => $this->origin_city_id,
            'origin_city_name' => $this->origin_city_id?  Lang::get($this->originCity->name) : null,
            'contact_method' => $this->contact_method,
            'patient_type' => $this->patient_type,
            'test_type' => $this->test_type,
            'has_family_history' => $this->has_family_history,
            'father_family_id' => $this->father_family_id,
            'father_family_name' => $this->father_family_id? $this->fatherFamily->name : null,
            'mother_family_id' => $this->mother_family_id,
            'mother_family_name' => $this->mother_family_id? $this->motherFamily->name : null,
            'parents_relationship' => $this->parents_relationship,
            'contacts' => ContactResource::collection($this->contacts),
            'can_edit' => Patient::checkCanEdit($user),
            'is_deleted' => $this->trashed(),
            'is_saudi' => $this->is_saudi
        );
    }
}
