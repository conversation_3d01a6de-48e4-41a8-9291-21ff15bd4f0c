<?php

namespace App\Http\Resources\Tasks;

use Illuminate\Http\Resources\Json\JsonResource;

class TaskStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'new' => $this->new,
            'inprogress' => $this->inprogress,
            'inprogress_with_delay' => $this->inprogress_with_delay,
            'completed' => $this->completed,
            'completed_with_delay' => $this->completed_with_delay,
        );
    }
}
