<?php

namespace App\Http\Connectors;

use Kreait\Firebase\Messaging\CloudMessage;
use Kreait\Firebase\Messaging\Notification;
use Kreait\Firebase\Messaging\AndroidConfig;
use Kreait\Firebase\Messaging\ApnsConfig;
use App\Services\FcmTokenService;
use Kreait\Firebase\Messaging;
use Illuminate\Http\Response;
use Lang;
use Log;

class FirebaseNotificationConnector
{
    public static function sendMulticast(array $deviceTokens, Notification $notification, array $data)
    {
        $messaging = app('firebase.messaging');
        $message = CloudMessage::new()
            ->withNotification($notification)
            ->withAndroidConfig(
                AndroidConfig::new()
                ->withHighPriority()
            )
            ->withApnsConfig(
                ApnsConfig::new()
                ->withImmediatePriority()
            )
            ->withDefaultSounds()
            ->withData($data);
                

        $response = $messaging->sendMulticast($message, $deviceTokens);

         // Log::error("sendMulticast:::::");   
         // Log::error(print_r($response, true));   
         //         Log::error("message:::::");   
         //    Log::error(print_r($message, true));   

        return Self::getResponse($response, count($deviceTokens));
    }

    public static function notification(string $title, string $body)
    {
        return Notification::create($title, $body);
    }

    public static function notify(array $deviceTokens, string $title, string $body, array $data)
    {
        $notification = Self::notification($title, $body);
        return Self::sendMulticast($deviceTokens, $notification, $data);
    }

    public static function getResponse($response, $tokensCount)
    {
        if ($response->successes()->count() == $tokensCount) {
            return true;
        }
        if ($response->hasFailures()) {
            foreach ($response->failures()->getItems() as $failure) {
                Log::channel('firebase_notification')->error($failure->error()->getMessage());
            }
        }
        // remove invalid token
        $invalidTokens = $response->invalidTokens();
        app(FcmTokenService::class)->deleteMultiple('fcm_token', $invalidTokens);
    }
}
