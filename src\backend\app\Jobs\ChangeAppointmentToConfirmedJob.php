<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\AppointmentService;
use App\Enums\AppointmentPurpose;
use App\Services\RequestService;
use App\Enums\AppointmentStatus;
use App\Enums\AppointmentReason;
use App\Enums\RequestStatus;

class ChangeAppointmentToConfirmedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $appointment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->appointment->refresh();
        if ($this->appointment->isStatus(AppointmentStatus::INPROGRESS)) {
            $reasonId = AppointmentReason::NOT_COMPLETED_DATA;
            $data = array('status' => AppointmentStatus::CONFIRMED, 'appointment_reason_id' => $reasonId);
            $request = $this->appointment->request;
            $patient = $this->appointment->patient;
            app(AppointmentService::class)
                ->updateAppointmentStatus($data, $this->appointment, $reasonId, null, $this->appointment->user);
            app(AppointmentService::class)->completeAllActive($patient, $request, $this->appointment);
            $requestStatus = $this->appointment->isPurpose(AppointmentPurpose::RESULT_EXPLANATION)?
                RequestStatus::RESULT_INTERVIEW : RequestStatus::OBLIGATION_NOT_SENT;
            app(RequestService::class)->updateRequestStatus($request, $requestStatus);
        }
    }
}
