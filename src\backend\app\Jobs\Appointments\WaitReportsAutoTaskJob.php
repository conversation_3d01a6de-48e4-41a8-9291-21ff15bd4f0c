<?php

namespace App\Jobs\Appointments;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\UserService;
use App\Services\TaskService;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class WaitReportsAutoTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $appointment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $patientName = $this->appointment->patient->translate('ar')->name;
        $date = Carbon::parse($this->appointment->completion_due_date_converted)->format('Y-m-d');
        $coordinators = app(UserService::class)->users([Role::FOLLOW_UP]);
        $data = array(
            'description' => Lang::get(
                'translations.appointments.waiting_reports_auto_task',
                ['name' => $patientName, 'date' => $date], 'ar'
            ),
            'complete_date' => $this->appointment->completion_due_date,
        );
        foreach ($coordinators as $coordinator) {
            app(TaskService::class)->createModel($data, $coordinator);
        }
    }
}
