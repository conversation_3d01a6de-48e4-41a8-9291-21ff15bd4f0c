<?php

namespace App\Models;

use Astrotomic\Translatable\Contracts\Translatable as TranslatableContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\SoftDeletes;
use Astrotomic\Translatable\Translatable;
use Illuminate\Database\Eloquent\Model;
use App\Traits\TimeZoneConverterTrait;
use App\Traits\LabTestStatusTrait;
use App\Enums\RequestStatus;
use App\Enums\LabTestStatus;
use App\Traits\FilterTrait;
use App\Enums\RequestStage;
use App\Enums\Role;
use Carbon\Carbon;

class PatientLabTest extends Model implements TranslatableContract
{
    use HasFactory, SoftDeletes, FilterTrait, Translatable, TimeZoneConverterTrait,
        LabTestStatusTrait;

    public static $approved =  -3;
    public static $notApproved = -4;
    public static $notApprovedAndDelay =    -5;
    public static $samplingNotBooked = -6;
    public static $samplingNotBookedWithDelay = -7;

    protected $fillable = [
        'lab_test_id', 'request_id', 'status', 'notes', 'patient_id',
        'obligation_id', 'payment_id', 'status_before_cancel', 'sample_quantity',
        'tube_type', 'tubes_count', 'result_due_days', 'sample_date', 'force_deposited',
        'send_date', 'result_id', 'general_introduction', 'general_conclusion','result_pdf'
    ];

    protected $translatedAttributes = [
        'name', 'sample_type',
    ];

    protected $casts = [
        'sample_date' => 'datetime:Y-m-d H:i:s',
        'send_date' => 'datetime:Y-m-d H:i:s',
    ];

    public function patientLabTestTranslations()
    { 
        return $this->hasMany(PatientLabTestTranslation::class); 
    }

    public function shipments()
    { 
        return $this->hasMany(Shipment::class); 
    }

    public function testResultCategories()
    {
        return $this->hasMany(TestResultCategory::class);
    }

    public function scopeRequestId($query, $id)
    {
        return $query->where('request_id', $id);
    }

    public function scopeNotRequestId($query, $id)
    {
        return $query->where('request_id', '!=', $id);
    }

    public function scopeLabTest($query, $id)
    {
        return $query->where('lab_test_id', $id);
    }

    public function scopePatient($query, $id)
    {
        return $query->where('patient_id', $id);
    }

    public function scopeStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    public function scopeStatuses($query, $statuses)
    {
        return $query->whereIn('status', $statuses);
    }

    public function scopeNotStatus($query, $status)
    {
        return $query->where('status', '!=', $status);
    }

    public function scopeNotStatuses($query, $statuses)
    {
        return $query->whereNotIn('status', $statuses);
    }

    public function scopePatientLabTest($query, $id)
    {
        return $query->where('id', '<>', $id);
    }

    public function scopeApproved($query)
    {
        return $query->whereNotIn(
            'status', 
            array(
                LabTestStatus::NEW,
                LabTestStatus::SENT_TO_PATIENT,
                LabTestStatus::CANCELED,
            )
        );
    }

    public function scopeCreatedAfter($query, $date)
    {        
        return $query->whereDate('created_at', '>=', $date);
    }

    public function scopeNotApproved($query)
    {
        return $query->status(LabTestStatus::SENT_TO_PATIENT)->where('send_date', '>=', Carbon::now()->subHours(24));
    }

    public function scopeNotApprovedWithDelay($query)
    {
        return $query->status(LabTestStatus::SENT_TO_PATIENT)->where('send_date', '<', Carbon::now()->subHours(24));
    }

    public function scopeActiveRequests($query)
    {        
        return $query->whereHas(
            'request', function ($query) {
                $query->stage(RequestStage::ACTIVE);
            }
        );
    }

    public function scopeRequestStatus($query, $status)
    {
        return $query->whereHas(
            'request', function ($query) use ($status) {
                $query->status($status);
            }
        );
    }

    public function scopeFilterByDateRange($query, $startDate, $endDate)
    {
        return $query->when(
            $startDate && $endDate, function ($query) use ($startDate, $endDate) {
                return $query->dateRang($startDate, $endDate);
            }
        );
    }

    public function scopeDateRang($query, $startDate, $endDate)
    {
        return $query->whereDate('created_at', '>=', $startDate)
            ->whereDate('created_at', '<=', $endDate);
    }

    public function labTest()
    {
        return $this->belongsTo(LabTest::class); 
    }

    public function obligation()
    {
        return $this->belongsTo(Obligation::class); 
    }

    public function payment()
    {
        return $this->belongsTo(Payment::class); 
    }

    public function patient()
    {
        return $this->belongsTo(Patient::class); 
    }

    public function request()
    {
        return $this->belongsTo(Request::class); 
    }

    public function result()
    {
        return $this->belongsTo(Result::class); 
    }

    public function samplingReservation()
    {
        return $this->hasOne(SamplingReservation::class)
            ->ofMany('id', 'max');
    }

    public static function canTakeAction($roleId)
    {
        return in_array($roleId, array(Role::SPECIALIST, Role::CONSULTANT));
    }

    public static function canForceDeposited($roleId)
    {
        return in_array($roleId, array(Role::ADMIN, Role::FOLLOW_UP, Role::RECEPTIONIST));
    }

    public function isStatus($status)
    {
        return $this->status == $status;
    }

    public function isNotApproved()
    {
        return $this->status == LabTestStatus::SENT_TO_PATIENT && 
            $this->send_date->gte(Carbon::now()->subHours(24));
    }

    public function isNotApprovedWithDelay()
    {
        return $this->status == LabTestStatus::SENT_TO_PATIENT &&
            $this->send_date->lt(Carbon::now()->subHours(24));
    }

    public function statusValue()
    {
        return $this->isStatus(LabTestStatus::CANCELED)? $this->status_before_cancel : $this->status;
    }

    public function getReceiveResultAfterAttribute()
    {
        $dueDays = $this->result_due_days + $this->labTest->result_preparation_days;
        $samplingReservationDate = $this->sample_date->setTime(0, 0, 0);
        $currentDate = Carbon::now()->setTime(0, 0, 0);
        $days = $dueDays - ($samplingReservationDate->diffInDays($currentDate));
        return $days > 0? $days : 0;
    }

    public function isApproved()
    {
        // TODO ADD approved status
        return in_array($this->status, array_merge(self::$approvedStatuses, [LabTestStatus::REFUNDED]));
    }

    public function canRefund()
    {
        return in_array(
            $this->status, array(
            LabTestStatus::PAID,
            LabTestStatus::SAMPLING_RESERVATION,
            LabTestStatus::SAMPLE_NOT_DEPOSITED,
            )
        );
    }
}
