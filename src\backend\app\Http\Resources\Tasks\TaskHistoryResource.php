<?php

namespace App\Http\Resources\Tasks;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\TaskStatus;

class TaskHistoryResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'status_id' => $this->status,
            'status' => TaskStatus::getDescription($this->status),    
            'date' => $this->created_at_converted,
            'is_status_changed' => $this->is_status_changed,
            'reason' => $this->reason
        );
    } 
}
