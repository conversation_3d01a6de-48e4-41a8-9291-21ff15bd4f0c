<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\AppointmentType;
use App\Enums\AppointmentPurpose;
use App\Enums\AppointmentStatus;
use App\Enums\RequestStatus;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class AppointmentDetailsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $appointmentHistory = $this->appointmentHistories->first(
            function ($history) {
                return in_array($history->status, array(AppointmentStatus::CONFIRMED, AppointmentStatus::CANCELED));
            }
        );
        $appointmentReason = $appointmentHistory? $appointmentHistory->appointmentReason: null;

        return array(
            'id' => $this->id,
            'start_date' => $this->start_date_converted,
            'end_date' => $this->end_date_converted,
            'patient_id' => $this->patient_id,
            'patient_name' => $this->patient->name,
            'patient_image' => $this->patient->profile_image,
            'patient_phone_number' => $this->patient->phone_number,
            'patient_gender_type' => $this->patient->gender_type,
            'program_id' => $this->patient->program_id,
            'program_name' => $this->patient->program->name??  null,
            'type' => $this->type,
            'type_name' => AppointmentType::getDescription($this->type),
            'purpose' => $this->purpose,
            'purpose_name' => AppointmentPurpose::getDescription($this->purpose),
            'user_id' => $this->user_id,
            'user_name' => $this->user->name?? Lang::get('translations.user'),
            'role_id' => $this->user->role_id?? null,
            'role_name' => $this->user? Role::getDescription($this->user->role_id) : null,
            'status' => $this->status,
            'status_name' => AppointmentStatus::getDescription($this->status),
            'appointment_reason_id' => $appointmentHistory->appointment_reason_id?? null,
            'appointment_reason' => Lang::get($appointmentReason->reason?? ''),
            'notes' => $appointmentHistory->notes?? null,
            'join_url'=> $this->onlineMeeting->join_url?? null,
            'call_recording_url' => $this->onlineMeeting->call_recording_url?? null,
            'request_id' => $this->request_id,
            'request_status' => $this->request->request_status,
            'request_status_name' => RequestStatus::getDescription($this->request->request_status),
            'completion_due_date' => Carbon::parse($this->completion_due_date_converted)->format('Y-m-d'),
        );
    }
}
