<?php

namespace App\Services;

use App\Repositories\TestResultRepository;
use App\Repositories\PatientLabTestRepository;
use App\Models\PatientLabTest;
use App\Enums\LabTestStatus;
use App\Enums\ResultStatus;
use App\Traits\TestResultReportTrait;
use Illuminate\Http\Response;

class TestResultService extends BaseService
{
    use TestResultReportTrait;

    protected $patientLabTestRepository;

    public function __construct(
        TestResultRepository $repository,
        PatientLabTestRepository $patientLabTestRepository
    ) {
        $this->setRepository($repository);
        $this->patientLabTestRepository = $patientLabTestRepository;
    }

    /**
     * Create a test result with categories, genes, and conclusion
     *
     * @param array $data
     * @param int $patientLabTestId
     * @return mixed
     */
    public function createTestResult(array $data, $patientLabTestId)
    {
        $patientLabTest = $this->patientLabTestRepository->find($patientLabTestId);

        if (!$patientLabTest) {
            return $this->errorResponse('Patient lab test not found', Response::HTTP_NOT_FOUND);
        }

        // Check if the patient lab test is in a valid state to receive results
        // if (!in_array($patientLabTest->status, [
        //     LabTestStatus::SAMPLE_SHIPPED,
        //     LabTestStatus::RESULT_NOT_RECEIVED
        // ])) {

        //     return $this->errorResponse(
        //         'Patient lab test is not in a valid state to receive results',
        //         Response::HTTP_BAD_REQUEST
        //     );
        // }

        // Create general introduction if provided
        $generalIntroduction = $data['general_introduction'] ?? null;

        // Create general conclusion if provided
        $generalConclusion = $data['general_conclusion'] ?? null;
        // Create test result categories with their components
        $categories = [];
        if (isset($data['categories']) && is_array($data['categories'])) {
            foreach ($data['categories'] as $categoryData) {
                $categories[] = $this->repository->createTestResult($categoryData, $patientLabTest);
            }
        }



        // Update the patient lab test status
        $patientLabTest->update([
            'status' => LabTestStatus::RESULT_RECEIVED,
            'general_introduction' => $generalIntroduction,
            'general_conclusion' => $generalConclusion
        ]);

        // Update the result status if exists
        if ($patientLabTest->result_id) {
            app(ResultService::class)->update(
                ['status' => ResultStatus::RECEIVED],
                $patientLabTest->result_id
            );
        }

        return $this->successResponse(
            'Test result created successfully',
            ['patient_lab_test' => $patientLabTest, 'categories' => $categories],
            Response::HTTP_CREATED
        );
    }

    /**
     * Get test results for a patient lab test
     *
     * @param int $patientLabTestId
     * @return mixed
     */
    public function getTestResult($patientLabTestId)
    {
        $patientLabTest = $this->patientLabTestRepository->find($patientLabTestId);
        if (!$patientLabTest) {
            return $this->errorResponse('Patient lab test not found', Response::HTTP_NOT_FOUND);
        }

        // Check if the patient lab test has results
        if ($patientLabTest->status != LabTestStatus::RESULT_RECEIVED) {
            return $this->errorResponse(
                'Patient lab test does not have results',
                Response::HTTP_BAD_REQUEST
            );
        }

        $testResult = $this->repository->getTestResultByPatientLabTest($patientLabTestId);
        return $this->successResponse(
            'Test result retrieved successfully',
            [
                'patient_lab_test' => $patientLabTest,
                'general_introduction' => $patientLabTest->general_introduction,
                'general_conclusion' => $patientLabTest->general_conclusion,
                'test_result' => $testResult
            ],
            Response::HTTP_OK
        );
    }

    /**
     * Get test result data for TestResultResource
     *
     * @param int $patientLabTestId
     * @return array
     */
    public function getTestResultData($patientLabTestId)
    {
        $patientLabTest = $this->patientLabTestRepository->find($patientLabTestId);
        if (!$patientLabTest) {
            return [
                'success' => false,
                'message' => 'Patient lab test not found',
                'status' => Response::HTTP_NOT_FOUND
            ];
        }

        // Check if the patient lab test has results
        if ($patientLabTest->status != LabTestStatus::RESULT_RECEIVED) {
            return [
                'success' => false,
                'message' => 'Patient lab test does not have results',
                'status' => Response::HTTP_BAD_REQUEST
            ];
        }

        // Load necessary relationships
        $patientLabTest->load([
            'testResultCategories.genes.gene',
            'testResultCategories.genes.mutationSite',
            'testResultCategories.genes.geneResult',
            'testResultCategories.genes.transmissionMethod',
            'testResultCategories.genes.geneCategory',
            'testResultCategories.genes.illness'
        ]);

        // Build data structure for TestResultResource
        $testResultData = (object) [
            'id' => $patientLabTest->id,
            'patient_lab_test_id' => $patientLabTest->id,
            'general_introduction' => $patientLabTest->general_introduction,
            'general_conclusion' => $patientLabTest->general_conclusion,
            'categories' => $patientLabTest->testResultCategories
        ];

        return [
            'success' => true,
            'message' => 'Test result retrieved successfully',
            'data' => $testResultData,
            'status' => Response::HTTP_OK
        ];
    }

    /**
     * Generate PDF report for test results
     *
     * @param int $patientLabTestId
     * @return mixed
     */
    public function generateTestResultReport($patientLabTestId)
    {
        $patientLabTest = $this->patientLabTestRepository->find($patientLabTestId);

        if (!$patientLabTest) {
            return $this->errorResponse('Patient lab test not found', Response::HTTP_NOT_FOUND);
        }

        // Check if the patient lab test has results
        if ($patientLabTest->status != LabTestStatus::RESULT_RECEIVED) {
            return $this->errorResponse(
                'Patient lab test does not have results to generate report',
                Response::HTTP_BAD_REQUEST
            );
        }

        // Load necessary relationships for PDF generation
        $patientLabTest->load([
            'patient.translations',
            'labTest',
            'testResultCategories.genes.gene',
            'testResultCategories.genes.mutationSite',
            'testResultCategories.genes.geneResult',
            'testResultCategories.genes.transmissionMethod',
            'testResultCategories.genes.geneCategory',
            'testResultCategories.genes.illness'
        ]);

        try {
            $pdfPath = $this->createTestResultReportPdf($patientLabTest);
            $patientLabTest->update([
                'result_pdf' => $pdfPath
            ]);
            
            return $this->successResponse(
                'Test result report generated successfully',
                [
                    'pdf_url' => config('app.url') . $pdfPath,
                    'pdf_path' => $pdfPath
                ],
                Response::HTTP_OK
            );
        } catch (\Exception $e) {
            return $this->errorResponse(
                'Failed to generate test result report: ' . $e->getMessage(),
                Response::HTTP_INTERNAL_SERVER_ERROR
            );
        }
    }
}