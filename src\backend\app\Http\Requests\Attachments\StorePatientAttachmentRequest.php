<?php

namespace App\Http\Requests\Attachments;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class StorePatientAttachmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'attachment' => 'required|mimes:jpeg,jpg,png,pdf|max:'.config('app.profile_max_size'),
        ];
    }

    public function messages()
    {
        return [
            'attachment.required' => Lang::get('validation.custom.attachments.attachment.required'),
            'attachment.mimes' => Lang::get(
                'validation.custom.attachments.attachment.mimes', ['mimes' => 'jpeg,jpg,png,pdf']
            ),
            'attachment.max' => Lang::get(
                'validation.custom.attachments.attachment.max', ['max' => config('app.profile_max_size')]
            ),
        ];
    }
}
