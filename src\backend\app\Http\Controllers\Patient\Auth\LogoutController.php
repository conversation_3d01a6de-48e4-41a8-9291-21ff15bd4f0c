<?php

namespace App\Http\Controllers\Patient\Auth;

use App\Http\Controllers\Controller;
use App\Http\Requests\PatientLogoutRequest;
use App\Services\PatientService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lang;

class LogoutController extends Controller
{
    public function __invoke(PatientLogoutRequest $request)
    {
        app(PatientService::class)->logout($request->validated());
        return response()->json(['message' => Lang::get('messages.logout.success')], Response::HTTP_OK);
    }
}