<?php

namespace App\Http\Controllers;

use App\Http\Requests\Tamara\AddWebhookRequest;
use App\Services\TamaraPaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;

class TamaraWebhookController extends Controller
{

    private $tamaraPaymentService;

    public function __construct(TamaraPaymentService $tamaraPaymentService)
    {
        $this->tamaraPaymentService = $tamaraPaymentService;
    }

    /**
     * register
     *
     * @return JsonResponse
     */
    public function register(AddWebhookRequest $request)
    {
        return $this->tamaraPaymentService->registerWebhook($request);
    }


    /**
     * sync with webhook
     *
     * @param  mixed $request
     * @return void
     */
    public function syncwithWebhook(Request $request)
    {
        $this->tamaraPaymentService->syncWebhookData($request);
        return response()->json(['message' => 'Success'], Response::HTTP_OK);
    }
}
