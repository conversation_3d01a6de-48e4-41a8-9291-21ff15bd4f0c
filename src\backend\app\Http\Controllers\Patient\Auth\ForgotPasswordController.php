<?php

namespace App\Http\Controllers\Patient\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\ForgotPasswordPatientRequest;
use App\Services\ForgotPasswordService;

class ForgotPasswordController extends Controller
{
    public function __invoke(ForgotPasswordPatientRequest $request)
    {
        return app(ForgotPasswordService::class)->getPatientResetToken($request->validated());
    }
}