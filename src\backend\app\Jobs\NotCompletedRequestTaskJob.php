<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\TaskService;
use App\Enums\RequestStatus;
use Carbon\Carbon;
use Lang;

class NotCompletedRequestTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $request, $systemUser;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request, $systemUser)
    {
        $this->request = $request;
        $this->systemUser = $systemUser;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        if ($this->request->isStatus(RequestStatus::CONFIRMED)) {
            $patientName = $this->request->requestable->name;
            $data = array(
                'description' => Lang::get('translations.requests.auto_task', ['name' => $patientName], 'ar'),
                'complete_date' => Carbon::now()->addDay(),
            );
            app(TaskService::class)->createModel($data, $this->systemUser);
        }
    }
}
