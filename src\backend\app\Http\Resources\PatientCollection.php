<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;
use App\Http\Resources\CollectionResource;
use App\Traits\UserTrait;

class PatientCollection extends ResourceCollection
{
    use UserTrait, CollectionResource;

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $user = $this->systemUser();
        CustomPatientResource::user($user);
        
        return array(
            'current_page' => $this->currentPage(),
            'data' => CustomPatientResource::collection($this->items()),
            'total' => $this->total(),
            'last_page' => $this->lastPage(),
            'per_page' => $this->perPage()
        );
    }
}
