<?php

namespace App\Events\Requests;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class RequestRejected
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $request, $rejectReason;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($request, $rejectReason)
    {
        $this->request = $request;
        $this->rejectReason = $rejectReason;
    }
}
