<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\UserService;
use App\Enums\LabTestStatus;
use App\Traits\MailTrait;
use App\Enums\Role;

class SendUserSamplingReservationReminderEmailJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        if ($this->patientLabTest->isStatus(LabTestStatus::PAID)) {
            $coordinators = app(UserService::class)->users([Role::FOLLOW_UP, Role::ADMIN]);
            foreach ($coordinators as $coordinator) {
                $this->sendSamplingReservationMail($this->patientLabTest, $coordinator);
            }
        }
    }
}
