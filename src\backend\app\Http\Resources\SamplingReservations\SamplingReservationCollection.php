<?php

namespace App\Http\Resources\SamplingReservations;

use Illuminate\Http\Resources\Json\ResourceCollection;
use App\Http\Resources\CollectionResource;

class SamplingReservationCollection extends ResourceCollection
{
    use CollectionResource;

    /**
     * Transform the resource collection into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'current_page' => $this->currentPage(),
            'data' => $this->collection,
            'total' => $this->total(),
            'last_page' => $this->lastPage(),
            'per_page' => $this->perPage()
        );
    }
}
