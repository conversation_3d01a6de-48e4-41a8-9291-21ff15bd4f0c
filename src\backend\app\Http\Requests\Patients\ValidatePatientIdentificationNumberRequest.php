<?php

namespace App\Http\Requests\Patients;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class ValidatePatientIdentificationNumberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'identification_number' => 'required|min:10|max:20|unique:patients,identification_number,'.
                $this->patient->id.',id,deleted_at,NULL',
        ];
    }

    public function messages()
    {
        return [
            'identification_number.required' => Lang::get(
                'validation.custom.patient-register.identification_number.required'
            ),
            'identification_number.unique' => Lang::get(
                'validation.custom.patient-register.identification_number.unique'
            ),
            'identification_number.max' => Lang::get(
                'validation.custom.patient-register.identification_number.max', ['max' => 20]
            ),
            'identification_number.min' => Lang::get(
                'validation.custom.patient-register.identification_number.min', ['min' => 10]
            ),
        ];
    }
}
