<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;

class AppointmentStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'new' => $this->new,
            'active' => $this->active,
            'not_completed_with_delay' => $this->confirmedWithDelay,
            'completed' => $this->completed,
            'canceled' => $this->canceled,
            'missed' => $this->missed,
            'total' => $this->total,
        );
    }
}
