<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static OptionOne()
 * @method static static OptionTwo()
 * @method static static OptionThree()
 */
final class RequestStatus extends Enum implements LocalizedEnum
{
    const NEW =                       1;
    const CONFIRMED =                 2;
    const APPOINTMENT_RESERVATION =   3;
    const INTERVIEW =                 4;
    const OBLIGATION_CONFIRMED =      5;
    const PAYMENT_CONFIRMED =         6;
    const REJECTED =                  7;
    const CANCELED =                  8;
    const SAMPLE_DEPOSITED =          9;
    const RESULT_EXPLANATION =        10;
    const RESULT_RECEIVED =           11;
    const COMPLETED =                 12;
    const OBLIGATION_SENT =           13;
    const NOT_INTERVIEWED =           14;
    const OBLIGATION_NOT_SENT =       15;
    const OBLIGATION_NOT_CONFIRMED =  16;
    const NOT_PAID_WITH_DELAY =       17;
    const SAMPLING_RESERVATION =      18;
    const SAMPLE_NOT_DEPOSITED =      19;
    const SAMPLE_SHIPPED =            20;
    const SAMPLE_NOT_SHIPPED =        21;
    const RESULT_NOT_RECEIVED =       22;
    const RESULT_INTERVIEW =          23;
    const NOT_RESULT_EXPLANATION =    24;
}
