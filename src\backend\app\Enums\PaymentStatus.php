<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static NOT_PAID()
 * @method static static PENDING()
 * @method static static PAID()
 */
final class PaymentStatus extends Enum implements LocalizedEnum
{
    const NOT_PAID =       1;
    const PENDING =        2;
    const PAID =           3;
    const CANCELED =       4;
    const REJECTED =       5;
    const REFUNDED =       6;
    //TODO add status for un completed payment after complete request
}
