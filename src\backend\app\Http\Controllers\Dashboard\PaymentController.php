<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Resources\PaymentDashboardStatisticsResource;
use App\Jobs\Payments\CheckPaymentTransactionStatusJob;
use App\Http\Requests\Payments\RejectPaymentRequest;
use App\Http\Requests\Payments\PayPaymentRequest;
use App\Http\Resources\PaymentStatisticsResource;
use App\Http\Resources\PaymentCollection;
use App\Http\Controllers\Controller;
use App\Http\Filters\PaymentFilter;
use App\Services\PaymentService;
use App\Services\UserService;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use App\Enums\PaymentMethod;
use App\Models\Payment;
use Lang;

class PaymentController extends Controller
{
    private $paymentService;

    public function __construct(PaymentService $paymentService)
    {
        $this->paymentService = $paymentService;
    }

    public function index(PaymentFilter $filter)
    {
        $this->authorize('viewAny', Payment::class);
        $result = $this->paymentService->filter($filter);
        return new PaymentCollection($result);
    }

    public function statistics()
    {
        $this->authorize('statistics', Payment::class);
        $statistics = $this->paymentService->statistics();
        return response()->json(
            ['statistics' => new PaymentStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function accept(Payment $payment)
    {
        $this->authorize('accept', [Payment::class, $payment]);
        $this->paymentService->accept($payment);
        return response()->json(['message' => Lang::get('messages.payments.success.accept')], Response::HTTP_OK);
    }

    public function pay(PayPaymentRequest $request, Payment $payment)
    {
        $this->paymentService->pay($request->validated(), $payment);
        return response()->json(['message' => Lang::get('messages.payments.success.paid')], Response::HTTP_OK);
    }

    public function reject(RejectPaymentRequest $request, Payment $payment)
    {
        $this->paymentService->reject($request->validated(), $payment);
        return response()->json(['message' => Lang::get('messages.payments.success.reject')], Response::HTTP_OK);
    }

    public function newCount()
    {
        $this->authorize('newCount', Payment::class);
        return $this->paymentService->newCount();
    }

    public function lastSeen()
    {
        app(UserService::class)->paymentsLastSeen();
        return response()->json(['message' => Lang::get('messages.payments.success.last_seen')], Response::HTTP_OK);
    }

    public function refund(Payment $payment)
    {
        $this->authorize('refund', [Payment::class, $payment]);
        $this->paymentService->refund($payment);
        return response()->json(['message' => Lang::get('messages.payments.success.refund')], Response::HTTP_OK);
    }

    public function excel(PaymentFilter $filter)
    {
        $this->authorize('viewAny', Payment::class);
        return $this->paymentService->excel($filter);
    }

    public function dashboardStatistics(Request $request)
    {
        $this->authorize('dashboardStatistics', Payment::class);
        $statistics = $this->paymentService->dashboardStatistics($request->all());
        return response()->json(
            ['statistics' => new PaymentDashboardStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function paymentStatus(Payment $payment)
    {
        if ($payment->paymentMethod(PaymentMethod::ONLINE_PAYMENT)) {
            CheckPaymentTransactionStatusJob::dispatch($payment)
                ->onQueue(config('queue.queues.requests'));
        }
    }
}
