<?php

namespace App\Http\Controllers\Agency\Auth;

use App\Http\Requests\Auth\Agency\ResetPasswordAgencyRequest;
use App\Services\ResetPasswordService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class ResetPasswordController extends Controller
{
    public function __invoke(ResetPasswordAgencyRequest $request)
    {
        app(ResetPasswordService::class)->resetAgencyPassword($request->validated());
        return response()->json(['message' => Lang::get('messages.reset-password.success')], Response::HTTP_OK);
    }
}