<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class ValidateIdentificationNumberRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'identification_number' => 'required|unique:patients,identification_number,NULL,id,deleted_at,NULL',
        ];
    }

    public function messages()
    {
        return [
            'identification_number.required' => Lang::get(
                'validation.custom.patient-register.identification_number.required'
            ),
            'identification_number.unique' => Lang::get(
                'validation.custom.patient-register.identification_number.unique'
            ),
        ];
    }
}
