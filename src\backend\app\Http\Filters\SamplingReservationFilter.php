<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\Patient;

class SamplingReservationFilter extends Filter
{
    public $fields = array('date', 'created_at', 'updated_at');

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->orWhereHas(
            'patient', function (Builder $query) use ($search) {
                $query->whereHas(
                    'patientTranslations', function (Builder $query) use ($search) {
                        $query->where('name', 'like', '%'.$search.'%');
                    }
                );
            }
        );
    }

    /**
     * @param string $startDate
     */
    public function startDate(string $startDate)
    {
        $this->builder->whereDate('date', '>=', $startDate);
    }

    /**
     * @param string $endDate
     */
    public function endDate(string $endDate)
    {
        $this->builder->whereDate('date', '<=', $endDate);
    }

    /**
     * Sort the attachments by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}