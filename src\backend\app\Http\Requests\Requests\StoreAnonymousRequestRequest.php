<?php

namespace App\Http\Requests\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\RequestSource;
use Lang;

class StoreAnonymousRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|regex:/^(?!.*\d)[a-zِِA-Z\p{Arabic}\s]+$/iu|max:150',
            'phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:'.$this->country_code?? 'AUTO',
            'country_code' => 'required|regex:/^(?!.*\d)[a-zِِA-Z]+$/iu',
            'message_text' => 'required|max:1000',
            'request_source' => 'required|in:'
                .RequestSource::WEBSITE.','. RequestSource::ANDROID_APP.','. RequestSource::IOS_APPS,
        ];
    }

    public function messages()
    {
        return [
            'name.required' => Lang::get('validation.custom.requests.name.required'),
            'name.max' => Lang::get('validation.custom.requests.name.max'),
            'name.regex'  => Lang::get('validation.custom.requests.name.regex'),
            'phone_number.required' => Lang::get('validation.custom.requests.phone_number.required'),
            'phone_number.phone' => Lang::get('validation.custom.requests.phone_number.phone'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
            'message_text.required' => Lang::get('validation.custom.requests.message_text.required'),
            'message_text.max' => Lang::get('validation.custom.requests.message_text.max'),
            'country_code.required' =>  Lang::get('validation.custom.requests.country_code.required'),
            'country_code.regex' =>  Lang::get('validation.custom.requests.country_code.regex'),
            'request_source.required' => Lang::get('validation.custom.requests.request_source.required'),
            'request_source.in' => Lang::get(
                'validation.custom.requests.request_source.in',
                ['in' => RequestSource::WEBSITE.','. RequestSource::ANDROID_APP.','. RequestSource::IOS_APPS]
            ),
        ];
    }
}
