<?php

namespace App\Http\Controllers\Patient;

use App\Http\Resources\BookingAppointments\BookingAppointmentCollection;
use App\Http\Filters\BookingAppointmentFilter;
use App\Services\BookingAppointmentService;
use App\Http\Controllers\Controller;
use App\Models\BookingAppointment;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

class BookingAppointmentController extends Controller
{
 
    private $service;

    public function __construct(BookingAppointmentService $service)
    {
        $this->service = $service;
    }

    public function upcoming()
    {
        return $this->service->upcoming();
    }

    public function index(BookingAppointmentFilter $filter) 
    {
        $result = $this->service->patientFilter($filter);
        return new BookingAppointmentCollection($result);
    }
}
