<?php

namespace App\Http\Resources\SamplingReservations;

use Illuminate\Http\Resources\Json\JsonResource;

class SamplesDashboardStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'total' => $this->total,
            'done' => $this->done,
            'not_done' => $this->not_done,
            'lated' => $this->lated,
        );
    }
}
