<?php

namespace App\Listeners\Appointments;

use App\Jobs\SendPatientAppointmentNotificationJob;
use App\Jobs\SendUserAppointmentNotificationJob;
use App\Events\Appointments\AppointmentDeleted;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Traits\MailTrait;

class NotifyAppointmentDeleted
{
    use MailTrait;

    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Appointments\AppointmentDeleted $event
     * @return void
     */
    public function handle(AppointmentDeleted $event)
    {
        SendUserAppointmentNotificationJob::dispatch($event->appointment, 'delete')
            ->onQueue(config('queue.queues.notifications'));
        SendPatientAppointmentNotificationJob::dispatch($event->appointment, 'delete')
            ->onQueue(config('queue.queues.notifications'));
        $this->sendAppointmentMail($event->appointment, 'delete');
    }
}
