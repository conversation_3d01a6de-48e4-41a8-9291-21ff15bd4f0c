<?php

namespace App\Http\Resources\Tamara;

use Illuminate\Http\Resources\Json\JsonResource;

class InstallmentPaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'status' => $this->status,
            'instalments' => $this->instalments,
            'is_success' => $this->is_success,
            'order_id' => $this->order_id,
            'checkout_id' => $this->checkout_id,
            'checkout_url' => $this->checkout_url,
            'response_code' => $this->response_code,
            'response_description' => $this->response_description,
            'expired_at' => $this->expired_at ? $this->getExpiredAtConvertedAttribute() : null,
            'created_at'  => $this->getCreatedAtConvertedAttribute(),
        );
    }
}
