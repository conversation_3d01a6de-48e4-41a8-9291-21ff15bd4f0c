<?php

namespace App\Listeners\Notes;

use App\Events\Notes\NoteCreated;
use App\Jobs\Notes\SendNoteUsersNotificationJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;

class NotifyNoteCreated
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\Notes\NoteCreated $event
     * @return void
     */
    public function handle(NoteCreated $event)
    {
        SendNoteUsersNotificationJob::dispatch($event->note)
            ->onQueue(config('queue.queues.notifications')); 
    }
}
