<?php

namespace App\Http\Requests\Schedules;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\Day;
use Lang;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'schedules' => 'required|array',
            'schedules.*.day' => 'required|distinct|in:'.implode(',', Day::getValues()),
            'schedules.*.from_hour' => 'required|date_format:H:i',
            'schedules.*.to_hour' => 'required|date_format:H:i|after:schedules.*.from_hour'
        ];
    }

    public function messages()
    {
        return array(
            'schedules.required' => Lang::get('validation.custom.schedules.schedules.required'),
            'schedules.array' => Lang::get('validation.custom.schedules.schedules.array'),
            'schedules.*.day.required' => Lang::get('validation.custom.schedules.day.required'),
            'schedules.*.day.distinct' => Lang::get('validation.custom.schedules.day.distinct'),
            'schedules.*.day.in' => Lang::get(
                'validation.custom.schedules.day.in', ['in' => implode(',', Day::getValues())]
            ),
            'schedules.*.from_hour.required' => Lang::get('validation.custom.schedules.from_hour.required'),
            'schedules.*.from_hour.date_format' => Lang::get(
                'validation.custom.schedules.from_hour.date_format', ['format' => 'H:i']
            ),
            'schedules.*.to_hour.required' => Lang::get('validation.custom.schedules.to_hour.required'),
            'schedules.*.to_hour.date_format' => Lang::get(
                'validation.custom.schedules.to_hour.date_format', ['format' => 'H:i']
            ),
            'schedules.*.to_hour.after' => Lang::get('validation.custom.schedules.to_hour.after'),
        );
    }
}
