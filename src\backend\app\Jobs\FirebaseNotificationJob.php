<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Connectors\FirebaseNotificationConnector;
use App\Models\Patient;
use Lang;

class FirebaseNotificationJob
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $patient, $message, $notification;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patient, $message, $notification)
    {
        $this->patient = $patient;
        $this->message = $message;
        $this->notification = $notification;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->isPatient()) {
            $deviceTokens = $this->patient->fcmTokensArray();
            $this->sendFirebaseNotification($deviceTokens, $this->message);
        }
    }

    private function sendFirebaseNotification($deviceTokens, $message)
    {
        if (count($deviceTokens) == 0) {
            return true;
        }

        $title = Lang::get('messages.genome');
        $data = $this->notificationData();
        FirebaseNotificationConnector::notify($deviceTokens, $title, $message, $data);
    }

    private function notificationData()
    {
        return array(
            'id' => $this->notification->modelId,
            'type' => $this->notification->type,
            'link' => $this->notification->link,
            'request_status' => $this->notification->requestStatus,
            'status' => $this->notification->requestStatus,
        );
    }

    private function isPatient()
    {
        return get_class($this->patient) == Patient::class;
    }
}
