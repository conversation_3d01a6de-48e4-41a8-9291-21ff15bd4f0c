<?php

namespace App\Http\Requests\Patients;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\SocialStatus;
use App\Models\Patient;
use Lang;
use Auth;

class UpdateCurrentPatientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $this->patient = Auth::user();
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'profile_image' => 'nullable|mimes:jpeg,jpg,png|max:'.config('app.profile_max_size'),
            'phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA|unique:patients,phone_number,'. 
                $this->patient->id.',id,deleted_at,NULL',
            'email' => 'nullable|email:rfc,dns',
            'birthday' => 'required|date|date_format:Y-m-d|before_or_equal:today',
            'social_status' => 'required|in:'.implode(',', SocialStatus::getValues()),
            'city_id' => 'required|exists:cities,id,deleted_at,NULL', 
            'address' => 'nullable',
            'is_image_removed' => 'required|boolean',
        );
    }

    public function messages()
    {
        return array_merge(
            $this->mandatoryMessages(),
            $this->optionalMessages(),
        );
    }

    public function mandatoryMessages()
    {
        return array(
            'phone_number.required' => Lang::get('validation.custom.requests.phone_number.required'),
            'phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.invalid'),
            'phone_number.unique' => Lang::get('validation.custom.patient-register.phone_number.unique'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
            'birthday.required' => Lang::get('validation.custom.patient-register.birthday.required'),
            'birthday.date' => Lang::get('validation.custom.patient-register.birthday.date'),
            'birthday.date_format' => Lang::get(
                'validation.custom.patient-register.birthday.date_format', ['format' => 'Y-m-d']
            ),
            'birthday.before_or_equal' => Lang::get('validation.custom.patient-register.birthday.before_or_equal'),
            'social_status.required' => Lang::get('validation.custom.patient-register.social_status.required'),
            'social_status.in' => Lang::get(
                'validation.custom.patient-register.social_status.in', ['in' => implode(',', SocialStatus::getValues())]
            ),
            'city_id.required' => Lang::get('validation.custom.patient-register.city_id.required'),
            'city_id.exists' => Lang::get('validation.custom.patient-register.city_id.exists'),      
            'is_image_removed.required' => Lang::get('validation.custom.patients.is_image_removed.required'),
            'is_image_removed.boolean' => Lang::get('validation.custom.patients.is_image_removed.boolean'),
        );
    }

    public function optionalMessages()
    {
        return array(
            'email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'profile_image.mimes' => Lang::get(
                'validation.custom.profile.profile_image.mimes', ['values' => 'jpeg,jpg,png']
            ),
            'profile_image.max' => Lang::get(
                'validation.custom.profile.profile_image.max', ['max' => config('app.profile_max_size')]
            ),        
        );
    }
}
