<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static OptionOne()
 * @method static static OptionTwo()
 * @method static static OptionThree()
 */
final class Role extends Enum implements LocalizedEnum
{
    const ADMIN =          1;
    const RECEPTIONIST =   2;
    const FOLLOW_UP =      3;
    const SPECIALIST =     4;
    const ACCOUNTANT =     5;
    const CONSULTANT =     6;
    const LABORATORY =     7;
    const HOSPITAL =       8;
}
