<?php

namespace App\Enums;

use BenSampo\Enum\Enum;

/**
 * @method static static CHECKOUT()
 * @method static static GET_PAYMENT()
 * @method static static  WEBHOOK()
 */
final class PaymentRequestType extends Enum
{
    const CHECKOUT =                 'checkout';
    const GET_PAYMENT =              'get_payment';
    const WEBHOOK =                  'webhook';
    const TRANSACTION_REPORT =       'transaction_report';
}
