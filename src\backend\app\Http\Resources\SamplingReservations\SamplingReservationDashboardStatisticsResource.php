<?php

namespace App\Http\Resources\SamplingReservations;

use Illuminate\Http\Resources\Json\JsonResource;

class SamplingReservationDashboardStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'total' => $this->total,
            'done' => $this->done,
            'not_booked' => $this->not_booked,
            'lated' => $this->lated,
        );
    }
}
