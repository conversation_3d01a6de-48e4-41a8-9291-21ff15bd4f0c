<?php

namespace App\Jobs\Appointments;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\TaskService;
use Carbon\Carbon;
use Lang;

class WaitReferenceLabAutoTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $user, $appointment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment, $user)
    {
        $this->appointment = $appointment;
        $this->user = $user;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $patientName = $this->appointment->patient->translate('ar')->name;
        $data = array(
            'description' => Lang::get('translations.appointments.auto_task', ['name' => $patientName], 'ar'),
            'complete_date' => Carbon::now()->addDay(),
        );
        app(TaskService::class)->createModel($data, $this->user);
    }
}
