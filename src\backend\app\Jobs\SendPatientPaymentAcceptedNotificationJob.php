<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Traits\MessageTrait;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Enums\PaymentStatus;
use App\Enums\MessageType;
use Lang;

class SendPatientPaymentAcceptedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        MessageTrait, NotificationTrait;

    protected $payment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->payment->refresh();
        if ($this->payment->isStatus(PaymentStatus::PAID)) {
            $patientLabTestId = $this->payment->patientLabTest->id;
            $message = Lang::get('messages.request_statuses.PAYMENT_CONFIRMED', [], 'ar');
            $notification = $this->notificationObject(
                NotificationType::PATIENT_LAB_TEST, $patientLabTestId, null, RequestStatus::PAYMENT_CONFIRMED
            );
            $this->createMessage($this->payment->patient, $message, MessageType::BOTH, $notification);
        }
    }
}
