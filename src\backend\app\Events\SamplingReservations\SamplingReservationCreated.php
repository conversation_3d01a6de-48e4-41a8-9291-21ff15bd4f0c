<?php

namespace App\Events\SamplingReservations;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SamplingReservationCreated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $samplingReservation, $byUser;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($samplingReservation, $byUser)
    {
        $this->samplingReservation = $samplingReservation;
        $this->byUser = $byUser;
    }
}
