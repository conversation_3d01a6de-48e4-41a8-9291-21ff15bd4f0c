<?php

namespace App\Http\Controllers\Dashboard\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\OtpVerificationService;
use App\Http\Requests\GenerateUserOtpRequest;
use Lang;

class OtpController extends Controller
{
    
    private $otpVerificationService;

    public function __construct(OtpVerificationService $otpVerificationService)
    {
        $this->otpVerificationService = $otpVerificationService;
    }

    public function resendOtpForSystemUser(GenerateUserOtpRequest $request)
    {
        $expiredAt = $this->otpVerificationService->resendOtpForSystemUser($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.otp.success.send'), 'expired_at' => $expiredAt], Response::HTTP_OK
        );
    }
}
