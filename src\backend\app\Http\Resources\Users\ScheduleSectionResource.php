<?php

namespace App\Http\Resources\Users;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class ScheduleSectionResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'from_hour' => Carbon::parse($this->from_hour)->format('H:i'),
            'to_hour' => Carbon::parse($this->to_hour)->format('H:i'),
        );
    }
}
