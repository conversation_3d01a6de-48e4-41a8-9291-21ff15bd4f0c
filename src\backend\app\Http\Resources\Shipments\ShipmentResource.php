<?php

namespace App\Http\Resources\Shipments;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\ShippingCompany;
use App\Enums\ShipmentStatus;
use Lang;

class ShipmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'pickup_location_id' => $this->pickup_location_id,
            'delivery_location_id' => $this->delivery_location_id,
            'shipping_company' => $this->shipping_company,
            'shipping_company_name' => ShippingCompany::getDescription($this->shipping_company),
            'status' => $this->status,
            'status_name' => ShipmentStatus::getDescription($this->status),
            'notes' => $this->notes,
        );
    }
}
