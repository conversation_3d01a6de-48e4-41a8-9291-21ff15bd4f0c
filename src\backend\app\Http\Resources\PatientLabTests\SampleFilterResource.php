<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\PatientLabTest;
use App\Enums\LabTestStatus;
use App\Enums\TubeType;
use Carbon\Carbon;
use Lang;

class SampleFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'patient_id' => $this->patient_id,
            'patient_name' => $this->patient->name,
            'lab_test_id' => $this->lab_test_id,
            'lab_test_name' => $this->name?? $this->labTest->name,
            'sampling_date' => $this->samplingReservation->date_converted?? null,
            'agency_id' => $this->samplingReservation->agency_id?? null,
            'agency_name' => $this->samplingReservation->agency->name?? null,
            'sample_type' => $this->sample_type,
            'tube_type' => $this->tube_type,
            'tube_type_name' => TubeType::getDescription($this->tube_type),
            'tubes_count' => $this->tubes_count,
            'status_name' => $this->getStatus(),
            'sample_quantity' => $this->sample_quantity,
        );
    }

    public function getStatus()
    {
        switch (true) {
        case in_array($this->status, PatientLabTest::$sampleDepositeStatus):
            return Lang::get('translations.patient_lab_test_status.sample_deposited');
                break;
        case $this->status == LabTestStatus::SAMPLE_NOT_DEPOSITED:
            return Lang::get('translations.patient_lab_test_status.lated');
                break;
        case $this->isLate():
            return Lang::get('translations.patient_lab_test_status.lated');
                break;
        case $this->isNotLated():
            return Lang::get('translations.patient_lab_test_status.not_sample_deposited');
                break;
        default:
            return LabTestStatus::getDescription($this->status);
                    break;
        }
    }

    public function isNotLated()
    {
        return $this->status == LabTestStatus::SAMPLING_RESERVATION && 
            $this->samplingReservation->date->gte(Carbon::now()->subHours(24));
    }

    public function isLate()
    {
        return $this->status == LabTestStatus::SAMPLING_RESERVATION && 
            $this->samplingReservation->date->lt(Carbon::now()->subHours(24));
    }
}
