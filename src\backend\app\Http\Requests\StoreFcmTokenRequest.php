<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class StoreFcmTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'fcm_token' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'fcm_token.required' => Lang::get('validation.custom.fcm-tokens.fcm_token.required'),
            'fcm_token.unique' => Lang::get('validation.custom.fcm-tokens.fcm_token.unique'),
        ];
    }
}
