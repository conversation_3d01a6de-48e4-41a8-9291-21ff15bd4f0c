<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\User;

class UserFilter extends Filter
{
    public $internalUser = -1;
    public $usersHaveSchedules = -2;
    public $fields = array('username', 'user_code', 'role_id', 'blocked_at',
        'created_at', 'email', 'phone_number', 'updated_at');

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->where(
            function ($query) use ($search) {
                $query->where('phone_number', 'like', '%'.$search.'%')
                    ->orWhere('username', 'like', '%'.$search.'%')
                    ->orWhere('email', 'like', '%'.$search.'%')
                    ->orWhereHas(
                        'userTranslations', function (Builder $query) use ($search) {
                            $query->where('name', 'like', '%'.$search.'%');
                        }
                    );
            }
        );
    }

    /**
     * @param string $roleId
     */
    public function roleId(string $roleId)
    {
        $this->builder->when(
            $roleId == $this->internalUser, function ($query) {
                return $query->whereIn('role_id', User::internalUsersRoles());
            }
        )->when(
            $roleId == $this->usersHaveSchedules, function ($query) {
                return $query->whereIn('role_id', User::usersHaveSchedules());
            }
        )
        ->when(
            $roleId > 0, function ($query) use ($roleId) {
                return $query->where('role_id', $roleId);
            }
        );
    }

    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}