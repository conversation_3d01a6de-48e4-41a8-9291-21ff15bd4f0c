<?php

namespace App\Http\Connectors;

use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Http\Response;
use GuzzleHttp\Client;
use Exception;
use Lang;

class MicrosoftGraphConnector
{
    private static function sendRequest($baseUri, $method, $url, $data, $headers, $code, $dataType = 'json')
    {
        $body = array();
        $body['headers'] = $headers;
        $body[$dataType] = $data?? array();
        $body['http_errors'] = false;
        
        $client = new Client(['base_uri' => $baseUri]);
        $clientResponse = $client->request($method, $url, $body);
        $response = self::getResponse($clientResponse, $code, $data);

        if (!$response['is_success']) {
            throw new Exception($response['response']['error']['message']?? $response['response']['error']);
        }

        return  $response['response'];
    }

    private static function getResponse($response, $statusCode)
    {
        $jsonResponse = json_decode($response->getBody()->getContents(), true);
        $responseCode = $response->getStatusCode();
        $isSuccess = $statusCode == $responseCode;

        return array('is_success' => $isSuccess,'response' => $jsonResponse);
    }

    private static function getHeaders($clientId, $clientSecret, $tenantId)
    {
        $token = self::token($clientId, $clientSecret, $tenantId);
        return array(
            'content-type' => 'application/json',
            'Authorization' => $token
        );
    }

    public static function token($clientId, $clientSecret, $tenantId)
    {
        $baseUri = config('microsoft_graph.tokenBaseURL');
        $url = $tenantId .config('microsoft_graph.tokenEndPoint');
        $headers = array('content-type' => 'application/x-www-form-urlencoded');
        $data = array(
            'client_id' => $clientId,
            'grant_type' => config('microsoft_graph.grantType'),
            'client_secret' => $clientSecret,
            'scope' => config('microsoft_graph.scope'),
        );

        $response = self::sendRequest($baseUri, 'POST', $url, $data, $headers, Response::HTTP_OK, 'form_params');
        return $response['token_type'].' '.$response['access_token'];
    }

    public static function createMeeting($application, $startDate, $endDate, $subject)
    {
        $headers = self::getHeaders($application->client_id, $application->client_secrete, $application->tenant_id);
        $baseUri = config('microsoft_graph.apiBaseURL');
        $url = 'users/'.$application->user_object_id.'/onlineMeetings';
        $data = self::meetingData($startDate, $endDate, $subject);

        return self::sendRequest($baseUri, 'POST', $url, $data, $headers, Response::HTTP_CREATED);
    }

    public static function getMeeting($application, $meetingId)
    {
        $headers = self::getHeaders($application->client_id, $application->client_secrete, $application->tenant_id);
        $baseUri = config('microsoft_graph.apiBaseURL');
        $url = 'users/'.$application->user_object_id.'/onlineMeetings/'. $meetingId;

        return self::sendRequest($baseUri, 'GET', $url, null, $headers, Response::HTTP_OK);
    }

    public static function updateMeeting($application, $startDate, $endDate, $subject, $meetingId)
    {
        $headers = self::getHeaders($application->client_id, $application->client_secrete, $application->tenant_id);
        $baseUri = config('microsoft_graph.apiBaseURL');
        $url = 'users/'.$application->user_object_id.'/onlineMeetings/'. $meetingId;
        $data = self::meetingData($startDate, $endDate, $subject);

        return self::sendRequest($baseUri, 'PATCH', $url, $data, $headers, Response::HTTP_OK);
    }

    public static function deleteMeeting($application, $meetingId)
    {
        $headers = self::getHeaders($application->client_id, $application->client_secrete, $application->tenant_id);
        $baseUri = config('microsoft_graph.apiBaseURL');
        $url = 'users/'.$application->user_object_id.'/onlineMeetings/'. $meetingId;

        return self::sendRequest($baseUri, 'DELETE', $url, null, $headers, Response::HTTP_NO_CONTENT);
    }

    public static function getChat($application, $chatId)
    {
        $headers = self::getHeaders($application->client_id, $application->client_secrete, $application->tenant_id);
        $baseUri = config('microsoft_graph.apiBaseURL');
        $url = 'users/'.$application->user_object_id.'/chats/'. $chatId.'/messages';

        return self::sendRequest($baseUri, 'GET', $url, null, $headers, Response::HTTP_OK);
    }

    public static function getRootDriveItems($application)
    {
        $headers = self::getHeaders($application->client_id, $application->client_secrete, $application->tenant_id);
        $baseUri = config('microsoft_graph.apiBaseURL');
        $url = 'users/'.$application->user_object_id.'/drive/root/children';

        return self::sendRequest($baseUri, 'GET', $url, null, $headers, Response::HTTP_OK);
    }

    public static function getItemChildren($application, $itemId)
    {
        $headers = self::getHeaders($application->client_id, $application->client_secrete, $application->tenant_id);
        $baseUri = config('microsoft_graph.apiBaseURL');
        $url = 'users/'.$application->user_object_id.'/drive/items/'.$itemId.'/children';

        return self::sendRequest($baseUri, 'GET', $url, null, $headers, Response::HTTP_OK);
    }

    public static function createSharingLink($application, $itemId)
    {
        $headers = self::getHeaders($application->client_id, $application->client_secrete, $application->tenant_id);
        $baseUri = config('microsoft_graph.apiBaseURL');
        $url = 'users/'.$application->user_object_id.'/drive/items/'.$itemId.'/createLink';        
        $data =self::sharingLinkData();

        return self::sendRequest($baseUri, 'POST', $url, $data, $headers, Response::HTTP_CREATED);
    }

    private static function meetingData($startDate, $endDate, $subject)
    {
        return array(
            'startDateTime' => $startDate,
            'endDateTime' => $endDate,
            'subject' => $subject,
            'recordAutomatically' => true,
        );
    }

    private static function sharingLinkData()
    {
        return array(
            'type' => config('microsoft_graph.sharingLinkType'),
            'scope' => config('microsoft_graph.sharingLinkScope'),
        );
    }
}
