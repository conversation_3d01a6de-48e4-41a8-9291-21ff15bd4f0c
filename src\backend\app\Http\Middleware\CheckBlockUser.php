<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\UserService;
use App\Services\PatientService;
use App\Services\AgencyService;
use Carbon\Carbon;
use Auth;
use Lang;

class CheckBlockUser
{
    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $guard)
    {
        $user = call_user_func(array($this, $guard), $request);
        if (!$user) {
            return $next($request);
        }

        if ($user->blocked_at) {
            $this->returnBlockedError($user);
        }
        
        return $next($request);
    }

    public function user($request)
    {
        return Auth::user()?? app(UserService::class)->findBy('username', $request['username']?? '');
    }

    public function patient($request)
    {
        $column = $request['phone_number']? 'phone_number' : 'identification_number';
        $value = $request['phone_number']? $request['phone_number'] : ($request['username']?? '');
        return Auth::user()?? app(PatientService::class)->findBy($column, $value);    
    }

    public function agency($request)
    {
        return Auth::user()?? app(AgencyService::class)->findBy('username', $request['username']?? '');
    }

    public function returnBlockedError($user)
    {
        if ($user->blocked_by_admin) {
            abort(Response::HTTP_FORBIDDEN, Lang::get('messages.blocked_by_admin'));
        }
        $hours = $user->blocked_at->addMinutes(config('app.otp_block_time'))->diffForHumans(Carbon::now());
        abort(Response::HTTP_FORBIDDEN, Lang::get('messages.blocked', ['hours' => $hours]));
    }
}
