<?php

namespace App\Http\Requests\Patients;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\PatientSource;
use App\Enums\GenderType;
use App\Enums\SocialStatus;
use App\Enums\ContactMethod;
use App\Enums\PatientType;
use App\Enums\TestType;
use App\Enums\ParentsRelationship;
use App\Models\Patient;
use Lang;
use Auth;

class UpdatePatientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('update', Patient::class);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array_merge(
            $this->mainInformationRules(),
            $this->personalInformationRules(),
        );
    }

    public function mainInformationRules()
    {
        return [
            'identification_number' => 'required|min:10|max:20|unique:patients,identification_number,'. 
                $this->patient->id.',id,deleted_at,NULL',
            'source' => 'required|in:'.implode(',', PatientSource::getValues()),
            'name.ar' => 'required|min:3|max:150|regex:/^(?!.*\d)[\p{Arabic}\s]+$/iu',
            'name.en' => 'required|min:3|max:150|regex:/^(?!.*\d)[a-zِِA-Z\s]+$/iu',
            'phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA|unique:patients,phone_number,'. 
                $this->patient->id.',id,deleted_at,NULL',
            'email' => 'nullable|email:rfc,dns',
            'program_id' => 'required|exists:programs,id,deleted_at,NULL',
            'contact_method' => 'required|in:'.implode(',', ContactMethod::getValues()),
            'patient_type' => 'required|in:'.implode(',', PatientType::getValues()),
            'test_type' => 'required|in:'.implode(',', TestType::getValues()),
            'first_name' => 'required|min:3|max:150|regex:/^(?!.*\d)[\p{Arabic}\s]+$/iu',
            'is_saudi' => 'required|boolean'
        ];
    }

    public function personalInformationRules()
    {
        return [
            'birthday' => 'required|date|date_format:Y-m-d|before_or_equal:today',
            'gender_type' => 'required|in:'.implode(',', GenderType::getValues()),
            'social_status' => 'required|in:'.implode(',', SocialStatus::getValues()),
            'city_id' => 'required|exists:cities,id,deleted_at,NULL', 
            'birthplace_id' => 'nullable|required_if:is_saudi,true|exists:cities,id,deleted_at,NULL',
            'address' => 'nullable',
            'has_family_history' => 'required|boolean',
            'father_family_id' => 'nullable|required_without:father_family_name|exists:families,id,deleted_at,NULL',
            'mother_family_id' => 'nullable|required_without:mother_family_name|exists:families,id,deleted_at,NULL',
            'parents_relationship' => 'required|in:'.implode(',', ParentsRelationship::getValues()),
            'father_family_name' => 'nullable|required_without:father_family_id|min:3|max:150|unique:families,name,NULL
            ,id,deleted_at,NULL',
            'mother_family_name' => 'nullable|required_without:mother_family_id|min:3|max:150|unique:families,name,NULL
            ,id,deleted_at,NULL',
            'origin_city_id' => 'nullable|required_if:is_saudi,true|exists:cities,id,deleted_at,NULL',
            'with_contacts' => 'required|boolean',
            'contacts' => 'nullable|array',
            'contacts.*.name.ar' => 'required|min:3|max:150|regex:/^(?!.*\d)[\p{Arabic}\s]+$/iu',
            'contacts.*.name.en' => 'required|min:3|max:150|regex:/^(?!.*\d)[a-zِِA-Z\s]+$/iu',
            'contacts.*.relation' => 'required',
            'contacts.*.reason_of_contact' => 'required',
            'contacts.*.phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA',
            'contacts.*.email' => 'nullable|email:rfc,dns',
        ];
    }

    public function messages()
    {
        return array_merge(
            $this->mainInformationMessages(),
            $this->personalInformationMessages(),
            $this->extraInformationMessages(),
            $this->familyInformationMessages(),
            $this->contactsDataMessages(),
        );
    }

    public function mainInformationMessages()
    {
        return [
            'name.ar.required' => Lang::get('validation.custom.patient-profile.name.ar.required'),
            'name.ar.min' => Lang::get('validation.custom.patient-profile.name.ar.min', ['min' => 3]),
            'name.ar.max' => Lang::get('validation.custom.patient-profile.name.ar.max', ['max' => 150]),
            'name.ar.regex' => Lang::get('validation.custom.patient-profile.name.ar.regex'),
            'name.en.required' => Lang::get('validation.custom.patient-profile.name.en.required'),
            'name.en.min' => Lang::get('validation.custom.patient-profile.name.en.min', ['min' => 3]),
            'name.en.max' => Lang::get('validation.custom.patient-profile.name.en.max', ['max' => 150]),
            'name.en.regex' => Lang::get('validation.custom.patient-profile.name.en.regex'),
            'identification_number.required' => Lang::get(
                'validation.custom.patient-register.identification_number.required'
            ),
            'identification_number.unique' => Lang::get(
                'validation.custom.patient-register.identification_number.unique'
            ),
            'identification_number.max' => Lang::get(
                'validation.custom.patient-register.identification_number.max', ['max' => 20]
            ),
            'identification_number.min' => Lang::get(
                'validation.custom.patient-register.identification_number.min', ['min' => 10]
            ),
            'phone_number.required' => Lang::get('validation.custom.requests.phone_number.required'),
            'phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.invalid'),
            'phone_number.unique' => Lang::get('validation.custom.patient-register.phone_number.unique'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
        ];
    }

    public function personalInformationMessages()
    {
        return [
            'first_name.required' => Lang::get('validation.custom.patient-profile.first_name.required'),
            'first_name.min' => Lang::get('validation.custom.patient-profile.first_name.min', ['min' => 3]),
            'first_name.max' => Lang::get('validation.custom.patient-profile.first_name.max', ['max' => 150]),
            'first_name.regex' => Lang::get('validation.custom.patient-profile.first_name.regex'),
            'email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'birthday.required' => Lang::get('validation.custom.patient-register.birthday.required'),
            'birthday.date' => Lang::get('validation.custom.patient-register.birthday.date'),
            'birthday.date_format' => Lang::get(
                'validation.custom.patient-register.birthday.date_format', ['format' => 'Y-m-d']
            ),
            'gender_type.required' => Lang::get('validation.custom.patient-register.gender_type.required'),
            'gender_type.in' => Lang::get(
                'validation.custom.patient-register.gender_type.in', ['in' => implode(',', GenderType::getValues())]
            ),
            'social_status.required' => Lang::get('validation.custom.patient-register.social_status.required'),
            'social_status.in' => Lang::get(
                'validation.custom.patient-register.social_status.in', ['in' => implode(',', SocialStatus::getValues())]
            ),
            'city_id.required' => Lang::get('validation.custom.patient-register.city_id.required'),
            'city_id.exists' => Lang::get('validation.custom.patient-register.city_id.exists'),
            'birthplace_id.required_if' => Lang::get('validation.custom.patient-profile.birthplace_id.required'),
            'birthplace_id.exists' => Lang::get('validation.custom.patient-profile.birthplace_id.exists'),
            'address.required' => Lang::get('validation.custom.patient-profile.address.required'),
            'mother_family_name.unique' => Lang::get('validation.custom.families.name.unique'),
        ];
    }

    public function extraInformationMessages()
    {
        return [
            'source.required' => Lang::get('validation.custom.patient-profile.source.required'),
            'source.in' => Lang::get(
                'validation.custom.patient-profile.source.in', ['in' => implode(',', PatientSource::getValues())]
            ),
            'program_id.required' => Lang::get('validation.custom.patient-profile.program_id.required'),
            'program_id.exists' => Lang::get('validation.custom.patient-profile.program_id.exists'),
            'contact_method.required' => Lang::get('validation.custom.patient-profile.contact_method.required'),
            'contact_method.in' => Lang::get(
                'validation.custom.patient-profile.contact_method.in', 
                ['in' => implode(',', ContactMethod::getValues())]
            ),
            'patient_type.required' => Lang::get('validation.custom.patient-profile.patient_type.required'),
            'patient_type.in' => Lang::get(
                'validation.custom.patient-profile.patient_type.in', ['in' => implode(',', PatientType::getValues())]
            ),
            'test_type.required' => Lang::get('validation.custom.patient-profile.test_type.required'),
            'test_type.in' => Lang::get(
                'validation.custom.patient-profile.test_type.in',
                ['in' => implode(',', TestType::getValues())]
            ),
            'has_family_history.required' => Lang::get('validation.custom.patient-profile.has_family_history.required'),
            'has_family_history.boolean' => Lang::get('validation.custom.patient-profile.has_family_history.boolean'),
            'father_family_id.exists' => Lang::get('validation.custom.patient-profile.father_family_id.exists'),
            'mother_family_id.exists' => Lang::get('validation.custom.patient-profile.mother_family_id.exists'),
        ];
    }

    public function familyInformationMessages()
    {
        return array(
            'father_family_id.required_without' => Lang::get(
                'validation.custom.patient-profile.father_family_id.required_without'
            ),
            'mother_family_id.required_without' => Lang::get(
                'validation.custom.patient-profile.mother_family_id.required_without'
            ),
            'parents_relationship.required' => Lang::get(
                'validation.custom.patient-profile.parents_relationship.required'
            ),
            'parents_relationship.in' => Lang::get(
                'validation.custom.patient-profile.parents_relationship.in',
                ['in' => implode(',', ParentsRelationship::getValues())]
            ),
            'father_family_name.required_without' => Lang::get(
                'validation.custom.patient-profile.father_family_name.required_without'
            ),
            'father_family_name.min' => Lang::get('validation.custom.families.name.min', ['min' => 3]),
            'father_family_name.max' => Lang::get('validation.custom.families.name.max', ['max' => 150]),
            'father_family_name.unique' => Lang::get('validation.custom.families.name.unique'),
            'mother_family_name.required_without' => Lang::get(
                'validation.custom.patient-profile.mother_family_name.required_without'
            ),
            'mother_family_name.min' => Lang::get('validation.custom.families.name.min', ['min' => 3]),
            'mother_family_name.max' => Lang::get('validation.custom.families.name.max', ['max' => 150]),
        );
    }

    public function contactsDataMessages()
    {
        return array(
            'origin_city_id.required_if' => Lang::get('validation.custom.patient-profile.origin_city_id.required'),
            'origin_city_id.exists' => Lang::get('validation.custom.patient-profile.origin_city_id.exists'),
            'contacts.array' => Lang::get('validation.custom.patient-profile.contacts.array'),
            'contacts.*.email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'contacts.*.phone_number.required' => Lang::get('validation.custom.patient-login.phone_number.required'),
            'contacts.*.phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
            'contacts.*.phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.invalid'),
            'contacts.*.reason_of_contact.required' => Lang::get(
                'validation.custom.patient-profile.contacts.reason_of_contact.required'
            ),
            'contacts.*.relation.required' => Lang::get('validation.custom.patient-profile.contacts.relation.required'),
            'contacts.*.name.ar.required' => Lang::get('validation.custom.users.name.ar.required'),
            'contacts.*.name.ar.min' => Lang::get('validation.custom.users.name.ar.min', ['min' => 3]),
            'contacts.*.name.ar.max' => Lang::get('validation.custom.users.name.ar.max', ['max' => 150]),
            'contacts.*.name.ar.regex' => Lang::get('validation.custom.users.name.ar.regex'),
            'contacts.*.name.en.required' => Lang::get('validation.custom.users.name.en.required'),
            'contacts.*.name.en.min' => Lang::get('validation.custom.users.name.en.min', ['min' => 3]),
            'contacts.*.name.en.max' => Lang::get('validation.custom.users.name.en.max', ['max' => 150]),
            'contacts.*.name.en.regex' => Lang::get('validation.custom.users.name.en.regex'),
            'with_contacts.required' => Lang::get('validation.custom.patient-profile.with_contacts.required'),
            'with_contacts.boolean' => Lang::get('validation.custom.patient-profile.with_contacts.boolean'),
            'birthday.before_or_equal' => Lang::get('validation.custom.patient-register.birthday.before_or_equal'),
            'is_saudi.required' => Lang::get('validation.custom.patient-profile.is_saudi.required'),
            'is_saudi.boolean' => Lang::get('validation.custom.patient-profile.is_saudi.boolean'),
        );
    }
}
