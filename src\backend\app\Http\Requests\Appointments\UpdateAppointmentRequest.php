<?php

namespace App\Http\Requests\Appointments;

use Illuminate\Foundation\Http\FormRequest;
use App\Services\TimeZoneConverter;
use App\Rules\NonOverlappingAppointment;
use App\Rules\CanCreateOnlineMeeting;
use App\Rules\PatientHasRequest;
use App\Rules\HasSchedules;
use App\Enums\AppointmentType;
use App\Enums\AppointmentPurpose;
use Carbon\Carbon;
use Lang;
use Auth;

class UpdateAppointmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        if (!$this->patient_id) {
            return true;
        }
        return $user && $user->can('update', [$this->appointment, $this->patient_id]); 
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'patient_id' => ['required', 'exists:patients,id,deleted_at,NULL',
                new NonOverlappingAppointment($this->start_date, 'patient', $this->appointment)],
            'user_id' => ['required', 'exists:users,id,deleted_at,NULL', new HasSchedules($this->start_date),
                new NonOverlappingAppointment($this->start_date, 'user', $this->appointment)],
            'type' => ['required', 'in:'. implode(',', AppointmentType::getValues()),
                new CanCreateOnlineMeeting($this->start_date, $this->appointment)],
            'purpose' => ['required', 'in:'. implode(',', AppointmentPurpose::getValues()),
                new PatientHasRequest($this->appointment->patient_id, $this->appointment)],
            'start_date' => 'required|date|date_format:Y-m-d H:i:s|after:'.
                TimeZoneConverter::convertFromUtc(Carbon::now()->startOfMinute()),
        ];
    }

    public function messages()
    {
        return [
            'patient_id.required' => Lang::get('validation.custom.appointments.patient_id.required'),
            'patient_id.exists' => Lang::get('validation.custom.appointments.patient_id.exists'),
            'user_id.required' => Lang::get('validation.custom.appointments.user_id.required'),
            'user_id.exists' => Lang::get('validation.custom.appointments.user_id.exists'),
            'type.required' => Lang::get('validation.custom.appointments.type.required'),
            'type.in' => Lang::get(
                'validation.custom.appointments.type.in', ['in' => implode(',', AppointmentType::getValues())]
            ),
            'purpose.required' => Lang::get('validation.custom.appointments.purpose.required'),
            'purpose.in' => Lang::get(
                'validation.custom.appointments.purpose.in', ['in' => implode(',', AppointmentPurpose::getValues())]
            ),
            'start_date.required' => Lang::get('validation.custom.appointments.start_date.required'),
            'start_date.date' => Lang::get('validation.custom.appointments.start_date.date'),
            'start_date.date_format' => Lang::get(
                'validation.custom.appointments.start_date.date_format', ['format' => 'Y-m-d H:i:s']
            ),
            'start_date.after' => Lang::get('validation.custom.appointments.start_date.after'),
        ];
    }
}
