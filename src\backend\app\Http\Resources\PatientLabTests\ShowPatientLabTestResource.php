<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Http\Resources\OnlinePaymentResource;
use App\Enums\SamplingReservationType;
use App\Enums\LabTestStatus;
use App\Enums\RequestStatus;
use App\Enums\PaymentStatus;
use App\Enums\PaymentMethod;
use App\Enums\RequestStage;
use App\Traits\PaymentTrait;
use Lang;

class ShowPatientLabTestResource extends JsonResource
{
    use PaymentTrait;
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'lab_test_id' => $this->lab_test_id,
            'lab_test_name' => $this->name?? $this->labTest->name,
            'lab_test_price' => $this->payment->price?? $this->labTest->price_after_discount,
            'notes' => $this->notes,
            'reference_lab' => array(
                'id' => $this->labTest->referenceLab->id,
                'ar' => $this->labTest->referenceLab->translate('ar')->name?? null,
                'en' => $this->labTest->referenceLab->translate('en')->name?? null,
            ),
            'status' => $this->status,
            'status_name' => LabTestStatus::getDescription($this->status),
            'request_status' => $this->request->status(),
            'request_status_name' => RequestStatus::getDescription($this->request->status()),
            'request_stage' => $this->request->stage,
            'request_stage_name' => RequestStage::getDescription($this->request->stage),
            'is_approved' => $this->isApproved(),
            'payment_status' => $this->payment->status?? PaymentStatus::NOT_PAID,
            'payment_status_name' => Lang::get('translations.payment_statuses.'. PaymentStatus::getKey($this->payment->status?? PaymentStatus::NOT_PAID)),
            'receive_result_after' => $this->sample_date? $this->receive_result_after : 0, 
            'payment' => $this->payment? $this->paymentData() : null,
            'samplingReservation' => $this->samplingReservation?  $this->samplingReservationData() : null,
            'force_deposited' => $this->force_deposited,
            'patient_city_id' => $this->patient->city_id,
            'patient_city_name' => Lang::get($this->patient->city->name),
            'patient_region_id' => $this->patient->city->region_id,
            'patient_region_name' => Lang::get($this->patient->city->region->name),
        );
    }

    public function paymentData()
    {
        return array(
            'id' => $this->payment->id,
            'status' => $this->payment->status,
            'price' => $this->payment->price,
            'payment_method' => $this->payment->payment_method,
            'created_at' => $this->payment->created_at_converted,
            'payment_method_name' => PaymentMethod::getDescription($this->payment->payment_method),
            'paymentable' => $this->payment->paymentable?
                $this->getPaymentableResource($this->payment->payment_method, $this->payment->paymentable) : null,
        );
    }

    public function samplingReservationData()
    {
        return array(
            'id' => $this->samplingReservation->id,
            'agency_id' => $this->samplingReservation->agency_id,
            'agency_name' => $this->samplingReservation->agency->name,
            'map_link' => $this->samplingReservation->agency->map_link,
            'city_id' => $this->samplingReservation->agency->city_id,
            'city_name' => Lang::get($this->samplingReservation->agency->city->name),
            'region_id' => $this->samplingReservation->agency->city->region_id,
            'region_name' => Lang::get($this->samplingReservation->agency->city->region->name),
            'status' => $this->samplingReservation->status,
            'type' => $this->samplingReservation->type,
            'type_name' => SamplingReservationType::getDescription($this->samplingReservation->type),
            'date' => $this->samplingReservation->date_converted,
        );
    }
}
