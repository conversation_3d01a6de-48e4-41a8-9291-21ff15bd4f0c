<?php

namespace App\Http\Requests\Auth\Agency;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class AgencyAuthRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'username' => 'required|exists:agencies,username,deleted_at,NULL|max:50',
            'password' => 'required',
        ];
    }


    public function messages()
    {
        return [
            'username.required' => Lang::get('validation.custom.agencies.username.required'),
            'username.exists' => Lang::get('validation.custom.agencies.username.exists'),
            'username.max' => Lang::get('validation.custom.agencies.username.max', ['max' => 50]),
            'password.required'  => Lang::get('validation.custom.login.password.required'),
        ];
    }
}
