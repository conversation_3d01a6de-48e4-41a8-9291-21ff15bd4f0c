<?php

namespace App\Http\Controllers\Patient;

use App\Http\Controllers\Controller;
use App\Http\Filters\ObligationFilter;
use App\Http\Resources\ObligationCollection;
use App\Services\ObligationService;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lang;

class ObligationController extends Controller
{
    private $obligationService;

    public function __construct(ObligationService $obligationService)
    {
        $this->obligationService = $obligationService;
    }

    public function index(ObligationFilter $filter)
    {
        $result = $this->obligationService->patientFilter($filter);
        return new ObligationCollection($result);
    }
}
