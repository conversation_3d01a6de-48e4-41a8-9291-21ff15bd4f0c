<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\TaskService;

class ChangeTaskStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $task, $fromStatus, $toStatus;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($task, $fromStatus, $toStatus)
    {
        $this->task = $task;
        $this->fromStatus = $fromStatus;
        $this->toStatus = $toStatus;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->task->refresh();
        if (!$this->task->trashed()) {
            app(TaskService::class)->changeStatus($this->task, $this->fromStatus, $this->toStatus);
        }
    }
}
