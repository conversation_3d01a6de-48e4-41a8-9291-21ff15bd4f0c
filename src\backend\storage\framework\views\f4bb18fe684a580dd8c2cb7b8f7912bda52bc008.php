<?php $__env->startSection('content'); ?>
    <!-- Header -->
    <div class="header">
        <div class="logo">
            <img src="<?php echo e(public_path('img/logo.png')); ?>" alt="Genome Logo" class="logo">
        </div>
        <div class="header-title">
            <h1>تقرير نتائج الفحص الوراثي</h1>
            <h2>Genetic Test Results Report</h2>
        </div>
        <div class="report-info">
            <strong>رقم التقرير: <?php echo e($data['report_id']); ?></strong><br>
            <strong>التاريخ: <?php echo e($data['date']); ?></strong>
        </div>
    </div>

    <!-- Patient Information -->
    <div class="patient-info">
        <table>
            <tr>
                <td class="label">اسم المريض / Patient Name:</td>
                <td class="value"><?php echo e($data['patient']['name']); ?></td>
                <td class="label">رقم المريض / Patient ID:</td>
                <td class="value"><?php echo e($data['patient']['id']); ?></td>
            </tr>
            <tr>
                <td class="label">العمر / Age:</td>
                <td class="value"><?php echo e($data['patient']['age']); ?></td>
                <td class="label">الجنس / Gender:</td>
                <td class="value"><?php echo e($data['patient']['gender']); ?></td>
            </tr>
            <tr>
                <td class="label">نوع الفحص / Test Type:</td>
                <td class="value"><?php echo e($data['lab_test']['name']); ?></td>
                <td class="label">رمز الفحص / Test Code:</td>
                <td class="value"><?php echo e($data['lab_test']['code']); ?></td>
            </tr>
        </table>
    </div>

    <!-- General Introduction -->
    <?php if($data['general_introduction']): ?>
    <div class="section">
        <div class="section-title">المقدمة العامة / General Introduction</div>
        <div class="introduction">
            <?php echo e($data['general_introduction']); ?>

        </div>
    </div>
    <?php endif; ?>

    <!-- Test Results by Categories -->
    <div class="section">
        <div class="section-title">تفاصيل النتائج في الجدول التالي / Test Results Details</div>
        
        <?php $__currentLoopData = $data['categories']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <div class="category">
            <div class="category-header">
                فئة النتائج <?php echo e($loop->iteration); ?> / Results Category <?php echo e($loop->iteration); ?>

            </div>
            <div class="category-content">
                <?php if($category['introduction']): ?>
                <div class="introduction">
                    <strong>المقدمة / Introduction:</strong><br>
                    <?php echo e($category['introduction']); ?>

                </div>
                <?php endif; ?>

                <?php if(count($category['genes']) > 0): ?>
                <table class="genes-table">
                    <thead>
                        <tr>
                            <th>الجين<br>Gene</th>
                            <th>نمط الوراثة<br>Inheritance Pattern</th>
                            <th>المرض<br>Disease</th>
                            <th>تصنيف الطفرة الوراثية<br>Genetic Risk Classification</th>
                            <th>تفاصيل الطفرة الوراثية<br>Genetic Variant Details</th>
                            <th>فئة الجين<br>Gene Category</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php $__currentLoopData = $category['genes']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $gene): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <tr>
                            <td><?php echo e($gene['gene_name']); ?></td>
                            <td><?php echo e($gene['inheritance_pattern']); ?></td>
                            <td><?php echo e($gene['disease']); ?></td>
                            <td><?php echo e($gene['genetic_risk']); ?></td>
                            <td><?php echo e($gene['mutation_site']); ?></td>
                            <td><?php echo e($gene['gene_category']); ?></td>
                        </tr>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </tbody>
                </table>
                <?php endif; ?>

                <?php if($category['conclusion']): ?>
                <div class="conclusion">
                    <strong>الخلاصة / Conclusion:</strong><br>
                    <?php echo e($category['conclusion']); ?>

                </div>
                <?php endif; ?>
            </div>
        </div>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </div>

    <!-- General Conclusion -->
    <?php if($data['general_conclusion']): ?>
    <div class="section">
        <div class="section-title">الخلاصة العامة / General Conclusion</div>
        <div class="conclusion">
            <?php echo e($data['general_conclusion']); ?>

        </div>
    </div>
    <?php endif; ?>

    <!-- Footer -->
    <div class="footer">
        <div>
            <strong>جينوم للفحوصات الوراثية</strong><br>
            <strong>Genome for Genetic Testing</strong>
        </div>
        <div class="footer-contact">
            المملكة العربية السعودية 9665584442994+هاتف<br>
            Dammam,Email: <EMAIL>, web:SaudiGenome.net
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.test-result-report-pdf', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\saudigenome-Backend\src\backend\resources\views/pdf/test-result-report.blade.php ENDPATH**/ ?>