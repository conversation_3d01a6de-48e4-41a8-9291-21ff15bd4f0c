<?php

namespace App\Enums;

use BenSampo\Enum\Enum;
use BenSampo\Enum\Contracts\LocalizedEnum;

/**
 * @method static static APPOINTMENT()
 */
final class ReminderType extends Enum implements LocalizedEnum
{
    const APPOINTMENT =             1;
    const OBLIGATION =              2;
    const UNPAID_PAYMENT =          3;
    const PENDING_PAYMENT =         4;
    const SAMPLING_RESERVATION =    5;
}
