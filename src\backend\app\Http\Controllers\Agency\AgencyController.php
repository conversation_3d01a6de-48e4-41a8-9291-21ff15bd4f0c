<?php

namespace App\Http\Controllers\Agency;

use App\Http\Resources\Agencies\AgencyResource;
use App\Http\Controllers\Controller;
use App\Services\AgencyService;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class AgencyController extends Controller
{
    private $agencyService;

    public function __construct(AgencyService $agencyService)
    {
        $this->agencyService = $agencyService;
    }

    public function agency(Request $request)
    {
        return new AgencyResource($request->user());
    }
}
