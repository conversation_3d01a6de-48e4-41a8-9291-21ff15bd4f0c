<?php

namespace App\Http\Requests\Tamara;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AddWebhookRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        $eventsArray = ['approved', 'declined', 'authorised', 'canceled', 'captured', 'refunded', 'expired'];
        return [
            'event' => ['nullable',Rule::in($eventsArray)],
            'url'   => 'nullable|url'
        ];
    }
}
