<?php

namespace App\Http\Requests\Attachments;

use Illuminate\Foundation\Http\FormRequest;
use Lang;
use Auth;

class UpdateRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        if (!$this->attachment) {
            return true;
        }
        return $user && $user->can('update', [Attachment::class]);  
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|regex:/^[^.]*$/iu|min:3|max:120'
        ];
    }

    public function messages()
    {
        return [
            'name.required' => Lang::get('validation.custom.attachments.name.required'),
            'name.regex' => Lang::get('validation.custom.attachments.name.regex'),
            'name.min' => Lang::get('validation.custom.attachments.name.min', ['min' => 3]),
            'name.max' => Lang::get('validation.custom.attachments.name.max', ['max' => 120]),
        ];
    }
}
