<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Services\UserService;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Enums\Role;
use Lang;

class ObligationNotConfirmedNotificationToUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        NotificationTrait;

    protected $request;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        if ($this->request->isStatus(RequestStatus::OBLIGATION_SENT)) {
            $patient = $this->request->requestable;
            $nameAr= $patient->translate('ar')->name;
            $nameEn= $patient->translate('en')->name;
            $bodyAr = Lang::get('translations.mails.body.obligation_reminder', ['name' => $nameAr], 'ar');
            $bodyEn = Lang::get('translations.mails.body.obligation_reminder', ['name' => $nameEn], 'en');
            $users = app(UserService::class)->users([Role::RECEPTIONIST, Role::FOLLOW_UP]);
            $notificationData = $this->notificationData(
                $patient->id, NotificationType::PATIENT, $bodyAr, $bodyEn, array()
            );
            SendNotificationJob::dispatch($users, $notificationData);
        }
    }
}
