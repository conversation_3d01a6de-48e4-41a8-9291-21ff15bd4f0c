<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\OnlinePaymentMethod;

class OnlinePaymentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'online_payment_method' => $this->online_payment_method,
            'merchant_transaction_id' => $this->merchant_transaction_id,
            'payment_brand' => $this->card->payment_brand?? null,
            'bin' => $this->card->bin?? null,
            'last_digits' => $this->card->last_digits?? null,
            'expiry_month' => $this->card->expiry_month?? null,
            'expiry_year' => $this->card->expiry_year?? null,
            'created_at'  => $this->created_at_converted,
        );
    }
}
