<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Lang;

class ContactResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'patient_id' => $this->contactable->id,
            'name' => array(
                'ar' => $this->translate('ar')->name,
                'en' => $this->translate('en')->name
            ),
            'relation' => $this->relation,
            'reason_of_contact' => $this->reason_of_contact,
            'phone_number' => $this->phone_number,
            'email' => $this->email,
        );
    }
}
