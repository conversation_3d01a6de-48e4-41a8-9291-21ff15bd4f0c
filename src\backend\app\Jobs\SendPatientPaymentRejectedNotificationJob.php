<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Traits\MessageTrait;
use App\Enums\RequestStatus;
use App\Enums\PaymentStatus;
use App\Enums\MessageType;
use Lang;

class SendPatientPaymentRejectedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        MessageTrait, NotificationTrait;

    protected $payment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->payment->refresh();
        if ($this->payment->isStatus(PaymentStatus::REJECTED)) {
            $lastPaymentHistory = $this->payment->lastPaymentHistory;
            $paymentReason = $lastPaymentHistory->paymentReason;
            $message = Lang::get(
                'messages.request_statuses.OBLIGATION_CONFIRMED_WITH_REJECTED_PAYMENT',
                ['reason' => Lang::get($paymentReason->reason, [], 'ar')], 'ar'
            );
            $notification = $this->notificationObject(
                NotificationType::REQUEST, $this->payment->request_id, null, RequestStatus::OBLIGATION_CONFIRMED
            );
            $this->createMessage($this->payment->patient, $message, MessageType::BOTH, $notification);
        }
    }
}
