<?php

namespace App\Jobs\Attachments;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Jobs\SendNotificationJob;
use App\Traits\NotificationTrait;
use App\Traits\AppointmentTrait;
use App\Enums\AppointmentReason;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Enums\GenderType;
use App\Models\Patient;
use App\Enums\Role;
use Lang;

class SendAttachmentIsUploadedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        AppointmentTrait, NotificationTrait;

    protected $patient, $creatable;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patient, $creatable)
    {
        $this->patient = $patient;
        $this->creatable = $creatable;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $appointment = $this->getAppointment();
        if ($this->isWaitReportsReason($appointment) && ($this->isPatient() || $this->isCoordinator())) {
            $gender = $this->patient->gender_type == GenderType::FEMALE? 'her' : 'him';
            $nameAr = $this->patient->translate('ar')->name;
            $nameEn = $this->patient->translate('en')->name;
            $key = $this->isPatient()? 'upload_attachment' : 'upload_attachment_user';
            $bodyAr = Lang::get('translations.mails.body.'.$key, ['name' => $nameAr], 'ar');
            $bodyEn = Lang::get(
                'translations.mails.body.'.$key, ['name' => $nameEn, 'gender' => $gender], 'en'
            );
            $notificationData = $this->notificationData(
                $this->patient->id, NotificationType::PATIENT, $bodyAr, $bodyEn, array()
            );
            SendNotificationJob::dispatch(array($appointment->user), $notificationData);
        }
    }

    public function getAppointment()
    {
        $request = $this->patient->lastRequest;
        $obligationNotSent = $request? $request->isStatus(RequestStatus::OBLIGATION_NOT_SENT) : null;
        return $obligationNotSent? $request->lastAppointment : null;
    }

    public function isWaitReportsReason($appointment)
    {
        $appointmentReasonId = $appointment->appointment_reason_id?? null;
        return $appointmentReasonId == AppointmentReason::WAIT_REPORTS;
    }

    public function isPatient()
    {
        return get_class($this->creatable) == Patient::class;
    }

    public function isCoordinator()
    {
        return $this->creatable->role_id == Role::FOLLOW_UP;
    }
}
