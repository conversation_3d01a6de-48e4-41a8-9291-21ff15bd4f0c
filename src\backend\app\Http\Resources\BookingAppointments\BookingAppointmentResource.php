<?php

namespace App\Http\Resources\BookingAppointments;

use App\Http\Resources\Appointments\AppointmentResource;
use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\SamplingReservationStatus;
use App\Enums\BookingAppointmentType;
use App\Enums\AppointmentPurpose;
use App\Enums\AppointmentStatus;
use App\Enums\AppointmentType;
use App\Enums\RequestStatus;
use App\Enums\Role;
use Lang;
use App;

class BookingAppointmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'type' => $this->type,
            'type_name' => BookingAppointmentType::getDescription($this->type),
            'bookable' => $this->type == BookingAppointmentType::APPOINTMENT?
                $this->appointment() : $this->samplingReservation(),
        );
    }

    public function appointment()
    {
        $appointment = $this->bookable;
        return array(
            'id' => $appointment->id,
            'start_date' => $appointment->start_date_converted,
            'join_date' => $appointment->join_date_converted,
            'end_date' => $appointment->end_date_converted,
            'type' => $appointment->type,
            'type_name' => AppointmentType::getDescription($appointment->type),
            'purpose' => $appointment->purpose,
            'purpose_name' => AppointmentPurpose::getDescription($appointment->purpose),
            'user_id' => $appointment->user_id,
            'user_name' => $appointment->assign->name?? Lang::get('translations.user'),
            'role_id' => $appointment->assign->role_id?? null,
            'role_name' => $appointment->assign? Role::getDescription($appointment->assign->role_id) : null,
            'status' => $appointment->status,
            'status_name' => AppointmentStatus::getDescription($appointment->status),
            'call_recording_url' => $appointment->onlineMeeting->call_recording_url?? null,
            'join_url'=> $appointment->onlineMeeting->join_url?? null,
            'file_sharing_link' =>  $appointment->onlineMeeting->file_sharing_link?? null,
            'message' => $this->statusMessage($appointment),
            'request_status' => $appointment->request->request_status,
            'request_status_name' => RequestStatus::getDescription($appointment->request->request_status),
        );
    }

    public function samplingReservation()
    {
        $samplingReservation = $this->bookable;
        return array(
            'id' => $samplingReservation->id,
            'date' => $samplingReservation->date_converted,
            'status' => $samplingReservation->status,
            'status_name' => SamplingReservationStatus::getDescription($samplingReservation->status),
            'agency_id' => $samplingReservation->agency_id,
            'agency_name' => $samplingReservation->assign->name,
            'patient_lab_test_id' => $samplingReservation->patient_lab_test_id,
            'patient_lab_test_name' => $samplingReservation->patientLabTest->name,
            'request_status' => $samplingReservation->request->request_status,
            'request_status_name' => RequestStatus::getDescription($samplingReservation->request->request_status),
        );
    }

    public function statusMessage($appointment)
    {
        $name = $appointment->user? $appointment->user->translate(App::getLocale())->name : Lang::get('translations.user');
        $position = Role::getDescription($appointment->user->role_id?? Role::SPECIALIST);
        $purpose = $appointment->purposeName(App::getLocale());
        $type = $appointment->typeName(App::getLocale());

        return Lang::get(
            'messages.request_statuses.'. RequestStatus::getKey($appointment->request->request_status),
            ['name' => $name, 'purpose' => $purpose, 'type' => $type, 'position' => $position]
        );
    }
}
