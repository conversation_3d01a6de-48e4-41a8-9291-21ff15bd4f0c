<?php

namespace App\Http\Controllers\Dashboard\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\ResetPasswordUserRequest;
use App\Services\ResetPasswordService;
use Lang;

class ResetPasswordController extends Controller
{
    private $resetPasswordService;

    public function __construct(ResetPasswordService $resetPasswordService)
    {
        $this->resetPasswordService = $resetPasswordService;
    }

    public function __invoke(ResetPasswordUserRequest $request)
    {
        $this->resetPasswordService->resetUserPassword($request->validated());
        return response()->json(['message' => Lang::get('messages.reset-password.success')], Response::HTTP_OK);
    }
}
