<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Lang;

class ModuleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $data = array(
            'id' => $this->id,
            'name' => Lang::get($this->name),
            'icon' => $this->icon,
            'has_menu' => $this->rights_count > 1
        );
        if ($data['has_menu']) {
            $data['submenu'] = RightResource::collection($this->rights);
        } else {
            $data['url'] = $this->rights->first()->url;
        }

        return $data;
    }
}
