<?php

namespace App\Http\Controllers\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Controllers\Controller;
use App\Services\ProgramService;
use App\Http\Resources\ProgramResource;
use App\Http\Resources\CustomProgramResource;
use App\Http\Resources\ProgramSearchResource;
use App\Http\Resources\ProgramStatisticsResource;
use App\Http\Requests\Programs\StoreRequest;
use App\Http\Requests\Programs\UpdateRequest;
use App\Http\Filters\ProgramFilter;
use App\Models\Program;
use Lang;

class ProgramController extends Controller
{
    private $programService;

    public function __construct(ProgramService $programService)
    {
        $this->programService = $programService;
    }

    public function index(ProgramFilter $filter)
    {
        $this->authorize('viewAny', Program::class);
        $result = $this->programService->filter($filter);
        return new ProgramSearchResource($result);
    }

    public function show(Program $program)
    {
        $this->authorize('view', Program::class);
        $program->loadCount('patients');
        return new CustomProgramResource($program);
    }

    public function list()
    {
        $result = $this->programService->list();
        return ProgramResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->programService->activeList();
        return ProgramResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->programService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.programs.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, Program $program)
    {
        $this->programService->update($request->validated(), $program);
        return response()->json(['message' => Lang::get('messages.programs.success.updated')], Response::HTTP_OK);
    }

    public function destroy(Program $program)
    {
        $this->authorize('delete', Program::class);
        $this->programService->delete($program);
        return response()->json(['message' => Lang::get('messages.programs.success.deleted')], Response::HTTP_OK);
    }

    public function active(Program $program)
    {
        $this->authorize('active', Program::class);
        $this->programService->active($program);
        return response()->json(['message' => Lang::get('messages.programs.success.actived')], Response::HTTP_OK);
    }

    public function deactive(Program $program)
    {
        $this->authorize('deactive', Program::class);
        $this->programService->deactive($program);
        return response()->json(['message' => Lang::get('messages.programs.success.deactived')], Response::HTTP_OK);
    }

    public function statistics()
    {
        $this->authorize('statistics', Program::class);
        $statistics = $this->programService->statistics();
        return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    }
}
