<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\MicrosoftGraphService;
use App\Services\OnlineMeetingService;

class CreateSharingRecordUrlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $appointment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $recordingsFolder = $this->getRecordingsFolder();
        if ($recordingsFolder) {
            $recordingFile = $this->getRecordingFile($recordingsFolder);
            if ($recordingFile) {
                $onlineMeeting = $this->appointment->onlineMeeting;
                $permission = app(MicrosoftGraphService::class)
                    ->createSharingLink($onlineMeeting->azureApplication, $recordingFile['id']);
                $data = $this->recordData($recordingsFolder, $recordingFile, $permission);
                app(OnlineMeetingService::class)->updateModel($data, $onlineMeeting);
                // SendSharingRecordUrlJob::dispatch($this->appointment)->onQueue(config('queue.queues.notifications'));
            }
        }
    }

    public function getRecordingsFolder()
    {
        $response = app(MicrosoftGraphService::class)
            ->getRootDriveItems($this->appointment->onlineMeeting->azureApplication);
        $folders = array_values(
            array_filter(
                $response['value'], function ($folder) {
                    return isset($folder['specialFolder'])
                        && $folder['specialFolder']['name'] == config('microsoft_graph.recordingsFolder');
                }
            )
        );
        return $folders[0]?? null;
    }

    public function getRecordingFile($recordingsFolder)
    {
        $onlineMeeting = $this->appointment->onlineMeeting;
        $response = app(MicrosoftGraphService::class)
            ->getItemChildren($onlineMeeting->azureApplication, $recordingsFolder['id']);
        $files = array_values(
            array_filter(
                $response['value'], function ($file) use ($onlineMeeting) {
                    return $file['source']['externalId'] == $onlineMeeting->call_id &&
                    $file['source']['threadId'] == $onlineMeeting->chat_id;
                }
            )
        );
        return $files[0]?? null;
    }

    public function recordData($folder, $file, $permission)
    {
        return array(
            'recordings_item_id' => $folder['id'],
            'file_item_id' => $file['id'],
            'file_sharing_link' => $permission['link']['webUrl'],
            'file_permission_id' => $permission['id'],
        );
    }
}
