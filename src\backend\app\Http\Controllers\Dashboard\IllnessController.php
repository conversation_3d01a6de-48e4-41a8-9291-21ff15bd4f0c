<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\IllnessFilter;
use App\Http\Resources\IllnessResource;
use App\Models\Illness;
use App\Http\Resources\IllnessSearchResource;
use App\Http\Resources\CustomIllnessResource;
use App\Services\IllnessService;
use App\Http\Requests\Genes\StoreRequest;
use App\Http\Requests\Genes\UpdateRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class IllnessController extends Controller
{
    protected $illnessService;

    public function __construct(IllnessService $illnessService)
    {
        $this->illnessService = $illnessService;
    }

    public function index(IllnessFilter $filter)
    {
        $this->authorize('viewAny', Illness::class);
        $result = $this->illnessService->filter($filter);
        return new IllnessSearchResource($result);
    }

    public function show(Illness $illness)
    {
        $this->authorize('view', Illness::class);
        return new CustomIllnessResource($illness);
    }

    public function list()
    {
        $result = $this->illnessService->list();
        return IllnessResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->illnessService->activeList();
        return IllnessResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->illnessService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.illness.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, Illness $illness)
    {
        $this->illnessService->update($request->validated(), $illness);
        return response()->json(['message' => Lang::get('messages.illness.success.updated')], Response::HTTP_OK);
    }

    public function destroy(Illness $illness)
    {
        $this->authorize('delete', Illness::class);
        $this->illnessService->delete($illness);
        return response()->json(['message' => Lang::get('messages.illness.success.deleted')], Response::HTTP_OK);
    }

    public function active(Illness $illness)
    {
        $this->authorize('active', Illness::class);
        $this->illnessService->active($illness);
        return response()->json(['message' => Lang::get('messages.illness.success.actived')], Response::HTTP_OK);
    }

    public function deactive(Illness $illness)
    {
        $this->authorize('deactive', Illness::class);
        $this->illnessService->deactive($illness);
        return response()->json(['message' => Lang::get('messages.illness.success.deactived')], Response::HTTP_OK);
    }

    // public function statistics()
    // {
    //     $this->authorize('statistics', Gene::class);
    //     $statistics = $this->geneService->statistics();
    //     return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    // }
}