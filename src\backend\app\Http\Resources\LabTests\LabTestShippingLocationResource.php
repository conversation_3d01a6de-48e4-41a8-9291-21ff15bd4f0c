<?php

namespace App\Http\Resources\LabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\ShippingLocation;

class LabTestShippingLocationResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'from' => $this->from,
            'from_name' => ShippingLocation::getDescription($this->from),
            'to' => $this->to,
            'to_name' => ShippingLocation::getDescription($this->to),
            'shipping_company' => $this->shipping_company,
        );
    }
}
