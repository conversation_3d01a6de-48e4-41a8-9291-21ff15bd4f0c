<?php

namespace App\Http\Requests\Payments;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\PaymentMethod;
use App\Models\Payment;
use Lang;
use Auth;

class PayPaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('pay', [Payment::class, $this->payment]);   
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'payment_method' => 'required|in:'. 
                implode(',', array(PaymentMethod::BANK_TRANSFER, PaymentMethod::MANUAL_PAYMENT)),
            'bank_transfer_image' => 'required_if:payment_method,'.PaymentMethod::BANK_TRANSFER.
                '|mimes:jpeg,jpg,png|max:'.config('app.profile_max_size'),
            
            'offline_payment' => 'required_if:payment_method,'.PaymentMethod::MANUAL_PAYMENT,
            'offline_payment.offline_payment_method_id' => 'nullable|required_if:payment_method,'.
                PaymentMethod::MANUAL_PAYMENT.'required_without:offline_payment.name|
                exists:offline_payment_methods,id,deleted_at,NULL',
            'offline_payment.name' => 'nullable|required_if:payment_method,'.PaymentMethod::MANUAL_PAYMENT.
                'required_without:offline_payment.offline_payment_method_id|regex:/^(?!.*\d)[a-zِِA-Z\p{Arabic}\s]+$/iu|
                unique:offline_payment_methods,name,NULL,id,deleted_at,NULL|max:50',
            'offline_payment.image' => 'nullable|mimes:jpeg,jpg,png|max:'.config('app.profile_max_size'),
        );
    }

    public function messages()
    {
        return array_merge(
            $this->bankTransferMessages(),
            $this->otherMethodMessages(),
        );
    }

    public function bankTransferMessages()
    {
        return array(
            'payment_method.required' => Lang::get('validation.custom.payments.payment_method.required'),
            'payment_method.in' => Lang::get(
                'validation.custom.payments.payment_method.in', 
                ['in' => implode(',', array(PaymentMethod::BANK_TRANSFER, PaymentMethod::MANUAL_PAYMENT))]
            ),
            'bank_transfer_image.required_if' => Lang::get(
                'validation.custom.payments.bank_transfer_image.required_if', 
                ['value' => PaymentMethod::BANK_TRANSFER]
            ),
            'bank_transfer_image.mimes' => Lang::get(
                'validation.custom.payments.bank_transfer_image.mimes', 
                ['mimes' => 'jpeg,jpg,png']
            ),
            'bank_transfer_image.max' => Lang::get(
                'validation.custom.payments.bank_transfer_image.max', ['max' => config('app.profile_max_size')]
            ),
            'offline_payment.required_if' => Lang::get('validation.custom.payments.offline_payment.required_if'),
            'offline_payment.offline_payment_method_id.required_if' => Lang::get(
                'validation.custom.payments.offline_payment.offline_payment_method_id.required_if'
            ),
        );
    }

    public function otherMethodMessages()
    {
        return array(
            'offline_payment.offline_payment_method_id.required_without' => Lang::get(
                'validation.custom.payments.offline_payment.offline_payment_method_id.required_if'
            ),
            'offline_payment.offline_payment_method_id.exists' => Lang::get(
                'validation.custom.payments.offline_payment.offline_payment_method_id.exists'
            ),
            'offline_payment.name.required_if' => Lang::get(
                'validation.custom.payments.offline_payment.name.required_if'
            ),
            'offline_payment.name.required_without' => Lang::get(
                'validation.custom.payments.offline_payment.name.required_if'
            ),
            'offline_payment.name.regex' => Lang::get('validation.custom.payments.offline_payment.name.regex'),
            'offline_payment.name.unique' => Lang::get('validation.custom.payments.offline_payment.name.unique'),
            'offline_payment.name.max' => Lang::get(
                'validation.custom.payments.offline_payment.name.max', ['max' => 50]
            ),
            'offline_payment.image.mimes' => Lang::get(
                'validation.custom.payments.offline_payment.image.mimes', 
                ['mimes' => 'jpeg,jpg,png']
            ),
            'offline_payment.image.max' => Lang::get(
                'validation.custom.payments.offline_payment.image.max', ['max' => config('app.profile_max_size')]
            ),
        );
    }
}
