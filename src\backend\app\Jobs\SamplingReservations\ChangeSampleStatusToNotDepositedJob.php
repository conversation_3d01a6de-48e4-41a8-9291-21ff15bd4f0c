<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use App\Services\SamplingReservationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Enums\SamplingReservationStatus;
use Illuminate\Queue\SerializesModels;
use Carbon\Carbon;

class ChangeSampleStatusToNotDepositedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $samplingReservation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($samplingReservation)
    {
        $this->samplingReservation = $samplingReservation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->samplingReservation->refresh();
        $isPastDate = $this->samplingReservation->date->lte(Carbon::now());
        if ($this->samplingReservation->isStatus(SamplingReservationStatus::NEW) && $isPastDate) {
            app(SamplingReservationService::class)
                ->changeStatus(['status' => SamplingReservationStatus::NOT_DEPOSITED], $this->samplingReservation);
        }
    }
}
