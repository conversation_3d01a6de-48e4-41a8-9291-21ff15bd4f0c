<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PaymentStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'total' => $this->total,
            'not_paid' => $this->not_paid,
            'pending' => $this->pending,
            'paid' => $this->paid,
            'canceled' => $this->canceled,
        );
    }
}
