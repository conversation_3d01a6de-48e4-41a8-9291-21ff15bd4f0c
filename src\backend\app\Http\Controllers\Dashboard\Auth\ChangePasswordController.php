<?php

namespace App\Http\Controllers\Dashboard\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\ChangePasswordRequest;
use App\Services\UserService;
use Lang;

class ChangePasswordController extends Controller
{
    public function __invoke(ChangePasswordRequest $request)
    {
        app(UserService::class)->changePassword($request->validated());
        return response()->json(['message' => Lang::get('messages.change_password.success')], Response::HTTP_OK);
    }
}
