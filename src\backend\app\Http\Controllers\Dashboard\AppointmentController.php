<?php

namespace App\Http\Controllers\Dashboard;

use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\Appointments\StoreAppointmentRequest;
use App\Http\Resources\Appointments\AppointmentDashboardStatisticsResource;
use App\Http\Requests\Appointments\ListAvailableAppointmentRequest;
use App\Http\Requests\Appointments\ListAppointmentRequest;
use App\Http\Requests\Appointments\UpdateAppointmentRequest;
use App\Http\Requests\Appointments\MyCalendarAppointmentRequest;
use App\Http\Requests\Appointments\CalendarAppointmentRequest;
use App\Http\Requests\Appointments\ChangeAppointmentStatusRequest;
use App\Http\Requests\Appointments\UpdateAppointmentReasonRequest;
use App\Http\Filters\AppointmentFilter;
use App\Http\Controllers\Controller;
use App\Http\Resources\Appointments\AvailableAppointmentResource;
use App\Http\Resources\Appointments\AppointmentCollection;
use App\Http\Resources\Appointments\AppointmentDetailsResource;
use App\Http\Resources\Appointments\CalendarResource;
use App\Http\Resources\Appointments\AppointmentStatisticsResource;
use App\Services\AppointmentService;
use App\Services\UserService;
use App\Models\Appointment;
use Lang;

class AppointmentController extends Controller
{
    private $appointmentService;

    public function __construct(AppointmentService $appointmentService)
    {
        $this->appointmentService = $appointmentService;
    }

    public function show(Appointment $appointment)
    {
        return new AppointmentDetailsResource($appointment);
    }

    public function availableList(ListAvailableAppointmentRequest $request)
    {
        $result = app(UserService::class)->availableAppointmentsList($request->validated());
        return AvailableAppointmentResource::collection($result);
    }

    public function list(ListAppointmentRequest $request)
    {
        $result = $this->appointmentService->list($request->validated());
        return new CalendarResource($result);
    }

    public function index(AppointmentFilter $filter) 
    {
        $result = $this->appointmentService->filter($filter);
        return new AppointmentCollection($result);
    }

    public function store(StoreAppointmentRequest $request)
    {
        
        $this->appointmentService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.appointments.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateAppointmentRequest $request, Appointment $appointment)
    {
        $this->appointmentService->update($request->validated(), $appointment);
        return response()->json(['message' => Lang::get('messages.appointments.success.updated')], Response::HTTP_OK);
    }

    public function destroy(Appointment $appointment)
    {
        $this->authorize('delete', [$appointment, $appointment->patient_id]);
        $this->appointmentService->delete($appointment);
        return response()->json(['message' => Lang::get('messages.appointments.success.deleted')], Response::HTTP_OK);
    }

    public function myCalendar(MyCalendarAppointmentRequest $request)
    {
        $this->authorize('myCalendar', Appointment::class);
        $result = $this->appointmentService->myCalendar($request->validated());
        return new CalendarResource($result);
    }

    public function calendar(CalendarAppointmentRequest $request)
    {
        $result = $this->appointmentService->calendar($request->validated());
        return new CalendarResource($result);
    }

    public function myFilter(AppointmentFilter $filter)
    {
        $this->authorize('myFilter', Appointment::class);
        $result = $this->appointmentService->myFilter($filter);
        return new AppointmentCollection($result);
    }

    public function myStatistics()
    {
        $this->authorize('myStatistics', Appointment::class);
        $statistics = $this->appointmentService->myStatistics();
        return response()->json(
            ['statistics' => new AppointmentStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function statistics(Request $request)
    {
        $this->authorize('statistics', Appointment::class);
        $statistics = $this->appointmentService->generalStatistics($request->all());
        return response()->json(
            ['statistics' => new AppointmentStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function dashboardStatistics(Request $request)
    {
        $this->authorize('dashboardStatistics', Appointment::class);
        $statistics = $this->appointmentService->dashboardStatistics($request->all());
        return response()->json(
            ['statistics' => new AppointmentDashboardStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function upcoming()
    {
        $this->authorize('upcoming', Appointment::class);
        return $this->appointmentService->upcoming();
    }

    public function active(Appointment $appointment)
    {
        $this->authorize('active', $appointment);
        $this->appointmentService->active($appointment);
        return response()->json(['message' => Lang::get('messages.appointments.success.actived')], Response::HTTP_OK);
    }

    public function changeStatus(ChangeAppointmentStatusRequest $request,Appointment $appointment)
    {
        $this->appointmentService->changeStatus($request->validated(), $appointment);
        return response()->json(
            ['message' => Lang::get('messages.appointments.success.change_status')], Response::HTTP_OK
        );
    }

    public function changeReason(UpdateAppointmentReasonRequest $request,Appointment $appointment)
    {
        $this->appointmentService->changeReason($request->validated(), $appointment);
        return response()->json(
            ['message' => Lang::get('messages.appointments.success.change_reason')], Response::HTTP_OK
        );
    }
}
