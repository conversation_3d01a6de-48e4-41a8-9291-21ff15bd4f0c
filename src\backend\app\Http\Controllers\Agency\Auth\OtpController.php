<?php

namespace App\Http\Controllers\Agency\Auth;

use App\Http\Requests\Auth\Agency\GenerateAgencyOtpRequest;
use App\Services\OtpVerificationService;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Http\Request;

use Lang;

class OtpController extends Controller
{
    
    public function __invoke(GenerateAgencyOtpRequest $request)
    {
        $expiredAt = app(OtpVerificationService::class)->resendOtpForAgency($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.otp.success.send'), 'expired_at' => $expiredAt], Response::HTTP_OK
        );
    }
}
