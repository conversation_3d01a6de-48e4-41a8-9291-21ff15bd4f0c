<?php

namespace App\Http\Controllers\Patient;

use App\Http\Requests\PatientLabTests\CheckPaymentStatusPatientLabTestRequest;
use App\Http\Resources\PatientLabTests\PatientLabTestSearchListResource;
use App\Http\Resources\PatientLabTests\PatientLabTestCollection;
use App\Http\Requests\PatientLabTests\ConfirmPatientLabTestRequest;
use App\Http\Requests\PatientLabTests\PayPatientLabTestRequest;
use App\Http\Resources\PatientLabTests\ShowPatientLabTestResource;
use App\Http\Resources\PatientLabTestObligationResource;
use App\Http\Controllers\Controller;
use App\Http\Filters\PatientLabTestFilter;
use App\Services\PatientLabTestService;
use App\Models\PatientLabTest;
use App\Services\TamaraPaymentService;
use App\Services\HyperService;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class PatientLabTestController extends Controller
{

    private $patientLabTestService;

    public function __construct(PatientLabTestService $patientLabTestService)
    {
        $this->patientLabTestService = $patientLabTestService;
    }

    public function show(PatientLabTest $patientLabTest)
    {
        $this->authorize('view', $patientLabTest);
        return new ShowPatientLabTestResource($patientLabTest);
    }

    public function list()
    {
        return $this->patientLabTestService->unconfirmedList();
    }

    public function approvedList()
    {
        return $this->patientLabTestService->approvedList();
    }

    public function notApprovedList()
    {
        return $this->patientLabTestService->notApprovedList();
    }

    public function previousList(PatientLabTestFilter $filter)
    {
        $result = $this->patientLabTestService->previousList($filter);
        return new PatientLabTestCollection($result);
    }

    public function listForInsuranceLetters(PatientLabTestFilter $filter)
    {
        $result = $this->patientLabTestService->listForInsuranceLetters($filter);
        return new PatientLabTestSearchListResource($result);
    }

    public function obligation(PatientLabTest $patientLabTest)
    {
        $this->authorize('obligation', $patientLabTest);
        $obligation = $this->patientLabTestService->obligation($patientLabTest);
        return new PatientLabTestObligationResource($obligation);
    }

    public function confirm(ConfirmPatientLabTestRequest $request)
    {
        $this->patientLabTestService->confirm($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.patient_lab_tests.success.confirm')], Response::HTTP_OK
        );
    }

    public function pay(PayPatientLabTestRequest $request, PatientLabTest $patientLabTest)
    {
        $data = $this->patientLabTestService->pay($request->validated(), $patientLabTest);
        $message = ['message' => Lang::get('messages.patient_lab_tests.success.pay')];
        $reponse = is_array($data) ? $data : ['checkout_id' => $data];

        return response()->json(array_merge($message, $reponse), Response::HTTP_OK);
    }

    public function paymentStatus(CheckPaymentStatusPatientLabTestRequest $request, PatientLabTest $patientLabTest)
    {
        return app(HyperService::class)->checkPaymentStatus($request->validated(), $patientLabTest);
    }

    /**
     * cancel Payment Order
     *
     * @param  PatientLabTest $patientLabTest
     * @return void
     */
    public function cancelPaymentOrder(PatientLabTest $patientLabTest)
    {
        $this->authorize('cancel', $patientLabTest);
        app(TamaraPaymentService::class)->cancelPaymentOrder($patientLabTest);
    }
}
