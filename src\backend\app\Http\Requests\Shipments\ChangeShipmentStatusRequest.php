<?php

namespace App\Http\Requests\Shipments;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\CheckShippingLocation;
use App\Enums\ShippingCompany;
use App\Enums\ShipmentStatus;
use App\Models\Shipment;
use Lang;
use Auth;

class ChangeShipmentStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('changeStatus', [Shipment::class, $this->patient_lab_test]);   
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'shipments' => ['required', 'array', new CheckShippingLocation],
            'shipments.*.id' => 'nullable|exists:shipments,id,deleted_at,NULL',
            'shipments.*.pickup_location_id' => 'nullable|required_without:shipments.*.pickup_location|
                exists:shipping_locations,id,deleted_at,NULL',
            'shipments.*.pickup_location' => 'nullable|required_without:shipments.*.pickup_location_id|
                min:3|max:150|unique:shipping_locations,location,NULL,id,deleted_at,NULL',
            'shipments.*.delivery_location_id' => 'nullable|required_without:shipments.*.delivery_location|
                exists:shipping_locations,id,deleted_at,NULL',
            'shipments.*.delivery_location' => 'nullable|required_without:shipments.*.delivery_location_id|
                min:3|max:150|unique:shipping_locations,location,NULL,id,deleted_at,NULL',
            'shipments.*.shipping_company' => 'required|in:'. implode(',', ShippingCompany::getValues()),
            'shipments.*.status' => 'required|in:'.
                implode(',', array(ShipmentStatus::SHIPPED, ShipmentStatus::NOT_SHIPPED)),
            'shipments.*.notes' => 'nullable',
        );
    }

    public function messages()
    {
        return array_merge(
            $this->groupOne(),
            $this->groupTwo(),
        );
    }

    public function groupOne()
    {
        return array(
            'shipments.required' => Lang::get('validation.custom.shipments.shipments.required'),
            'shipments.array' => Lang::get('validation.custom.shipments.shipments.array'),
            'shipments.*.id.exists' => Lang::get('validation.custom.shipments.id.exists'),
            'shipments.*.pickup_location_id.required_without' => Lang::get(
                'validation.custom.shipments.pickup_location_id.required_without'
            ),
            'shipments.*.pickup_location_id.exists' => Lang::get(
                'validation.custom.shipments.pickup_location_id.exists'
            ),
            'shipments.*.pickup_location.required_without' => Lang::get(
                'validation.custom.shipments.pickup_location.required_without'
            ),
            'shipments.*.pickup_location.min' => Lang::get(
                'validation.custom.shipments.pickup_location.min', ['min' => 3]
            ),
            'shipments.*.pickup_location.max' => Lang::get(
                'validation.custom.shipments.pickup_location.max', ['max' => 150]
            ),
            'shipments.*.pickup_location.unique' => Lang::get('validation.custom.shipments.pickup_location.unique'),
            'shipments.*.delivery_location_id.required_without' => Lang::get(
                'validation.custom.shipments.delivery_location_id.required_without'
            ),
        );
    }

    public function groupTwo()
    {
        return array(
            'shipments.*.delivery_location_id.exists' => Lang::get(
                'validation.custom.shipments.delivery_location_id.exists'
            ),
            'shipments.*.delivery_location.required_without' => Lang::get(
                'validation.custom.shipments.delivery_location.required_without'
            ),
            'shipments.*.delivery_location.min' => Lang::get(
                'validation.custom.shipments.delivery_location.min', ['min' => 3]
            ),
            'shipments.*.delivery_location.max' => Lang::get(
                'validation.custom.shipments.delivery_location.max', ['max' => 150]
            ),
            'shipments.*.shipping_company.required' => Lang::get(
                'validation.custom.shipments.shipping_company.required'
            ),
            'shipments.*.shipping_company.in' => Lang::get(
                'validation.custom.shipments.shipping_company.in', ['in' => implode(',', ShippingCompany::getValues())]
            ),
            'shipments.*.status.required' => Lang::get('validation.custom.shipments.status.required'),
            'shipments.*.status.in' => Lang::get(
                'validation.custom.shipments.status.in',
                ['in' => ShipmentStatus::SHIPPED.','.ShipmentStatus::NOT_SHIPPED]
            ),
        );
    }
}
