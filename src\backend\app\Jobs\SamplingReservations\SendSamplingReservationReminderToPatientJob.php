<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Enums\SamplingReservationStatus;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Traits\MessageTrait;
use App\Enums\MessageType;
use Lang;

class SendSamplingReservationReminderToPatientJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        NotificationTrait, MessageTrait;

    protected $samplingReservation, $reminderDuration;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($samplingReservation, $reminderDuration)
    {
        $this->samplingReservation = $samplingReservation;
        $this->reminderDuration = $reminderDuration;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->samplingReservation->refresh();
        if ($this->samplingReservation->isStatus(SamplingReservationStatus::NEW)) {
            $isReminderTime = $this->checkCurrentTimeEqualToReminderTime();
            $isReminderTime? $this->sendNotification() : null;
        }
    }

    public function checkCurrentTimeEqualToReminderTime()
    {
        $reminderTime = $this->samplingReservation->date->subMinutes($this->reminderDuration);
        return $reminderTime->isToday();
    }

    public function sendNotification()
    {
        $patient = $this->samplingReservation->patient;
        $patientLabTest = $this->samplingReservation->patientLabTest;
        $message = Lang::get('messages.sampling-reservations.patient_next_day_reminder', [], 'ar');
        $notification = $this->notificationObject(
            NotificationType::PATIENT_LAB_TEST, $patientLabTest->id, null, RequestStatus::SAMPLING_RESERVATION
        );
        $this->createMessage($patient, $message, MessageType::BOTH, $notification);
    }
}
