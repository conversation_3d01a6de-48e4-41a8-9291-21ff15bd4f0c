<?php

namespace App\Http\Resources\Agencies;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\Role;
use Lang;

class AgencyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'username' => $this->username,
            'role_id' => $this->role_id,
            'role_name' => Role::getDescription($this->role_id),
            'name' => array(
                'ar' => $this->translate('ar')->name,
                'en' => $this->translate('en')->name
            ),
            'city_id' => $this->city_id,
            'city_name' => Lang::get($this->city->name),
            'phone_number' => $this->phone_number,
            'email' => $this->email,
            'code' => $this->code,
            'map_link' => $this->map_link,
            'user_name' => array(
                'ar' => $this->translate('ar')->user_name,
                'en' => $this->translate('en')->user_name
            ),
            'user_position' => $this->user_position,
            'is_blocked' => $this->blocked_at? true : false,
            'schedules' => AgencyScheduleResource::collection($this->schedules),
        );
    }
}
