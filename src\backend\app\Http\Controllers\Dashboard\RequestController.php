<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\RequestService;
use App\Services\RejectionReasonService;
use App\Http\Requests\Requests\StoreRequestByUserRequest;
use App\Http\Resources\RequestStatisticsResource;
use App\Http\Resources\CustomRequestResource;
use App\Http\Resources\RequestCollection;
use App\Http\Requests\Requests\RejectRequestRequest;
use App\Http\Requests\Requests\CancelRequestRequest;
use App\Models\Request as RequestModel;
use App\Http\Filters\RequestFilter;
use App\Models\Patient;
use Lang;

class RequestController extends Controller
{
    private $requestService;

    public function __construct(RequestService $requestService)
    {
        $this->requestService = $requestService;
    }

    public function store(StoreRequestByUserRequest $request, Patient $patient)
    {
        $this->requestService->createByUser($request->validated(), $patient);
        return response()->json(
            ['message' => Lang::get('messages.requests.success.created_by_user')], Response::HTTP_OK
        );
    }

    public function statistics(Request $request)
    {
        $statistics = $this->requestService->statistics($request->all());
        return response()->json(
            ['statistics' => new RequestStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function show(RequestModel $request)
    {
        $request->load('requestHistories.rejectionReason');
        return new CustomRequestResource($request);
    }

    public function index(RequestFilter $filter)
    {
        $result = $this->requestService->filter($filter);
        return new RequestCollection($result);
    }

    public function accept(RequestModel $request)
    {
        $this->authorize('accept', [RequestModel::class, $request]);
        return $this->requestService->accept($request);
    }

    public function reject(RejectRequestRequest $requestForm, RequestModel $request)
    {
        $this->authorize('reject', [RequestModel::class, $request]);
        return $this->requestService->reject($requestForm->validated(), $request);
    }

    public function requestRejectReasonsIndex(RequestModel $request)
    {
        return app(RejectionReasonService::class)->manualReasonsindex($request);
    }

    public function cancel(CancelRequestRequest $requestForm, RequestModel $request)
    {
        $this->authorize('cancel', [RequestModel::class, $request]);
        $this->requestService->cancel($requestForm->validated(), $request);
        return response()->json(['message' => Lang::get('messages.requests.success.cancel')], Response::HTTP_OK);
    }
}
