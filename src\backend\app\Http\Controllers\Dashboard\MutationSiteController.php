<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\MutationSiteFilter;
use App\Http\Resources\MutationSiteResource;
use App\Models\MutationSite;
use App\Http\Resources\MutationSiteSearchResource;
use App\Http\Resources\CustomMutationSiteResource;
use App\Services\MutationSiteService;
use App\Http\Requests\MutationSites\StoreRequest;
use App\Http\Requests\MutationSites\UpdateRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class MutationSiteController extends Controller
{
    protected $mutationSiteService;

    public function __construct(MutationSiteService $mutationSiteService)
    {
        $this->mutationSiteService = $mutationSiteService;
    }

    public function index(MutationSiteFilter $filter)
    {
        $this->authorize('viewAny', MutationSite::class);
        $result =$this->mutationSiteService->filter($filter);
        return new MutationSiteSearchResource($result);
    }

    public function show(MutationSite $mutationSite)
    {
        $this->authorize('view', MutationSite::class);
        return new CustomMutationSiteResource($mutationSite);
    }

    public function list()
    {
        $result =$this->mutationSiteService->list();
        return MutationSiteResource::collection($result);
    }

    public function activeList()
    {
        $result =$this->mutationSiteService->activeList();
        return MutationSiteResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
       $this->mutationSiteService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.mutation_sites.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, MutationSite $mutationSite)
    {
       $this->mutationSiteService->update($request->validated(), $mutationSite);
        return response()->json(['message' => Lang::get('messages.mutation_sites.success.updated')], Response::HTTP_OK);
    }

    public function destroy(MutationSite $mutationSite)
    {
        $this->authorize('delete', MutationSite::class);
       $this->mutationSiteService->delete($mutationSite);
        return response()->json(['message' => Lang::get('messages.mutation_sites.success.deleted')], Response::HTTP_OK);
    }

    public function active(MutationSite $mutationSite)
    {
        $this->authorize('active', MutationSite::class);
       $this->mutationSiteService->active($mutationSite);
        return response()->json(['message' => Lang::get('messages.mutation_sites.success.actived')], Response::HTTP_OK);
    }

    public function deactive(MutationSite $mutationSite)
    {
        $this->authorize('deactive', MutationSite::class);
       $this->mutationSiteService->deactive($mutationSite);
        return response()->json(['message' => Lang::get('messages.mutation_sites.success.deactived')], Response::HTTP_OK);
    }
}