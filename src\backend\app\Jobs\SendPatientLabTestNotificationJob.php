<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Http\Connectors\FirebaseDynamicLinkConnector;
use App\Traits\NotificationTrait;
use App\Traits\MessageTrait;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Enums\MessageType;
use Lang;

class SendPatientLabTestNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        MessageTrait, NotificationTrait;

    protected $request, $patient;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request, $patient)
    {
        $this->request = $request;
        $this->patient = $patient;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $token = $this->patient->createToken('auth_token')->plainTextToken;
        $link = config('app.patient_app_url').config('app.patient_obligation_url').'?token='.$token;
        $link = FirebaseDynamicLinkConnector::createDeepLink($link);

        $message = Lang::get('messages.messages.obligation_sent', ['link' => $link], 'ar');
        $notification = $this->notificationObject(
            NotificationType::REQUEST, $this->request->id, null, RequestStatus::OBLIGATION_SENT
        );
        $this->createMessage($this->patient, $message, MessageType::BOTH, $notification);
    }
}
