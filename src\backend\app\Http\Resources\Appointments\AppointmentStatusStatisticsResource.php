<?php

namespace App\Http\Resources\Appointments;

use Illuminate\Http\Resources\Json\JsonResource;

class AppointmentStatusStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'status' => $this->status,
            'count' => $this->count,
            'count_percentage' => $this->count_percentage,
            'details' => AppointmentReasonStatisticsResource::collection($this->details),
        );
    }
}
