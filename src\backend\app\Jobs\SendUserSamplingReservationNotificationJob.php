<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Services\PaymentService;
use App\Enums\NotificationType;
use App\Services\UserService;
use App\Enums\LabTestStatus;
use App\Enums\Role;
use Lang;

class SendUserSamplingReservationNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        if ($this->patientLabTest->isStatus(LabTestStatus::PAID)) {
            $patient = $this->patientLabTest->patient;
            $patientNameAr = $patient->translate('ar')->name;
            $patientNameEn = $patient->translate('en')->name;

            $bodyAr = Lang::get('translations.mails.body.accept_payment', ['name' => $patientNameAr], 'ar');
            $bodyEn = Lang::get('translations.mails.body.accept_payment', ['name' => $patientNameEn], 'en');

            $users = app(UserService::class)->users([Role::FOLLOW_UP]);
            $paymentsStatistics = (Array) app(PaymentService::class)->statistics();
            $notificationData = $this->notificationData(
                $patient->id,
                NotificationType::PATIENT,
                $bodyAr,
                $bodyEn,
                array('payments_statistics' => $paymentsStatistics)
            );
            SendNotificationJob::dispatch($users, $notificationData);
        }
    }
}
