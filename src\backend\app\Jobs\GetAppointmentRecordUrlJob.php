<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\OnlineMeetingService;
use App\Services\MicrosoftGraphService;
use Carbon\Carbon;

class GetAppointmentRecordUrlJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $appointment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        if ($this->appointment->onlineMeeting && !$this->appointment->onlineMeeting->call_id) {
            $response = app(MicrosoftGraphService::class)->getChat($this->appointment);
            $events = array_values(
                array_filter(
                    $response['value'], function ($event) {
                        return isset($event['eventDetail'])
                        && $event['eventDetail']['@odata.type'] == config('microsoft_graph.recordingEvent')
                        && $event['eventDetail']['callRecordingStatus'] == config('microsoft_graph.recordingStatus');
                    }
                )
            );

            if (count($events)) {
                $data = $this->recordData($events[0]);
                app(OnlineMeetingService::class)->updateModel($data, $this->appointment->onlineMeeting);
                CreateSharingRecordUrlJob::dispatch($this->appointment)->delay(Carbon::now()->addMinutes(10))
                    ->onQueue(config('queue.queues.requests'));
            }
        }
    }

    public function recordData($event)
    {
        return array(
            'call_id' => $event['eventDetail']['callId'],
            'call_recording_url' => $event['eventDetail']['callRecordingUrl'],
        );
    }
}
