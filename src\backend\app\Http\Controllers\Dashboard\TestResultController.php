<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Services\TestResultService;
use Illuminate\Http\Request;
use App\Http\Requests\TestResult\CreateTestResultRequest;
use App\Http\Resources\TestResult\TestResultResource;

class TestResultController extends Controller
{
    protected $service;

    public function __construct(TestResultService $service)
    {
        $this->service = $service;
    }

    /**
     * Create a new test result for a patient lab test
     *
     * @param CreateTestResultRequest $request
     * @param int $patientLabTestId
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(CreateTestResultRequest $request, $patientLabTestId)
    {
        $data = $request->validated();
        return $this->service->createTestResult($data, $patientLabTestId);
    }

    /**
     * Get test result for a patient lab test
     *
     * @param int $patientLabTestId
     * @return \Illuminate\Http\JsonResponse
     */
    public function show($patientLabTestId)
    {
        $result = $this->service->getTestResultData($patientLabTestId);
        if ($result['success']) {
            return response()->json([
                'success' => true,
                'message' => $result['message'],
                'data' => [
                    'result' => new TestResultResource($result['data'])
                ]
            ], $result['status']);
        }

        return response()->json([
            'success' => false,
            'message' => $result['message']
        ], $result['status']);
    }

    /**
     * Generate PDF report for test result
     *
     * @param int $patientLabTestId
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateReport($patientLabTestId)
    {
        return $this->service->generateTestResultReport($patientLabTestId);
    }
}