<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Services\TimeZoneConverter;
use App\Services\PatientService;
use App\Services\AgencyService;
use App\Services\UserService;
use App\Traits\GuardTrait;
use Auth;

class UpdateUserTimezone
{
    use GuardTrait;

    /**
     * Handle an incoming request.
     *
     * @param  \Illuminate\Http\Request $request
     * @param  \Closure                 $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next, $guard)
    {
        $user = call_user_func(array($this, $guard), $request);

        $currentTimezone = TimeZoneConverter::getTimezone()->timezone;
        if ($user && $user->timezone != $currentTimezone) {
            $service = $this->getService($guard);
            $service->updateModel(array('timezone' => $currentTimezone), $user);
        }
        return $next($request);
    }

    public function user($request)
    {
        return Auth::user()?? app(UserService::class)->findBy('username', $request['username']?? '');
    }

    public function patient($request)
    {
        $column = $request['phone_number']? 'phone_number' : 'identification_number';
        $value = $request['phone_number']? $request['phone_number'] : ($request['username']?? '');
        return Auth::user()?? app(PatientService::class)->findBy($column, $value);    
    }

    public function agency($request)
    {
        return Auth::user()?? app(AgencyService::class)->findBy('username', $request['username']?? '');
    }
}
