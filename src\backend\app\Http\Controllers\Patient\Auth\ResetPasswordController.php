<?php

namespace App\Http\Controllers\Patient\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Http\Requests\ResetPasswordPatientRequest;
use App\Http\Requests\CheckTokenPatientRequest;
use App\Services\ResetPasswordService;
use Lang;

class ResetPasswordController extends Controller
{
    public function resetPassword(ResetPasswordPatientRequest $request)
    {
        return app(ResetPasswordService::class)->resetPatientPassword($request->validated());
    }

    public function checkToken(CheckTokenPatientRequest $request)
    {
        return app(ResetPasswordService::class)->checkPatientToken($request->validated());
    }
}