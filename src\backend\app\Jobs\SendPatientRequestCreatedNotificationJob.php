<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Traits\MessageTrait;
use App\Enums\RequestStatus;
use App\Enums\MessageType;
use Lang;

class SendPatientRequestCreatedNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait,
        MessageTrait;

    protected $request, $message;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request, $message)
    {
        $this->request = $request;
        $this->message = $message;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        if ($this->request->isStatus(RequestStatus::NEW)) {
            $patient = $this->request->requestable;
            $notification = $this->notificationObject(
                NotificationType::REQUEST, $this->request->id, null, RequestStatus::NEW
            );
            $this->createMessage($patient, $this->message, MessageType::BOTH, $notification);
        }
    }
}
