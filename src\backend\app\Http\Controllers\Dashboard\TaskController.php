<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\TaskService;
use App\Http\Requests\Tasks\StoreRequest;
use App\Http\Requests\Tasks\UpdateRequest;
use App\Http\Resources\Tasks\TaskStatisticsResource;
use App\Http\Resources\Tasks\GeneralTaskCollection;
use App\Http\Resources\Tasks\TaskCollection;
use App\Http\Resources\Tasks\TaskResource;
use App\Http\Filters\TaskFilter;
use App\Models\Task;
use Lang;

class TaskController extends Controller
{
    private $taskService;

    public function __construct(TaskService $taskService)
    {
        $this->taskService = $taskService;
    }

    public function index(TaskFilter $filter)
    {
        $result = $this->taskService->myFilter($filter);
        return new TaskCollection($result);
    }

    public function generalFilter(TaskFilter $filter)
    {
        $this->authorize('generalFilter', Task::class);
        $result = $this->taskService->generalFilter($filter);
        return new GeneralTaskCollection($result);
    }

    public function show(Task $task)
    {
        $this->authorize('view', $task);
        $task->load('creatable', 'taskStatusHistories');
        return new TaskResource($task);
    }

    public function store(StoreRequest $request)
    {
        return $this->taskService->create($request->validated());
    }

    public function update(UpdateRequest $request, Task $task)
    {
        return $this->taskService->update($request->validated(), $task);
    }

    public function destroy(Task $task)
    {
        $this->authorize('delete', $task);
        $this->taskService->delete($task);
        return response()->json(['message' => Lang::get('messages.tasks.success.deleted')], Response::HTTP_OK);
    }

    public function start(Task $task)
    {
        $this->authorize('start', $task);
        $this->taskService->start($task);
        return response()->json(['message' => Lang::get('messages.tasks.success.started')], Response::HTTP_OK);
    }

    public function complete(Task $task)
    {
        $this->authorize('complete', $task);
        $this->taskService->complete($task);
        return response()->json(['message' => Lang::get('messages.tasks.success.completed')], Response::HTTP_OK);
    }

    public function myStatistics()
    {
        $statistics = $this->taskService->myStatistics();
        return response()->json(
            ['statistics' => new TaskStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function tasksStatistics(Request $request)
    {
        $this->authorize('tasksStatistics', Task::class);
        $statistics = $this->taskService->tasksStatistics($request->all());
        return response()->json(
            ['statistics' => new TaskStatisticsResource($statistics)], Response::HTTP_OK
        );
    }
}
