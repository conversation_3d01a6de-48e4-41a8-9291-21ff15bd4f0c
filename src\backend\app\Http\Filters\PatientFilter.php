<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\Patient;

class PatientFilter extends Filter
{
    public $noRequests = -1;
    public $fields = array('blocked_at', 'gender_type', 'program_id', 'source', 'created_at', 'updated_at');

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->where(
            function ($query) use ($search) {
                $query->where('phone_number', 'like', '%'.$search.'%')
                    ->orWhere('identification_number', 'like', '%'.$search.'%')
                    ->orWhereHas(
                        'patientTranslations', function (Builder $query) use ($search) {
                            $query->where('name', 'like', '%'.$search.'%');
                        }
                    );
            }
        );
    }

    /**
     * @param string $source
     */
    public function source(string $source)
    {
        $this->builder->where('source', $source);
    }

    /**
     * @param string $requestStatus
     */
    public function requestStatus(string $requestStatus)
    {
        $this->builder->when(
            $requestStatus == $this->noRequests, function ($query) {
                return $query->doesntHave('lastRequest');
            }
        )->when(
            $requestStatus > 0, function ($query) use ($requestStatus) {
                    return $query->whereHas(
                        'lastRequest', function (Builder $query) use ($requestStatus) {
                            $query->status($requestStatus);
                        }
                    );
            }
        );
    }

    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}