<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Lang;

class RejectionReasonResource extends JsonResource
{
    protected static $patientName;

    public static function patientName($patientName)
    {
        static::$patientName = $patientName;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'title' => Lang::get($this->title),
            'message' => Lang::get($this->message, ['name' => static::$patientName, 'app_url' => config('app.patient_app_url')])
        );
    }
}
