<?php

namespace App\Http\Resources\Agencies;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Services\TimeZoneConverter;
use App\Enums\Day;
use Carbon\Carbon;

class ShowAgencyScheduleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'day' => $this->day,
            'day_name' => Day::getDescription($this->day),
            'is_working_day' => $this->from_hour? true : false,
            'from_hour' => $this->from_hour? $this->getDate($this->from_hour) : null,
            'to_hour' => $this->to_hour? $this->getDate($this->to_hour) : null,
        );
    }

    public function getDate($date)
    {
        $dateUtc = TimeZoneConverter::convertToUtc($date, $this->timezone);

        return Carbon::parse(TimeZoneConverter::convertFromUtc(carbon::parse($dateUtc)))->format('g:i a');
    }
}
