<?php

namespace App\Jobs\Shipments;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Bus\Queueable;
use App\Services\UserService;
use App\Enums\LabTestStatus;
use App\Traits\MailTrait;
use App\Enums\Role;
use Lang;

class SendShipmentReminderEmailToUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $patientLabTest;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        $statuses = array(LabTestStatus::SAMPLE_DEPOSITED, LabTestStatus::SAMPLE_NOT_SHIPPED);
        if (in_array($this->patientLabTest->status, $statuses)) {
            $users = app(UserService::class)->users([Role::FOLLOW_UP, Role::ADMIN]);
            foreach ($users as $user) {
                $this->sendShipmentReminderMail($this->patientLabTest, $user);
            }
        }
    }
}
