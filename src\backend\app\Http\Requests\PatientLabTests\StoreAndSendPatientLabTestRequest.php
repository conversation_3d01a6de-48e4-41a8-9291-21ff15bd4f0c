<?php

namespace App\Http\Requests\PatientLabTests;

use Illuminate\Foundation\Http\FormRequest;
use App\Models\PatientLabTest;
use Lang;
use Auth;

class StoreAndSendPatientLabTestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('createAndSend', [PatientLabTest::class, $this->patient, $this->lab_test_id]);   
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'lab_test_id' => 'required|exists:lab_tests,id,deleted_at,NULL',
            'notes' => 'nullable',
        );
    }

    public function messages()
    {
        return array(
            'lab_test_id.required' => Lang::get('validation.custom.patient_lab_test.lab_test_id.required'),
            'lab_test_id.exists' => Lang::get('validation.custom.patient_lab_test.lab_test_id.exists'),
        );
    }
}
