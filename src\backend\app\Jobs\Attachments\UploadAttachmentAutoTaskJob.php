<?php

namespace App\Jobs\Attachments;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Enums\AppointmentReason;
use Illuminate\Bus\Queueable;
use App\Services\TaskService;
use App\Enums\RequestStatus;
use App\Models\Patient;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class UploadAttachmentAutoTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $patient, $creatable;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patient, $creatable)
    {
        $this->patient = $patient;
        $this->creatable = $creatable;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $appointment = $this->getAppointment();
        if ($this->isWaitReportsReason($appointment) && ($this->isPatient() || $this->isCoordinator())) { 
            $patientName = $this->patient->translate('ar')->name;
            $key = $this->isPatient()? 'upload_attachment' : 'upload_attachment_user';
            $data = array(
                'description' => Lang::get(
                    'translations.mails.body.'.$key,
                    ['name' => $patientName], 'ar'
                ),
                'complete_date' => Carbon::now()->addDay(),
            );
            app(TaskService::class)->createModel($data, $appointment->user);
        }
    }

    public function getAppointment()
    {
        $request = $this->patient->lastRequest;
        $obligationNotSent = $request? $request->isStatus(RequestStatus::OBLIGATION_NOT_SENT) : null;
        return $obligationNotSent? $request->lastAppointment : null;
    }

    public function isWaitReportsReason($appointment)
    {
        $appointmentReasonId = $appointment->appointment_reason_id?? null;
        return $appointmentReasonId == AppointmentReason::WAIT_REPORTS;
    }

    public function isPatient()
    {
        return get_class($this->creatable) == Patient::class;
    }

    public function isCoordinator()
    {
        return $this->creatable->role_id == Role::FOLLOW_UP;
    }
}
