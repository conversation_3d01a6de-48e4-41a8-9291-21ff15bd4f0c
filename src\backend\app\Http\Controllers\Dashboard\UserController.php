<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Resources\Users\UserCollection;
use App\Http\Resources\Users\CustomUserResource;
use App\Http\Resources\Users\UserStatisticsResource;
use App\Http\Resources\Users\UserResource;
use App\Http\Requests\Users\UpdateProfileImageRequest;
use App\Http\Requests\Users\ValidatePhoneNumberRequest;
use App\Http\Requests\Users\ValidateEmailRequest;
use App\Http\Requests\Users\StoreRequest;
use App\Http\Requests\Users\UpdateRequest;
use App\Http\Filters\UserFilter;
use App\Services\UserService;
use App\Models\User;
use Lang;

class UserController extends Controller
{
    private $userService;

    public function __construct(UserService $userService)
    {
        $this->userService = $userService;
    }

    public function user(Request $request)
    {
        return new UserResource($request->user());
    }

    public function changeImage(UpdateProfileImageRequest $request)
    {
        $this->userService->changeImage($request->validated());
        return response()->json(['message' => Lang::get('messages.change_image.success')], Response::HTTP_OK);
    }

    public function store(StoreRequest $request)
    {
        $this->userService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.users.success.created')], Response::HTTP_OK);
    }

    public function index(UserFilter $filter)
    {
        $this->authorize('viewAny', User::class);
        $result = $this->userService->filter($filter);
        return new UserCollection($result);
    }

    public function block(User $user)
    {
        $this->authorize('block', $user);
        $this->userService->blockByAdmin($user);
        return response()->json(['message' => Lang::get('messages.users.success.block')], Response::HTTP_OK);
    }

    public function unlock(User $user)
    {
        $this->authorize('unlock', User::class);
        $this->userService->unBlock($user);
        return response()->json(['message' => Lang::get('messages.users.success.unlock')], Response::HTTP_OK);
    }

    public function destroy(User $user)
    {
        $this->authorize('delete', $user);
        $this->userService->delete($user);
        return response()->json(['message' => Lang::get('messages.users.success.deleted')], Response::HTTP_OK);
    }

    public function show(User $user)
    {
        $this->authorize('view', User::class);
        $user->load('schedules.scheduleSections');
        return new CustomUserResource($user);
    }

    public function update(UpdateRequest $request, User $user)
    {
        $this->userService->update($request->validated(), $user);
        return response()->json(['message' => Lang::get('messages.users.success.updated')], Response::HTTP_OK);
    }

    public function statistics()
    {
        $this->authorize('statistics', User::class);
        $statistics = $this->userService->statistics();
        return response()->json(['statistics' => new UserStatisticsResource($statistics)], Response::HTTP_OK);
    }

    public function validateEmail(ValidateEmailRequest $request)
    {
        return response()->json(
            ['message' => Lang::get('messages.validate.success.email', ['data' => $request['email']])],
            Response::HTTP_OK
        );
    }

    public function validatePhoneNumber(ValidatePhoneNumberRequest $request)
    {
        return response()->json(
            ['message' => Lang::get('messages.validate.success.phone-number', ['data' => $request['phone_number']])],
            Response::HTTP_OK
        );
    }

    public function list(UserFilter $filter)
    {
        $this->authorize('list', User::class);
        $result = $this->userService->list($filter);
        return UserResource::collection($result);
    } 
}
