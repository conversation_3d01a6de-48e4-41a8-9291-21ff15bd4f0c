<?php

namespace App\Listeners\LabTests;

use App\Events\LabTests\LabTestUpdated;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use App\Services\PatientLabTestService;
use App\Services\ShipmentService;

class NotifyLabTestUpdated
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * Handle the event.
     *
     * @param  \App\Events\LabTests\LabTestUpdated $event
     * @return void
     */
    public function handle(LabTestUpdated $event)
    {
        $shipments = app(ShipmentService::class)->getNewShipments($event->labTest)->groupBy('patient_lab_test_id');
        foreach ($shipments as $patientLabtestId => $shipmentGroup) {
            $patientLabtest = app(PatientLabTestService::class)->findBy('id', $patientLabtestId);
            $patientLabtest->shipments()->delete();
            app(ShipmentService::class)->createForPatientLabTest($patientLabtest);
        }
    }
}
