<?php

namespace App\Events\LabTests;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class LabTestUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $labTest;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($labTest)
    {
        $this->labTest = $labTest;
    }
}
