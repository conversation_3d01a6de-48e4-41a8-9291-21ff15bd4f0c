<?php

namespace App\Http\Resources\Attachments;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Models\Patient;
use App\Enums\AttachmentType;
use App\Enums\Role;
use Lang;

class AttachmentResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $extension = pathinfo($this->file_path, PATHINFO_EXTENSION);

        return array(
            'id' => $this->id,
            'name' => $this->name,
            'url' => $this->url,
            'extension' => $extension,
            'type' => $extension == 'pdf'? AttachmentType::PDF : AttachmentType::IMAGE,
            'uploaded_by' => $this->createdBy(),
            'uploaded_at' => $this->created_at_converted,
        );
    }

    public function createdBy()
    {
        return $this->creatable_type == Patient::class?
            Lang::get('translations.users.patient') : 
            ($this->creatable? Role::getDescription($this->creatable->role_id) : Lang::get('translations.user'));
    }
}
