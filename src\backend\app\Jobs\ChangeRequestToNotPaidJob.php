<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Events\Requests\RequestNotPaid;
use Illuminate\Queue\SerializesModels;
use App\Services\RequestService;
use App\Enums\RequestStatus;
use App\Enums\PaymentStatus;

class ChangeRequestToNotPaidJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $request;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        $pendingAndPaidCount = $this->request->payments()
            ->where('status', '!=', PaymentStatus::NOT_PAID)->count();

        if ($this->request->isStatus(RequestStatus::OBLIGATION_CONFIRMED) && $pendingAndPaidCount == 0) {
            app(RequestService::class)
                ->updateRequestStatus($this->request, RequestStatus::NOT_PAID_WITH_DELAY);
            RequestNotPaid::dispatch($this->request);
        }
    }
}
