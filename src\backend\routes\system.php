<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Dashboard\NotificationController;
use App\Http\Controllers\Dashboard\RequestController;
use App\Http\Controllers\Dashboard\LookupController;
use App\Http\Controllers\Dashboard\ModuleController;
use App\Http\Controllers\Dashboard\UserController;
use App\Http\Controllers\Dashboard\PatientController;
use App\Http\Controllers\Dashboard\ProgramController;
use App\Http\Controllers\Dashboard\MessageController;
use App\Http\Controllers\Dashboard\AttachmentController;
use App\Http\Controllers\Dashboard\TaskController;
use App\Http\Controllers\Dashboard\ScheduleController;
use App\Http\Controllers\Dashboard\LabTestController;
use App\Http\Controllers\Dashboard\ReferenceLabController;
use App\Http\Controllers\Dashboard\AgencyController;
use App\Http\Controllers\Dashboard\AppointmentController;
use App\Http\Controllers\Dashboard\ContactController;
use App\Http\Controllers\Dashboard\PaymentController;
use App\Http\Controllers\Dashboard\PatientLabTestController;
use App\Http\Controllers\Dashboard\Auth\ChangePasswordController;
use App\Http\Controllers\Dashboard\SamplingReservationController;
use App\Http\Controllers\Dashboard\MailController;
use App\Http\Controllers\Dashboard\NoteController;
use App\Http\Controllers\Dashboard\GeneCategoryController;
use App\Http\Controllers\Dashboard\GeneController;
use App\Http\Controllers\Dashboard\GeneResultController;
use App\Http\Controllers\Dashboard\GeneTransmissionMethodController;
use App\Http\Controllers\Dashboard\LabTestResultCategoryController;
use App\Http\Controllers\Dashboard\IllnessController;
use App\Http\Controllers\Dashboard\IntroductionController;
use App\Http\Controllers\Dashboard\ConclusionController;
use App\Http\Controllers\Dashboard\MutationSiteController;
use App\Http\Controllers\Dashboard\TestResultController;
/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/
Route::name('system.')
->middleware(['guard:user', 'auth:user', 'user.block:user', 'timezone:user'])
->prefix('v1')->group(
    function () {
        Route::group(
            ['middleware' => ['notification.access']], function () {
                Route::get('notifications/count', [NotificationController::class, 'notificationCount'])->name('notifications.count');
                Route::put('notifications/{notification}/read', [NotificationController::class, 'markAsRead'])->name('notifications.read');
                Route::get('notifications/new', [NotificationController::class, 'unreaded'])->name('notifications.unreaded');
                Route::get('notifications/old', [NotificationController::class, 'readed'])->name('notifications.readed');
            }
        );

        Route::name('statistics.')->prefix('statistics')->group(
            function () {
                Route::get('appointments', [AppointmentController::class, 'dashboardStatistics'])->name('appointments');
                Route::get('patient-lab-tests', [PatientLabTestController::class, 'dashboardStatistics'])->name('patient-lab-tests');
                Route::get('payments', [PaymentController::class, 'dashboardStatistics'])->name('payments');
                Route::get('sampling-reservations', [SamplingReservationController::class, 'samplingStatistics'])->name('sampling-reservations');
                Route::get('samples', [SamplingReservationController::class, 'samplesStatistics'])->name('samples');
                Route::get('tasks', [TaskController::class, 'tasksStatistics'])->name('tasks');
            }
        );

        Route::get('requests/statistics', [RequestController::class, 'statistics'])->name('requests.statistics');
        Route::get('requests/{request}', [RequestController::class, 'show'])->name('requests.show');
        Route::get('requests', [RequestController::class, 'index'])->name('requests.index');

        Route::get('request-sources', [LookupController::class,'requestSourcesIndex'])->name('request-sources.index');
        Route::get('request-statuses', [LookupController::class,'requeststatusesIndex'])->name('request-statuses.index');
        Route::get('request-reject-reasons', [LookupController::class,'rejectReasonsIndex'])->name('request-reject-reasons.index');
        Route::get('request-cancel-reasons', [LookupController::class,'cancelReasonsIndex'])->name('request-cancel-reasons.index');
        Route::get('requests/{request}/reject-reasons', [RequestController::class,'requestRejectReasonsIndex'])->name('requests.reject-reasons.index');
        Route::post('requests/{request}/accept', [RequestController::class, 'accept'])->name('requests.accept');
        Route::post('requests/{request}/reject', [RequestController::class, 'reject'])->name('requests.reject');
        Route::post('requests/{request}/cancel', [RequestController::class, 'cancel'])->name('requests.cancel');
        Route::post('patients/{patient}/requests', [RequestController::class,'store'])->name('requests.store');

        Route::get('side-menu', [ModuleController::class, 'sideMenu'])->name('modules.side-menu');
        Route::get('users/me', [UserController::class, 'user'])->name('users.me');
        Route::post('users/profile-image', [UserController::class, 'changeImage'])->name('users.update-image');
        Route::post('users/change-password', ChangePasswordController::class)->name('users.change-password');

        Route::get('my-patients', [PatientController::class, 'myFilter'])->name('patients.my-filter');
        Route::get('patients/count', [PatientController::class, 'count'])->name('patients.count');
        Route::apiResource('patients', PatientController::class)->except('destroy');
        Route::get('patient-sources', [LookupController::class,'patientSourcesIndex'])->name('patient-sources.index');
        Route::get('families', [LookupController::class,'familiesIndex'])->name('families.index');
        Route::post('validate/family-name', [PatientController::class, 'validateFamilyName'])->name('validate.family-name');
        Route::post('patients/{patient}/validate/identification-number', [PatientController::class, 'validateIdentificationNumber'])->name('validate.identification-number');

        Route::post('validate/gene-name', [GeneController::class, 'validateGeneName'])->name('validate.gene-name');

        Route::get('programs/list', [ProgramController::class,'list'])->name('programs.list');
        Route::get('programs/statistics', [ProgramController::class, 'statistics'])->name('programs.statistics');
        Route::get('programs/active-list', [ProgramController::class,'activeList'])->name('programs.active-list');
        Route::apiResource('programs', ProgramController::class);
        Route::post('programs/{program}/active', [ProgramController::class, 'active'])->name('programs.active');
        Route::post('programs/{program}/deactive', [ProgramController::class, 'deactive'])->name('programs.deactive');

        Route::get('genes/list', [GeneController::class,'list'])->name('genes.list');
        Route::get('genes/active-list', [GeneController::class,'activeList'])->name('genes.active-list');
        Route::apiResource('genes', GeneController::class);
        Route::post('genes/{gene}/active', [GeneController::class, 'active'])->name('genes.active');
        Route::post('genes/{gene}/deactive', [GeneController::class, 'deactive'])->name('genes.deactive');

        Route::get('gene-results/list', [GeneResultController::class,'list'])->name('gene-results.list');
        Route::get('gene-results/active-list', [GeneResultController::class,'activeList'])->name('gene-results.active-list');
        Route::apiResource('gene-results', GeneResultController::class);
        Route::post('gene-results/{geneResult}/active', [GeneResultController::class, 'active'])->name('gene-results.active');
        Route::post('gene-results/{geneResult}/deactive', [GeneResultController::class, 'deactive'])->name('gene-results.deactive');

         Route::get('gene-categories/list', [GeneCategoryController::class,'list'])->name('gene-categories.list');
        Route::get('gene-categories/active-list', [GeneCategoryController::class,'activeList'])->name('gene-categories.active-list');
        Route::apiResource('gene-categories', GeneCategoryController::class);
        Route::post('gene-categories/{geneCategory}/active', [GeneCategoryController::class, 'active'])->name('gene-categories.active');
        Route::post('gene-categories/{geneCategory}/deactive', [GeneCategoryController::class, 'deactive'])->name('gene-categories.deactive');

         Route::get('gene-transmission-methods/list', [GeneTransmissionMethodController::class,'list'])->name('gene-transmission-methods.list');
        Route::get('gene-transmission-methods/active-list', [GeneTransmissionMethodController::class,'activeList'])->name('gene-transmission-methods.active-list');
        Route::apiResource('gene-transmission-methods', GeneTransmissionMethodController::class);
        Route::post('gene-transmission-methods/{geneTransmissionMethod}/active', [GeneTransmissionMethodController::class, 'active'])->name('gene-transmission-methods.active');
        Route::post('gene-transmission-methods/{geneTransmissionMethod}/deactive', [GeneTransmissionMethodController::class, 'deactive'])->name('gene-transmission-methods.deactive');

         Route::get('lab-test-result-categories/list', [LabTestResultCategoryController::class,'list'])->name('lab-test-result-categories.list');
        Route::get('lab-test-result-categories/active-list', [LabTestResultCategoryController::class,'activeList'])->name('lab-test-result-categories.active-list');
        Route::apiResource('lab-test-result-categories', LabTestResultCategoryController::class);
        Route::post('lab-test-result-categories/{labTestResultCategory}/active', [LabTestResultCategoryController::class, 'active'])->name('lab-test-result-categories.active');
        Route::post('lab-test-result-categories/{labTestResultCategory}/deactive', [LabTestResultCategoryController::class, 'deactive'])->name('lab-test-result-categories.deactive');

         Route::get('illnesses/list', [IllnessController::class,'list'])->name('illnesses.list');
        Route::get('illnesses/active-list', [IllnessController::class,'activeList'])->name('illnesses.active-list');
        Route::apiResource('illnesses', IllnessController::class);
        Route::post('illnesses/{illness}/active', [IllnessController::class, 'active'])->name('illnesses.active');
        Route::post('illnesses/{illness}/deactive', [IllnessController::class, 'deactive'])->name('illnesses.deactive');

         Route::get('conclusions/list', [ConclusionController::class,'list'])->name('conclusions.list');
        Route::get('conclusions/active-list', [ConclusionController::class,'activeList'])->name('conclusions.active-list');
        Route::apiResource('conclusions', ConclusionController::class);
        Route::post('conclusions/{conclusion}/active', [ConclusionController::class, 'active'])->name('conclusions.active');
        Route::post('conclusions/{conclusion}/deactive', [ConclusionController::class, 'deactive'])->name('conclusions.deactive');

         Route::get('mutation-sites/list', [MutationSiteController::class,'list'])->name('mutation-sites.list');
        Route::get('mutation-sites/active-list', [MutationSiteController::class,'activeList'])->name('mutation-sites.active-list');
        Route::apiResource('mutation-sites', MutationSiteController::class);
        Route::post('mutation-sites/{mutation_site}/active', [MutationSiteController::class, 'active'])->name('mutation-sites.active');
        Route::post('mutation-sites/{mutation_site}/deactive', [MutationSiteController::class, 'deactive'])->name('mutation-sites.deactive');

         Route::get('introductions/list', [IntroductionController::class,'list'])->name('introductions.list');
        Route::get('introductions/active-list', [IntroductionController::class,'activeList'])->name('introductions.active-list');
        Route::apiResource('introductions', IntroductionController::class);
        Route::post('introductions/{introduction}/active', [IntroductionController::class, 'active'])->name('introductions.active');
        Route::post('introductions/{introduction}/deactive', [IntroductionController::class, 'deactive'])->name('introductions.deactive');

        Route::post('patient-lab-tests/{patientLabTest}/test-results', [TestResultController::class, 'store']);
        Route::get('patient-lab-tests/{patientLabTest}/test-results', [TestResultController::class, 'show']);
        Route::get('patient-lab-tests/{patientLabTest}/test-results/report', [TestResultController::class, 'generateReport']);

        Route::get('messages', [MessageController::class,'index'])->name('messages.index');

        Route::get('attachments/{attachment}/download', [AttachmentController::class, 'download'])->name('attachments.download');
        Route::get('attachments/{attachment}/file', [AttachmentController::class, 'file'])->name('attachments.file');
        Route::apiResource('attachments', AttachmentController::class);

        Route::get('general/tasks', [TaskController::class, 'generalFilter'])->name('general.tasks-filter');
        Route::get('tasks/statistics', [TaskController::class, 'myStatistics'])->name('tasks.my-statistics');
        Route::apiResource('tasks', TaskController::class);
        Route::post('tasks/{task}/start', [TaskController::class, 'start'])->name('tasks.start');
        Route::post('tasks/{task}/complete', [TaskController::class, 'complete'])->name('tasks.complete');

        Route::get('users/statistics', [UserController::class, 'statistics'])->name('users.statistics');
        Route::get('users/list', [UserController::class, 'list'])->name('users.list');
        Route::apiResource('users', UserController::class);
        Route::post('users/{user}/block', [UserController::class, 'block'])->name('users.block');
        Route::post('users/{user}/unlock', [UserController::class, 'unlock'])->name('users.unlock');

        Route::post('validate/email', [UserController::class, 'validateEmail'])->name('validate.email');
        Route::post('validate/phone-number', [UserController::class, 'validatePhoneNumber'])->name('validate.phone-number');

        Route::get('days', [LookupController::class,'daysIndex'])->name('days.index');
        Route::post('users/{user}/schedules', [ScheduleController::class, 'update'])->name('schedules.update');

        Route::get('lab-tests/statistics', [LabTestController::class, 'statistics'])->name('lab-tests.statistics');
        Route::get('lab-tests/list', [LabTestController::class, 'list'])->name('lab-tests.list');
        Route::apiResource('lab-tests', LabTestController::class);
        Route::post('lab-tests/{lab_test}/active', [LabTestController::class, 'active'])->name('lab-tests.active');
        Route::post('lab-tests/{lab_test}/deactive', [LabTestController::class, 'deactive'])->name('lab-tests.deactive');

        Route::get('countries', [LookupController::class, 'countriesIndex'])->name('countries.index');
        Route::get('regions', [LookupController::class, 'regionsIndex'])->name('regions.index');

        Route::get('reference-labs/list', [ReferenceLabController::class,'list'])->name('reference-labs.list');
        Route::get('reference-labs/active-list', [ReferenceLabController::class,'activeList'])->name('reference-labs.active-list');
        Route::get('reference-labs/statistics', [ReferenceLabController::class, 'statistics'])->name('reference-labs.statistics');
        Route::get('reference-labs/countries', [ReferenceLabController::class, 'countries'])->name('reference-labs.countries');
        Route::apiResource('reference-labs', ReferenceLabController::class);
        Route::post('reference-labs/{reference_lab}/active', [ReferenceLabController::class, 'active'])->name('reference-labs.active');
        Route::post('reference-labs/{reference_lab}/deactive', [ReferenceLabController::class, 'deactive'])->name('reference-labs.deactive');

        Route::apiResource('agencies', AgencyController::class);
        Route::post('agencies/{agency}/block', [AgencyController::class, 'block'])->name('agencies.block');
        Route::post('agencies/{agency}/unlock', [AgencyController::class, 'unlock'])->name('agencies.unlock');

        Route::post('appointments/available-list', [AppointmentController::class, 'availableList'])->name('appointments.available-list');
        Route::post('appointments/list', [AppointmentController::class, 'list'])->name('appointments.list');
        Route::post('appointments/my-calendar', [AppointmentController::class, 'myCalendar'])->name('appointments.my-calendar');
        Route::post('appointments/calendar', [AppointmentController::class, 'calendar'])->name('appointments.calendar');
        Route::get('my-appointments', [AppointmentController::class, 'myFilter'])->name('appointments.my-filter');
        Route::get('appointments/my-statistics', [AppointmentController::class, 'myStatistics'])->name('appointments.my-statistics');
        Route::get('appointments/statistics', [AppointmentController::class, 'statistics'])->name('appointments.statistics');
        Route::get('appointments/upcoming', [AppointmentController::class, 'upcoming'])->name('appointments.upcoming');
        Route::apiResource('appointments', AppointmentController::class);
        Route::post('appointments/{appointment}/active', [AppointmentController::class, 'active'])->name('appointments.active');
        Route::post('appointments/{appointment}/change-status', [AppointmentController::class, 'changeStatus'])->name('appointments.change-status');
        Route::get('appointment-reasons', [LookupController::class,'appointmentReasonsIndex'])->name('appointment-reasons.index');
        Route::post('appointments/{appointment}/change-reason', [AppointmentController::class, 'changeReason'])->name('appointments.change-reason');

        Route::apiResource('contacts', ContactController::class);

        Route::post('patients/{patient}/patient-lab-tests/create-send', [PatientLabTestController::class, 'createAndSend'])->name('patients.patient-lab-tests.create-send');
        Route::post('patients/{patient}/patient-lab-tests/send', [PatientLabTestController::class, 'send'])->name('patients.patient-lab-tests.send');
        Route::get('patients/{patient}/patient-lab-tests/list', [PatientLabTestController::class, 'list'])->name('patients.patient-lab-tests.list');
        Route::apiResource('patients.patient-lab-tests', PatientLabTestController::class);
        Route::post('patient-lab-tests/{patient_lab_test}/force-deposited', [PatientLabTestController::class, 'forceDeposited'])->name('patient-lab-tests.force-deposited');
        Route::post('patient-lab-tests/{patient_lab_test}/shipment-status', [PatientLabTestController::class, 'changeShipmentStatus'])->name('patient-lab-tests.shipment-status');
        Route::post('patient-lab-tests/{patient_lab_test}/result-status', [PatientLabTestController::class, 'changeResultStatus'])->name('patient-lab-tests.result-status');
        Route::get('patient-lab-tests', [PatientLabTestController::class, 'filterList'])->name('patient-lab-tests.filter-list');
        Route::get('patient-lab-tests/sampling-reservations', [PatientLabTestController::class, 'samplingReservationsFilter'])->name('patient-lab-tests.sampling-reservations');
        Route::get('patient-lab-tests/samples', [SamplingReservationController::class, 'samplesFilter'])->name('patient-lab-tests.samples');

        Route::get('payments/statistics', [PaymentController::class, 'statistics'])->name('payments.statistics');
        Route::get('payments/new-count', [PaymentController::class, 'newCount'])->name('payments.new-count');
        Route::get('payments/excel', [PaymentController::class, 'excel'])->name('payments.excel');
        Route::get('payments', [PaymentController::class, 'index'])->name('payments.index');
        Route::post('payments/last-seen', [PaymentController::class, 'lastSeen'])->name('payments.last-seen');
        Route::post('payments/{payment}/accept', [PaymentController::class, 'accept'])->name('payments.accept');
        Route::post('payments/{payment}/pay', [PaymentController::class, 'pay'])->name('payments.pay');
        Route::post('payments/{payment}/reject', [PaymentController::class, 'reject'])->name('payments.reject');
        Route::post('payments/{payment}/refund', [PaymentController::class, 'refund'])->name('payments.refund');
        Route::get('payments/{payment}/payment-status', [PaymentController::class, 'paymentStatus']);

        Route::get('payment-reasons', [LookupController::class,'paymentReasonsIndex'])->name('payment-reasons.index');

        Route::get('offline-payment-methods', [LookupController::class,'offlinePaymentMethodsIndex'])->name('offline-payment-methods.index');

        Route::post('patient-lab-tests/{patient_lab_test}/sampling-reservations/working-days', [SamplingReservationController::class, 'workingDays'])->name('patient-lab-tests.working-days');
        Route::apiResource('patient-lab-tests/{patient_lab_test}/sampling-reservations', SamplingReservationController::class)->only('store', 'update');

        Route::get('shipping-locations', [LookupController::class,'shippingLocationsIndex'])->name('shipping-locations.index');
        Route::post('welcome-mail', [MailController::class, 'sendWelcomeMail'])->name('welcome-mail.send');

        Route::apiResource('patients/{patient}/notes', NoteController::class)->only('store', 'index');
    }
);