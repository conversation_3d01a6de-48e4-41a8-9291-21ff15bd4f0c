<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class ConclusionSearchResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'current_page' => $this->currentPage(),
            'data' => CustomConclusionResource::collection($this->items()),
            'total' => $this->total(),
            'last_page' => $this->lastPage(),
            'per_page' => $this->perPage()
        );
    }
}
