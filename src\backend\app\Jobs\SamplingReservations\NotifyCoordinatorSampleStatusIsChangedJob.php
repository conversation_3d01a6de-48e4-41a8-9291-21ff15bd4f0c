<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\PatientLabTestTrait;
use App\Traits\NotificationTrait;
use App\Jobs\SendNotificationJob;
use App\Enums\NotificationType;
use App\Enums\ShippingLocation;
use App\Enums\ShippingCompany;
use App\Services\UserService;
use App\Enums\LabTestStatus;
use App\Enums\Role;
use Lang;
use App;

class NotifyCoordinatorSampleStatusIsChangedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait,
        PatientLabTestTrait;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        $users = app(UserService::class)->users([Role::FOLLOW_UP]);
        $bodyAr = $this->agencyNotification('ar');
        $bodyEn = $this->agencyNotification('en');

        $notificationData = $this->notificationData(
            $this->patientLabTest->patient_id,
            NotificationType::PATIENT,
            $bodyAr,
            $bodyEn,
            array()
        );
        SendNotificationJob::dispatch($users, $notificationData);
    }


    public function agencyNotification($lang)
    {
        App::setlocale($lang);
        $labTestShippingLocation = $this->patientLabTest->labTest->labTestShippingLocations()->first();
        $patientName = $this->patientLabTest->patient->translate($lang)->name;
        $agency = $this->patientLabTest->samplingReservation->agency?? null;
        $referenceLab = $this->patientLabTest->labTest->referenceLab;
        $key = $this->patientLabTest->isStatus(LabTestStatus::SAMPLE_DEPOSITED)?  'DEPOSITED' : 'NOT_DEPOSITED';
        $fromLocation = $this->getLocation($labTestShippingLocation->from, $agency, $referenceLab);
        $toLocation = $this->getLocation($labTestShippingLocation->to, $agency, $referenceLab);
        $shippingCompany = ShippingCompany::getDescription($labTestShippingLocation->shipping_company);

        return Lang::get(
            'translations.mails.body.'.$key, 
            ['name' => $patientName, 'from' => $fromLocation, 'to' => $toLocation, 'by' => $shippingCompany], $lang
        );
    }
}
