<?php

namespace App\Http\Controllers\Dashboard;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Services\MailService;

class MailController extends Controller
{
    private $service;

    public function __construct(MailService $service)
    {
        $this->service = $service;
    }

    public function sendWelcomeMail()
    {
        return $this->service->sendWelcomeMail();
    }
}
