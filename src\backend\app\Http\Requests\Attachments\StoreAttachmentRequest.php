<?php

namespace App\Http\Requests\Attachments;

use Illuminate\Foundation\Http\FormRequest;
use Lang;
use Auth;

class StoreAttachmentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        if (!$this->patient_id) {
            return true;
        }
        return $user && $user->can('create', [Attachment::class]);  
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'patient_id' => 'required|exists:patients,id,deleted_at,NULL',
            'attachment' => 'required|mimes:jpeg,jpg,png,pdf|max:'.config('app.profile_max_size'),
        ];
    }

    public function messages()
    {
        return [
            'patient_id.required' => Lang::get('validation.custom.appointments.patient_id.required'),
            'patient_id.exists' => Lang::get('validation.custom.appointments.patient_id.exists'),
            'attachment.required' => Lang::get('validation.custom.attachments.attachment.required'),
            'attachment.mimes' => Lang::get(
                'validation.custom.attachments.attachment.mimes', ['mimes' => 'jpeg,jpg,png,pdf']
            ),
            'attachment.max' => Lang::get(
                'validation.custom.attachments.attachment.max', ['max' => config('app.profile_max_size')]
            ),
        ];
    }
}
