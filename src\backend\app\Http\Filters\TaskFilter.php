<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Enums\TaskStatus;
use App\Models\Task;

class TaskFilter extends Filter
{
    public $fields = array('complete_date', 'status', 'created_at', 'updated_at');

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->where('description', 'like', '%'.$search.'%');
    }
  
    /**
     * @param string $status
     */
    public function status(string $status)
    {
        $this->builder->when(
            $status == TaskStatus::NEW, function ($query) {
                return $query->status(TaskStatus::NEW);
            }
        )->when(
            $status == TaskStatus::INPROGRESS, function ($query) {
                    return $query->whereIn('status', Task::inprogressStatuses());
            }
        )->when(
            $status == TaskStatus::COMPLETED, function ($query) {
                    return $query->whereIn('status', Task::completedStatuses());
            }
        );
    }

    /**
     * @param string $roleId
     */
    public function roleId(string $roleId)
    {
        $this->builder->whereHas(
            'taskable', function ($query) use ($roleId) {
                $query->where('role_id', $roleId);
            }
        );
    }

    /**
     * @param string $userId
     */
    public function userId(string $userId)
    {
        $this->builder->userId($userId);
    }

    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}