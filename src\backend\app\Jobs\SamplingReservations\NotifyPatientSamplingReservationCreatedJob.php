<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Enums\SamplingReservationStatus;
use Illuminate\Queue\SerializesModels;
use App\Services\TimeZoneConverter;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Traits\MessageTrait;
use App\Enums\MessageType;
use Carbon\Carbon;
use Lang;

class NotifyPatientSamplingReservationCreatedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        NotificationTrait, MessageTrait;

    protected $samplingReservation;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($samplingReservation)
    {
        $this->samplingReservation = $samplingReservation;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->samplingReservation->refresh();
        $patient = $this->samplingReservation->patient;
        $patientLabTest = $this->samplingReservation->patientLabTest;
        $reservationDate = Carbon::parse(
            TimeZoneConverter::convertFromUtc($this->samplingReservation->date, $patient->timezone)
        );
        $testName = $patientLabTest->labTest->translate('ar')->name;
        $date = $reservationDate->format('Y-m-d');
        $time = $reservationDate->format('g:i a');
        $agencyName = $this->samplingReservation->agency->translate('ar')->name;
        $message = Lang::get(
            'messages.request_statuses.SAMPLING_RESERVATION',
            ['testName' => $testName, 'date' => $date, 'time' => $time, 'agencyName' => $agencyName], 'ar'
        );
        $notification = $this->notificationObject(
            NotificationType::PATIENT_LAB_TEST, $patientLabTest->id, null, RequestStatus::SAMPLING_RESERVATION
        );
        $this->createMessage($patient, $message, MessageType::BOTH, $notification);     
    }
}
