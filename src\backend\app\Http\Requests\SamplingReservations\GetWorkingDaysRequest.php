<?php

namespace App\Http\Requests\SamplingReservations;

use Illuminate\Foundation\Http\FormRequest;
use App\Rules\CheckInAgencyWorkingHours;
use App\Services\TimeZoneConverter;
use App\Models\SamplingReservation;
use Carbon\Carbon;
use Lang;
use Auth;

class GetWorkingDaysRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'agency_id' => 'required|exists:agencies,id,deleted_at,NULL'
        ];
    }

    public function messages()
    {
        return [
            'agency_id.required' => Lang::get('validation.custom.sampling-reservations.agency_id.required'),
            'agency_id.exists' => Lang::get('validation.custom.sampling-reservations.agency_id.exists'),
        ];
    }
}
