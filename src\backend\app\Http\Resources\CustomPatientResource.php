<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\PatientSource;
use App\Enums\RequestStatus;
use App\Enums\AppointmentStatus;
use App\Models\Patient;
use Lang;
use App;

class CustomPatientResource extends JsonResource
{
    protected static $user;

    public static function user($user)
    {
        static::$user = $user;
    }

    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'name' => $this->translate(App::getlocale())->name,
            'profile_image' => $this->profile_image,
            'created_at' => $this->created_at_converted,
            'source' => $this->source,
            'source_name' => Lang::get('translations.request_sources.'. PatientSource::getKey($this->source)),
            'gender_type' => $this->gender_type,
            'request_status' => $this->lastRequest? $this->lastRequest->request_status : null,
            'request_status_name' => $this->getStatusName($this->lastRequest),
            'can_edit' => Patient::checkCanEdit(static::$user),
            'appointment_status' => $this->isRequestStatusAppointment()? ($this->lastRequest->lastAppointment->status?? null) : null,
            'appointment_status_name' => $this->isRequestStatusAppointment()? (AppointmentStatus::getDescription($this->lastRequest->lastAppointment->status?? null)) : null,
            'is_deleted' => $this->trashed(),
            'is_saudi' => $this->is_saudi,
        );
    }

    public function getStatusName($request)
    {
        return $request? 
            RequestStatus::getDescription($request->request_status)
            : Lang::get('translations.request_statuses.no_requests');
    }

    public function isRequestStatusAppointment()
    {
        return $this->lastRequest && 
            in_array(
                $this->lastRequest->request_status, 
                array(
                    RequestStatus::APPOINTMENT_RESERVATION,
                    RequestStatus::RESULT_EXPLANATION,
                    RequestStatus::INTERVIEW,
                )
            );
    }
}
