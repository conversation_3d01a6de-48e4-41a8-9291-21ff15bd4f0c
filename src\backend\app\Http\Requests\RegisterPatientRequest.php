<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\GenderType;
use App\Enums\SocialStatus;
use App\Enums\RequestSource;
use Lang;

class RegisterPatientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required|regex:/^(?!.*\d)[a-zِِA-Z\p{Arabic}\s]+$/iu|min:3|max:150',
            'identification_number' => 'required|min:10|max:20|unique:patients,identification_number,
                NULL,id,deleted_at,NULL',
            'birthday' => 'required|date|date_format:Y-m-d|before_or_equal:today',
            'gender_type' => 'required|in:'.implode(',', GenderType::getValues()),
            'social_status' => 'required|in:'.implode(',', SocialStatus::getValues()),
            'phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA|unique:patients,phone_number,
                NULL,id,deleted_at,NULL',
            'email' => 'nullable|email:rfc,dns',
            'city_id' => 'required|exists:cities,id,deleted_at,NULL',            
            'password' => 'required|min:8',
            'source' => 'required|in:'
                .RequestSource::WEBSITE.','. RequestSource::ANDROID_APP.','. RequestSource::IOS_APPS,
        ];
    }

    public function messages()
    {
        return array_merge(
            $this->mainInformationMessages(),
            $this->extraInformationMessages(),
        );
    }

    public function mainInformationMessages()
    {
        return [
            'name.required' => Lang::get('validation.custom.patient-register.name.required'),
            'name.min' => Lang::get('validation.custom.patient-register.name.min', ['min' => 3]),
            'name.max' => Lang::get('validation.custom.patient-register.name.max', ['max' => 150]),
            'name.regex' => Lang::get('validation.custom.patient-register.name.regex', ['max' => 150]),
            'identification_number.required' => Lang::get(
                'validation.custom.patient-register.identification_number.required'
            ),
            'identification_number.unique' => Lang::get(
                'validation.custom.patient-register.identification_number.unique'
            ),
            'identification_number.max' => Lang::get(
                'validation.custom.patient-register.identification_number.max', ['max' => 20]
            ),
            'identification_number.min' => Lang::get(
                'validation.custom.patient-register.identification_number.min', ['min' => 10]
            ),
            'phone_number.required' => Lang::get('validation.custom.requests.phone_number.required'),
            'phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.invalid'),
            'phone_number.unique' => Lang::get('validation.custom.patient-register.phone_number.unique'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
            'email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'source.required' => Lang::get('validation.custom.requests.request_source.required'),
        ];
    }

    public function extraInformationMessages()
    {
        return [
            'birthday.required' => Lang::get('validation.custom.patient-register.birthday.required'),
            'birthday.date' => Lang::get('validation.custom.patient-register.birthday.date'),
            'birthday.date_format' => Lang::get(
                'validation.custom.patient-register.birthday.date_format', ['format' => 'Y-m-d']
            ),
            'birthday.before_or_equal' => Lang::get('validation.custom.patient-register.birthday.before_or_equal'),
            'gender_type.required' => Lang::get('validation.custom.patient-register.gender_type.required'),
            'gender_type.in' => Lang::get(
                'validation.custom.patient-register.gender_type.in', ['in' => implode(',', GenderType::getValues())]
            ),
            'social_status.required' => Lang::get('validation.custom.patient-register.social_status.required'),
            'social_status.in' => Lang::get(
                'validation.custom.patient-register.social_status.in', 
                ['in' => implode(',', SocialStatus::getValues())]
            ),
            'password.required'  => Lang::get('validation.custom.reset-password.password.required'),
            'password.regex'  => Lang::get('validation.custom.reset-password.password.regex'),
            'password.min'  => Lang::get('validation.custom.reset-password.password.min'),
            'city_id.required' => Lang::get('validation.custom.patient-register.city_id.required'),
            'city_id.exists' => Lang::get('validation.custom.patient-register.city_id.exists'),
            'source.in' => Lang::get(
                'validation.custom.requests.request_source.in',
                ['in' => RequestSource::WEBSITE.','. RequestSource::ANDROID_APP.','. RequestSource::IOS_APPS]
            ),
        ];
    }
}
