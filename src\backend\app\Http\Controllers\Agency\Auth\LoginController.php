<?php

namespace App\Http\Controllers\Agency\Auth;

use App\Http\Requests\Auth\Agency\AgencyAuthRequest;
use App\Http\Requests\Auth\Agency\AgencyTokenRequest;
use App\Http\Controllers\Controller;
use App\Services\LoginService;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class LoginController extends Controller
{
    private $loginService;

    public function __construct(LoginService $loginService)
    {
        $this->loginService = $loginService;
    }

    public function sendOtp(AgencyAuthRequest $request)
    {
        $expiredAt = $this->loginService->sendOtpToAgency($request->validated());
        return response()->json(
            ['message' => Lang::get('messages.otp.success.send'), 'expired_at' => $expiredAt], Response::HTTP_OK
        );
    }

    public function token(AgencyTokenRequest $request)
    {
        return $this->loginService->agencyToken($request->validated());
    }
}
