<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\UserService;
use App\Services\TaskService;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class AcceptPaymentAutoTaskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $payment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $patientName = $this->payment->patient->translate('ar')->name;
        $coordinators = app(UserService::class)->users([Role::FOLLOW_UP]);
        $data = array(
            'description' => Lang::get('translations.mails.body.accept_payment', ['name' => $patientName], 'ar'),
            'complete_date' => Carbon::now()->addDay(),
        );
        foreach ($coordinators as $coordinator) {
            app(TaskService::class)->createModel($data, $coordinator);  
        }
    }
}
