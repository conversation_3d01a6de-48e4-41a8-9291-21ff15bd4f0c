<?php

namespace App\Http\Requests\Auth\Agency;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class ResetPasswordAgencyRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'token' => 'required|exists:password_resets,token',
            'password' => 'required|min:8|regex:'. config('app.password_regex'),
        ];
    }

    public function messages()
    {
        return [
            'token.required' => Lang::get('validation.custom.reset-password.token.required'),
            'token.exists' => Lang::get('validation.custom.reset-password.token.exists'),
            'password.required'  => Lang::get('validation.custom.reset-password.password.required'),
            'password.regex'  => Lang::get('validation.custom.reset-password.password.regex'),
            'password.min'  => Lang::get('validation.custom.reset-password.password.min'),
        ];
    }
}
