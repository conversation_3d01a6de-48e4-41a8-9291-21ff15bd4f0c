<?php

namespace App\Http\Requests\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Lang;
use Auth;

class CancelRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;   
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'cancellation_reason_id' => 'nullable|required_without:title|
                exists:cancellation_reasons,id,deleted_at,NULL',
            'title' => 'nullable|required_without:cancellation_reason_id|regex:/^(?!.*\d)[a-zِِA-Z\p{Arabic}\s]+$/iu|
                unique:cancellation_reasons,title,NULL,id,deleted_at,NULL|max:50',
            'cancellation_note' => 'nullable|max:150',
        ];
    }

    public function messages()
    {
        return [
            'cancellation_reason_id.required_without'  => Lang::get(
                'validation.custom.requests.cancellation_reason_id.required_without'
            ),
            'cancellation_reason_id.exists'  => Lang::get('validation.custom.requests.cancellation_reason_id.exists'),
            'title.required_without'  => Lang::get('validation.custom.requests.reason.required'),
            'title.max'  => Lang::get('validation.custom.requests.reason.max', ['max' => 50]),
            'title.regex'  => Lang::get('validation.custom.requests.reason.regex'),
            'title.unique'  => Lang::get('validation.custom.requests.reason.unique'),
            'cancellation_note.required'  => Lang::get('validation.custom.requests.cancellation_note.required'),
            'cancellation_note.max'  => Lang::get('validation.custom.requests.cancellation_note.max', ['max' => 150]),
        ];
    }
}
