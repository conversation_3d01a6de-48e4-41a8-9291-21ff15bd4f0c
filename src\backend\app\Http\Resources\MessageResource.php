<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\MessageType;

class MessageResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'message' => $this->text,
            'patient_profile_image' => $this->messageable->profile_image,
            'by_system' => $this->by_system,
            'type' => $this->type,
            'type_name' => MessageType::getDescription($this->type),
            'created_at' => $this->created_at_converted,
        );
    }
}
