<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Services\UserService;
use App\Enums\Role;
use Lang;

class SendUserPaymentPendingNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $payment;
    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $bodyAr = $this->notification('ar');
        $bodyEn = $this->notification('en');
        $coordinators = app(UserService::class)->users([Role::FOLLOW_UP, Role::ACCOUNTANT]);

        $notificationData = $this->notificationData(
            $this->payment->id, NotificationType::PAYMENT, $bodyAr, $bodyEn, array()
        );
        SendNotificationJob::dispatch($coordinators, $notificationData);
    }

    public function notification($lang)
    {
        $patientName = $this->payment->patient->translate($lang)->name;

        return Lang::get(
            'translations.mails.body.pending_payment', ['name' => $patientName],
            $lang
        );
    }
}
