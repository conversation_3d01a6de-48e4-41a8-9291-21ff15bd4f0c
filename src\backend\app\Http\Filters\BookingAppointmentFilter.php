<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Enums\SamplingReservationStatus;
use App\Models\SamplingReservation;
use App\Models\BookingAppointment;
use App\Enums\AppointmentStatus;
use App\Models\Appointment;
use App\Models\Patient;

class BookingAppointmentFilter extends Filter
{
    public $activeStatus = 1;
    public $pastStatus = 2;

    public $fields = array('created_at', 'updated_at');

    /**
     * @param string $status
     */
    public function status($status)
    {
        $this->builder->when(
            $status == $this->activeStatus, function ($query) {
                return $query->whereHas(
                    'bookable', function (Builder $query, $type) {
                        $this->searchIntoActiveAppointment($query, $type);
                    }
                );
            }
        )->when(
            $status == $this->pastStatus, function ($query) {
                return $query->whereHas(
                    'bookable', function (Builder $query, $type) {
                        $this->searchIntopastAppointment($query, $type);
                    }
                );
            }
        );
    }

    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }

    public function searchIntoActiveAppointment($query, $type)
    {
        $query->when(
            $type == Appointment::class, function ($query) {
                return $query->whereIn('status', Appointment::$activeStatuses);
            }
        )->when(
            $type == SamplingReservation::class, function ($query) {
                return $query->where('status', SamplingReservationStatus::NEW);
            }
        );
    }

    public function searchIntopastAppointment($query, $type)
    {
        $query->when(
            $type == Appointment::class, function ($query) {
                return $query->whereNotIn('status', array(AppointmentStatus::NEW, AppointmentStatus::INPROGRESS));
            }
        )->when(
            $type == SamplingReservation::class, function ($query) {
                return $query->where('status', '!=', SamplingReservationStatus::NEW);
            }
        );
    }
}