<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\UserService;
use App\Enums\LabTestStatus;
use App\Enums\PaymentStatus;
use App\Traits\MailTrait;
use App\Enums\Role;

class SendLabTestNotPaidEmailToUserJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        $payment = $this->patientLabTest->payment;
        if ($this->patientLabTest->isStatus(LabTestStatus::APPROVED) 
            && $payment->isStatus(PaymentStatus::NOT_PAID)
        ) {
            $users = app(UserService::class)->users([Role::FOLLOW_UP, Role::ADMIN, Role::ACCOUNTANT]);
            foreach ($users as $user) {
                $this->sendUnpaidPaymentMail($this->patientLabTest, $user);
            }
        }
    }
}
