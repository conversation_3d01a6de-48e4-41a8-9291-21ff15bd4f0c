<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\AttachmentService;
use App\Http\Filters\AttachmentFilter;
use App\Http\Resources\Attachments\AttachmentCollection;
use App\Http\Resources\Attachments\AttachmentResource;
use App\Http\Requests\Attachments\UpdateRequest;
use App\Http\Requests\Attachments\StoreAttachmentRequest;
use App\Models\Attachment;
use Lang;

class AttachmentController extends Controller
{
    private $attachmentService;

    public function __construct(AttachmentService $attachmentService)
    {
        $this->attachmentService = $attachmentService;
    }

    public function index(AttachmentFilter $filter)
    {
        $result = $this->attachmentService->filter($filter);
        return new AttachmentCollection($result);
    }

    public function show(Attachment $attachment)
    {
        return new AttachmentResource($attachment);
    }

    public function store(StoreAttachmentRequest $request)
    {
        $this->attachmentService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.attachments.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, Attachment $attachment)
    {
        $this->attachmentService->update($request->validated(), $attachment);
        return response()->json(['message' => Lang::get('messages.attachments.success.updated')], Response::HTTP_OK);
    }

    public function destroy(Attachment $attachment)
    {
        $this->authorize('deleteByUser', [Attachment::class]);
        $this->attachmentService->delete($attachment);
        return response()->json(['message' => Lang::get('messages.attachments.success.delete')], Response::HTTP_OK);
    }

    public function download(Attachment $attachment)
    {
        return $this->attachmentService->download($attachment);
    }

    public function file(Attachment $attachment)
    {
        return $this->attachmentService->file($attachment);
    }
}
