<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Traits\MessageTrait;
use App\Enums\RequestStatus;
use App\Enums\MessageType;
use Lang;

class SendPatientRequestNotPaidNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait,
        MessageTrait;

    protected $request;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        if ($this->request->isStatus(RequestStatus::NOT_PAID_WITH_DELAY)) {
            $message = Lang::get('messages.request_statuses.NOT_PAID_WITH_DELAY', [], 'ar');
            $notification = $this->notificationObject(
                NotificationType::REQUEST, $this->request->id, null, RequestStatus::NOT_PAID_WITH_DELAY
            );
            $this->createMessage($this->request->requestable, $message, MessageType::BOTH, $notification);
        }
    }
}
