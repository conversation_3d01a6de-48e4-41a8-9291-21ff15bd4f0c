<?php

namespace App\Jobs\Payments;

use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\HyperService;
use Illuminate\Bus\Queueable;
use App\Enums\PaymentStatus;

class CheckPaymentTransactionStatusJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $payment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($payment)
    {
        $this->payment = $payment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->payment->refresh();
        if ($this->payment->isStatus(PaymentStatus::PENDING)) {
            app(HyperService::class)->udatePaymentPendingStatus($this->payment);
        }
    }
}
