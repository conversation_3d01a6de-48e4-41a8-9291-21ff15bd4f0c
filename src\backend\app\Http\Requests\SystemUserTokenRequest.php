<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class SystemUserTokenRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'username' => 'required|max:50',
            'password' => 'required',
            'required_otp' => 'required|boolean',
            'otp_code' => 'required_if:required_otp,true|digits:4',
        ];
    }


    public function messages()
    {
        return [
            'username.required' => Lang::get('validation.custom.login.username.required'),
            'username.max' => Lang::get('validation.custom.login.username.max'),
            'password.required'  => Lang::get('validation.custom.login.password.required'),
            'otp_code.required_if'  => Lang::get('validation.custom.login.otp_code.required'),
            'otp_code.digits'  => Lang::get('validation.custom.login.otp_code.digits'),
            'required_otp.required' => Lang::get('validation.custom.login.required_otp.required'),
            'required_otp.boolean' => Lang::get('validation.custom.login.required_otp.boolean'),
        ];
    }
}
