<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\GeneCategoryFilter;
use App\Http\Resources\GeneCategoryResource;
use App\Models\GeneCategory;
use App\Http\Resources\GeneCategorySearchResource;
use App\Http\Resources\CustomGeneCategoryResource;
use App\Services\GeneCategoryService;
use App\Http\Requests\GeneCategories\StoreRequest;
use App\Http\Requests\GeneCategories\UpdateRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class GeneCategoryController extends Controller
{
    protected $geneCategoryService;

    public function __construct(GeneCategoryService $geneCategoryService)
    {
        $this->geneCategoryService = $geneCategoryService;
    }

    public function index(GeneCategoryFilter $filter)
    {
        $this->authorize('viewAny', GeneCategory::class);
        $result = $this->geneCategoryService->filter($filter);
        return new GeneCategorySearchResource($result);
    }

    public function show(GeneCategory $geneCategory)
    {
        $this->authorize('view', GeneCategory::class);
        return new CustomGeneCategoryResource($geneCategory);
    }

    public function list()
    {
        $result = $this->geneCategoryService->list();
        return GeneCategoryResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->geneCategoryService->activeList();
        return GeneCategoryResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->geneCategoryService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.gene_categories.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, GeneCategory $geneCategory)
    {
        $this->geneCategoryService->update($request->validated(), $geneCategory);
        return response()->json(['message' => Lang::get('messages.gene_categories.success.updated')], Response::HTTP_OK);
    }

    public function destroy(GeneCategory $geneCategory)
    {
        $this->authorize('delete', GeneCategory::class);
        $this->geneCategoryService->delete($geneCategory);
        return response()->json(['message' => Lang::get('messages.gene_categories.success.deleted')], Response::HTTP_OK);
    }

    public function active(GeneCategory $geneCategory)
    {
        $this->authorize('active', GeneCategory::class);
        $this->geneCategoryService->active($geneCategory);
        return response()->json(['message' => Lang::get('messages.gene_categories.success.actived')], Response::HTTP_OK);
    }

    public function deactive(GeneCategory $geneCategory)
    {
        $this->authorize('deactive', GeneCategory::class);
        $this->geneCategoryService->deactive($geneCategory);
        return response()->json(['message' => Lang::get('messages.gene_categories.success.deactived')], Response::HTTP_OK);
    }

    // public function statistics()
    // {
    //     $this->authorize('statistics', Gene::class);
    //     $statistics = $this->geneService->statistics();
    //     return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    // }
}