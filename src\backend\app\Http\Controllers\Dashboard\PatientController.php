<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\PatientFilter;
use App\Services\PatientService;
use App\Http\Resources\PatientCollection;
use App\Http\Resources\ShowPatientResource;
use App\Http\Requests\Patients\UpdatePatientRequest;
use App\Http\Requests\Patients\StorePatientRequest;
use App\Http\Requests\Patients\ValidateFamilyNameRequest;
use App\Http\Requests\Patients\ValidatePatientIdentificationNumberRequest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Models\Patient;
use Lang;

class PatientController extends Controller
{
    private $patientService;

    public function __construct(PatientService $patientService)
    {
        $this->patientService = $patientService;
    }

    public function index(PatientFilter $filter)
    {
        $result = $this->patientService->filter($filter);
        return new PatientCollection($result);
    }

    public function count()
    {
        $count = $this->patientService->count();
        return response()->json(['count' => $count], Response::HTTP_OK);
    }

    public function show(Patient $patient)
    {
        $patient->load('city.region', 'birthplace', 'program', 'appointments', 'contacts.translations');
        return new ShowPatientResource($patient);
    }

    public function store(StorePatientRequest $request)
    {
        return $this->patientService->create($request->validated());
    }

    public function update(UpdatePatientRequest $request, Patient $patient)
    {
        $this->patientService->update($request->validated(), $patient);
        return response()->json(
            ['message' => Lang::get('messages.patient-profile.success.updated')], Response::HTTP_OK
        );
    }

    public function validateFamilyName(ValidateFamilyNameRequest $request)
    {
        return response()->json(
            ['message' => Lang::get('messages.validate.success.family-name', ['data' => $request['name']])],
            Response::HTTP_OK
        );
    }

    public function validateIdentificationNumber(ValidatePatientIdentificationNumberRequest $request, Patient $patient)
    {
        return response()->json(
            ['message' => Lang::get(
                'messages.validate.success.identification-number', 
                ['data' => $request['identification_number'], 'id'=> $patient->id]
            )],
            Response::HTTP_OK
        );
    }

    public function myFilter(PatientFilter $filter)
    {
        $result = $this->patientService->myPatients($filter);
        return new PatientCollection($result);
    }
}
