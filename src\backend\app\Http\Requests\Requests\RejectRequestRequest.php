<?php

namespace App\Http\Requests\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class RejectRequestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'rejection_reason_id' => 'sometimes|exists:rejection_reasons,id,deleted_at,NULL',
            'title' => 'required_without:rejection_reason_id|regex:/^(?!.*\d)[a-zِِA-Z\p{Arabic}\s]+$/iu|
            unique:rejection_reasons,title,NULL,id,deleted_at,NULL|max:50',
            'message' => 'required_without:rejection_reason_id|max:150',
        ];
    }

    public function messages()
    {
        return [
            'rejection_reason_id.exists'  => Lang::get('validation.custom.requests.rejection_reason_id.exists'),
            'title.required_without'  => Lang::get(
                'validation.custom.requests.title.required_without',
                ['other' => 'rejection_reason_id', 'value' => 'null']
            ),
            'title.unique'  => Lang::get('validation.custom.requests.title.unique'),
            'title.regex'  => Lang::get('validation.custom.requests.title.regex'),
            'title.max'  => Lang::get('validation.custom.requests.title.max', ['max' => 50]),
            'message.required_without'  => Lang::get(
                'validation.custom.requests.message.required_without',
                ['other' => 'rejection_reason_id', 'value' => 'null']
            ),
            'message.max'  => Lang::get('validation.custom.requests.message.max', ['max' => 150]),
        ];
    }
}
