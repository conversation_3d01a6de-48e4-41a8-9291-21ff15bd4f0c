<?php

namespace App\Http\Controllers\Dashboard;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\ShippingLocations\ShippingLocationResource;
use App\Http\Resources\OfflinePaymentMethodResource;
use App\Http\Resources\CancellationReasonResource;
use App\Http\Resources\RequestSourseResource;
use App\Http\Resources\RequestStatusResource;
use App\Http\Resources\RejectionReasonTitleResource;
use App\Http\Resources\PatientSourseResource;
use App\Http\Resources\DayResource;
use App\Http\Resources\CountryResource;
use App\Http\Resources\RegionResource;
use App\Http\Resources\FamilyResource;
use App\Http\Resources\Appointments\AppointmentReasonResource;
use App\Http\Resources\PaymentReasonResource;
use App\Http\Resources\MutaionSiteResource;
use App\Services\OfflinePaymentMethodService;
use App\Services\CancellationReasonService;
use App\Services\ShippingLocationService;
use App\Services\RejectionReasonService;
use App\Services\PaymentReasonService;
use App\Services\CountryService;
use App\Services\RegionService;
use App\Services\FamilyService;
use App\Services\AppointmentReasonService;
use App\Services\MutaionSiteService;
use App\Enums\RequestSource;
use App\Enums\RequestStatus;
use App\Enums\PatientSource;
use App\Enums\Day;

class LookupController extends Controller
{
    public function requestSourcesIndex()
    {
        return RequestSourseResource::collection(RequestSource::getValues());
    }

    public function requeststatusesIndex()
    {
        return RequestStatusResource::collection(RequestStatus::getValues());
    }

    public function rejectReasonsIndex()
    {
        $result = app(RejectionReasonService::class)->index();
        return RejectionReasonTitleResource::collection($result);
    }

    public function cancelReasonsIndex()
    {
        $result = app(CancellationReasonService::class)->index();
        return CancellationReasonResource::collection($result);
    }

    public function patientSourcesIndex()
    {
        return PatientSourseResource::collection(PatientSource::getValues());
    }

    public function daysIndex()
    {
        return DayResource::collection(Day::getValues());
    }

    public function countriesIndex(Request $request)
    {
        $result = app(CountryService::class)->index($request->all());
        return CountryResource::collection($result);
    }

    public function regionsIndex(Request $request)
    {
        $result = app(RegionService::class)->index($request->all());
        return RegionResource::collection($result);
    }

    public function familiesIndex(Request $request)
    {
        $result = app(FamilyService::class)->index($request->all());
        return FamilyResource::collection($result);
    }

    public function appointmentReasonsIndex(Request $request)
    {
        $result = app(AppointmentReasonService::class)->index($request->all());
        return AppointmentReasonResource::collection($result);
    }

    public function paymentReasonsIndex()
    {
        $result = app(PaymentReasonService::class)->index();
        return PaymentReasonResource::collection($result);
    }

    public function offlinePaymentMethodsIndex()
    {
        $result = app(OfflinePaymentMethodService::class)->index();
        return OfflinePaymentMethodResource::collection($result);
    }

    public function shippingLocationsIndex(Request $request)
    {
        $result = app(ShippingLocationService::class)->index($request->all());
        return ShippingLocationResource::collection($result);
    }

    public function MutaionSiteIndex(Request $request)
    {
        $result = app(MutaionSiteService::class)->index($request->all());
        return MutaionSiteResource::collection($result);
    }

}
