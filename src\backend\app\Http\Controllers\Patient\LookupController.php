<?php

namespace App\Http\Controllers\Patient;

use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use App\Http\Resources\BankAccountResource;
use App\Http\Resources\RegionResource;
use App\Http\Resources\CityResource;
use App\Services\BankAccountService;
use App\Services\RegionService;
use App\Services\CityService;
use App\Models\Region;
use App\Models\City;

class LookupController extends Controller
{
    public function regionsIndex(Request $request)
    {
        $result = app(RegionService::class)->saudiArabiaRegions($request->all());
        return RegionResource::collection($result);
    }

    public function regionCitiesIndex(Request $request, Region $region)
    {
        $result = app(CityService::class)->list($request->all(), $region->id);
        return CityResource::collection($result);
    }

    public function citiesIndex(Request $request)
    {
        $result = app(CityService::class)->index($request->all());
        return CityResource::collection($result);
    }

    public function bankAccountsIndex()
    {
        $result = app(BankAccountService::class)->index();
        return BankAccountResource::collection($result);
    }

    public function cityAgenciesCount(City $city)
    {
        return app(CityService::class)->cityAgenciesCount($city);
    }
}
