<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Services\RequestService;
use App\Enums\NotificationType;
use App\Services\UserService;
use App\Enums\RequestStatus;
use Lang;

class SendUserNotCompletedRequestNotificationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, NotificationTrait;

    protected $request, $systemUser;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request, $systemUser)
    {
        $this->request = $request;
        $this->systemUser = $systemUser;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        if ($this->request->isStatus(RequestStatus::CONFIRMED)) {
            $patient = $this->request->requestable;
            $admins = app(UserService::class)->admins();
            $users = $admins->push($this->systemUser);
            $bodyAr = Lang::get('messages.requests.statuses.CONFIRMED', ['name' => $patient->name], 'ar');
            $bodyEn = Lang::get('messages.requests.statuses.CONFIRMED', ['name' => $patient->name], 'en');

            $requestsStatistics = (Array) app(RequestService::class)->statistics();
            $notificationData = $this->notificationData(
                $this->request->id,
                NotificationType::REQUEST,
                $bodyAr,
                $bodyEn,
                array('requests_statistics' => $requestsStatistics)
            );
            SendNotificationJob::dispatch($users, $notificationData);
        }
    }
}
