<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Traits\MessageTrait;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Enums\MessageType;
use Lang;
use App;

class ObligationSentReminderToPatientJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        MessageTrait, NotificationTrait;

    protected $request;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($request)
    {
        $this->request = $request;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->request->refresh();
        if ($this->request->isStatus(RequestStatus::OBLIGATION_SENT)) {
            App::setlocale('ar');
            $requestStatus = $this->request->request_status;
            $message = Lang::get('messages.messages.obligation_sent_reminder');
            $notification = $this->notificationObject(
                NotificationType::REQUEST, $this->request->id, null, $requestStatus
            );
            $this->createMessage($this->request->requestable, $message, MessageType::BOTH, $notification);
        }
    }
}
