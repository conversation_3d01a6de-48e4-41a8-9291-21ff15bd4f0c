<?php

namespace App\Http\Requests\Contacts;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\TestType;
use Lang;
use Auth;

class UpdateContactRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        if (!$this->patient_id) {
            return true;
        }
        return $user && $user->can('update', [Contact::class]);  
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'patient_id' => 'required|exists:patients,id,deleted_at,NULL',
            'name.ar' => 'required|min:3|max:150|regex:/^(?!.*\d)[\p{Arabic}\s]+$/iu',
            'name.en' => 'required|min:3|max:150|regex:/^(?!.*\d)[a-zِِA-Z\s]+$/iu',
            'relation' => 'required',
            'reason_of_contact' => 'required',
            'phone_number' => 'required|regex:/^\+(?:[0-9]?){6,14}[0-9]$/|phone:AUTO,SA',
            'email' => 'nullable|email:rfc,dns',       
        );
    }

    public function messages()
    {
        return array(
            'patient_id.required' => Lang::get('validation.custom.appointments.patient_id.required'),
            'patient_id.exists' => Lang::get('validation.custom.appointments.patient_id.exists'),
            'email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'phone_number.required' => Lang::get('validation.custom.patient-login.phone_number.required'),
            'phone_number.regex' => Lang::get('validation.custom.patient-register.phone_number.regex'),
            'phone_number.phone' => Lang::get('validation.custom.patient-register.phone_number.invalid'),
            'reason_of_contact.required' => Lang::get(
                'validation.custom.patient-profile.contacts.reason_of_contact.required'
            ),
            'relation.required' => Lang::get('validation.custom.patient-profile.contacts.relation.required'),
            'name.ar.required' => Lang::get('validation.custom.users.name.ar.required'),
            'name.ar.min' => Lang::get('validation.custom.users.name.ar.min', ['min' => 3]),
            'name.ar.max' => Lang::get('validation.custom.users.name.ar.max', ['max' => 150]),
            'name.ar.regex' => Lang::get('validation.custom.users.name.ar.regex'),
            'name.en.required' => Lang::get('validation.custom.users.name.en.required'),
            'name.en.min' => Lang::get('validation.custom.users.name.en.min', ['min' => 3]),
            'name.en.max' => Lang::get('validation.custom.users.name.en.max', ['max' => 150]),
            'name.en.regex' => Lang::get('validation.custom.users.name.en.regex'),
        );
    }
}
