<?php

namespace App\Http\Controllers\Patient;

use App\Http\Requests\Attachments\StorePatientAttachmentRequest;
use App\Http\Resources\Attachments\AttachmentCollection;
use App\Http\Resources\Attachments\AttachmentResource;
use App\Http\Filters\AttachmentFilter;
use App\Http\Controllers\Controller;
use App\Services\AttachmentService;
use App\Models\Attachment;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class AttachmentController extends Controller
{
    private $attachmentService;

    public function __construct(AttachmentService $attachmentService)
    {
        $this->attachmentService = $attachmentService;
    }

    public function index(AttachmentFilter $filter)
    {
        $result = $this->attachmentService->patientFilter($filter);
        return new AttachmentCollection($result);
    }

    public function show(Attachment $attachment)
    {
        $this->authorize('view', [Attachment::class, $attachment]);
        return new AttachmentResource($attachment);
    }

    public function store(StorePatientAttachmentRequest $request)
    {
        $this->attachmentService->createByPatient($request->validated());
        return response()->json(['message' => Lang::get('messages.attachments.success.created')], Response::HTTP_OK);
    }

    public function destroy(Attachment $attachment)
    {
        $this->authorize('deleteByPatient', [Attachment::class, $attachment]);
        $this->attachmentService->delete($attachment);
        return response()->json(['message' => Lang::get('messages.attachments.success.delete')], Response::HTTP_OK);
    }
}
