<?php

namespace App\Http\Requests\PatientLabTests;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\OnlinePaymentMethod;
use App\Models\PatientLabTest;
use App\Enums\PaymentMethod;
use Lang;
use Auth;

class PayPatientLabTestRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('pay', [PatientLabTest::class, $this->patient_lab_test]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'payment_method' => 'required|in:'. implode(
                ',',
                array(PaymentMethod::BANK_TRANSFER, PaymentMethod::ONLINE_PAYMENT, PaymentMethod::INSTALLMENT_PAYMENT)
            ),
            'bank_transfer_image' => 'required_if:payment_method,'.PaymentMethod::BANK_TRANSFER.
                '|mimes:jpeg,jpg,png|max:'.config('app.profile_max_size'),
            'online_payment_method' => 'required_if:payment_method,'.PaymentMethod::ONLINE_PAYMENT.
                '|in:'.implode(',', OnlinePaymentMethod::getValues()),
            'installments' => 'required_if:payment_method,'.PaymentMethod::INSTALLMENT_PAYMENT.
                '|in:'.implode(',', [3,4]),
        );
    }

    public function messages()
    {
        return array(
            'payment_method.required' => Lang::get('validation.custom.payments.payment_method.required'),
            'payment_method.in' => Lang::get(
                'validation.custom.payments.payment_method.in',
                ['in' => implode(',', array(PaymentMethod::BANK_TRANSFER, PaymentMethod::ONLINE_PAYMENT))]
            ),
            'bank_transfer_image.required_if' => Lang::get(
                'validation.custom.payments.bank_transfer_image.required_if',
                ['value' => PaymentMethod::BANK_TRANSFER]
            ),
            'bank_transfer_image.mimes' => Lang::get(
                'validation.custom.payments.bank_transfer_image.mimes',
                ['mimes' => 'jpeg,jpg,png']
            ),
            'bank_transfer_image.max' => Lang::get(
                'validation.custom.payments.bank_transfer_image.max', ['max' => config('app.profile_max_size')]
            ),
            'online_payment_method.required_if'=> Lang::get(
                'validation.custom.payments.online_payment_method.required_if',
                ['value' => PaymentMethod::ONLINE_PAYMENT]
            ),
            'online_payment_method.in'=> Lang::get(
                'validation.custom.payments.online_payment_method.in',
                ['in' => implode(',', OnlinePaymentMethod::getValues())]
            ),
        );
    }
}
