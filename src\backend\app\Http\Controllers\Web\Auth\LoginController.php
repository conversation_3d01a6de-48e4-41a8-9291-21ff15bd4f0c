<?php

namespace App\Http\Controllers\Web\Auth;

use App\Http\Requests\Auth\Web\AdminAuthRequest;
use App\Http\Controllers\Controller;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Session;
use Lang;
use Auth;

class LoginController extends Controller
{
    public function loginView()
    {
        if (Auth::check()) {
            return view('dashboard.main');
        }
        return view('dashboard.auth.login');
    }

    public function login(AdminAuthRequest $request)
    {
        if (Auth::attempt($request->validated())) {
            return redirect()->route('dashboard');
        }
        return redirect()->back()->withErrors(['username' => Lang::get('messages.login.errors.wrong_data')]);
    }

    public function dashboard()
    {
        if (Auth::check()) {
            return view('dashboard.main');
        }
        return redirect()->route('login-view');
    }

    public function logout()
    {
        Session::flush();
        Auth::logout();
        return redirect()->route('login-view');
    }
}
