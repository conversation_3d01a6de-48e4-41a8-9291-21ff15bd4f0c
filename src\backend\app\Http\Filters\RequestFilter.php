<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\Patient;
use App\Models\AnonymousPatient;
use App\Models\Request;
use App\Enums\RequestStage;

class RequestFilter extends Filter
{
    public $activeStatus = -1;
    public $newStatus =    -2;
    public $fields = array('request_status', 'request_source', 'created_at', 'updated_at');

    /**
     * @param string $requestStatus
     */
    public function requestStatus(string $requestStatus)
    {
        $this->builder->when(
            $requestStatus == $this->newStatus, function ($query) {
                return $query->stage(RequestStage::NEW);
            }
        )->when(
            $requestStatus == $this->activeStatus, function ($query) {
                return $query->stage(RequestStage::ACTIVE);
            }
        )->when(
            $requestStatus > 0, function ($query) use ($requestStatus) {
                    return $query->status($requestStatus);
            }
        );
    }

    /**
     * @param string $stage
     */
    public function stage(string $stage)
    {
        $this->builder->stage($stage);
    }

    /**
     * @param string $previousStatus
     */
    public function previousStatus(string $previousStatus)
    {
        $this->builder->whereHas(
            'previousRequestHistory', function (Builder $query) use ($previousStatus) {
                $query->where('request_status', $previousStatus);
            }
        );
    }

    /**
     * @param string $requestSource
     */
    public function requestSource(string $requestSource)
    {
        $this->builder->where('request_source', $requestSource);
    }
    
    /**
     * @param string $patientName
     */
    public function patientName(string $patientName)
    {
        $this->builder->whereHas(
            'requestable', function (Builder $query) use ($patientName) {
                $query->where('name', 'like', '%'.$patientName.'%');
            }
        );
    }

    /**
     * @param string $patientId
     */
    public function patientId($patientId)
    {
        $this->builder->patient($patientId, Patient::class);
    }

    /**
     * @param string $phoneNumber
     */
    public function patientPhone(string $phoneNumber)
    {
        $this->builder->whereHas(
            'requestable', function (Builder $query) use ($phoneNumber) {
                $query->where('phone_number', 'like', '%'.$phoneNumber.'%');
            }
        );
    }

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->whereHas(
            'requestable', function (Builder $query, $type) use ($search) {
                $query->when(
                    $type == Patient::class, function ($query) use ($search) {
                        return $this->searchIntoPatient($query, $search);
                    }
                )->when(
                    $type == AnonymousPatient::class, function ($query) use ($search) {
                        return $this->searchIntoAnonymousPatient($query, $search);
                    }
                );
            }
        );
    }
    
    /**
     * @param string $rejectionReasonId
     */
    public function rejectionReasonId(string $rejectionReasonId)
    {
        $this->builder->whereHas(
            'requestHistories', function (Builder $query) use ($rejectionReasonId) {
                $query->where('rejection_reason_id', $rejectionReasonId);
            }
        );
    }

    /**
     * @param string $cancellationReasonId
     */
    public function cancellationReasonId(string $cancellationReasonId) 
    {
        $this->builder->whereHas(
            'requestHistories', function (Builder $query) use ($cancellationReasonId) {
                $query->where('cancellation_reason_id', $cancellationReasonId);
            }
        );
    }

    /**
     * @param string $startDate
     */
    public function startDate(string $startDate)
    {
        $this->builder->whereDate('created_at', '>=', $startDate);
    }

    /**
     * @param string $endDate
     */
    public function endDate(string $endDate)
    {
        $this->builder->whereDate('created_at', '<=', $endDate);
    }
    
    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }

    private function searchIntoPatient($query, $search)
    {
        return $query->where(
            function ($query) use ($search) {
                $query->where('phone_number', 'like', '%'.$search.'%')
                    ->orWhere('identification_number', 'like', '%'.$search.'%')
                    ->orWhereHas(
                        'patientTranslations', function (Builder $query) use ($search) {
                            $query->where('name', 'like', '%'.$search.'%');
                        }
                    );
            }
        );
    }

    private function searchIntoAnonymousPatient($query, $search)
    {
        return $query->where(
            function ($query) use ($search) {
                $query->where('phone_number', 'like', '%'.$search.'%')
                    ->orWhere('name', 'like', '%'.$search.'%');
            }
        );
    }
}