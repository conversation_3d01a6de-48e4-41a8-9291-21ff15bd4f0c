<?php

namespace App\Http\Requests\Patients;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\SocialStatus;
use App\Models\Patient;
use Lang;
use Auth;

class DeleteCurrentPatientRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $patient = Auth::user();
        return $patient && $patient->can('delete', Patient::class);   
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return array(
            'otp_code' => 'required|digits:4',
            'is_data_saved' => 'required|boolean'
        );
    }

    public function messages()
    {
        return array(
            'otp_code.required'  => Lang::get('validation.custom.login.otp_code.required'),
            'otp_code.digits'  => Lang::get('validation.custom.login.otp_code.digits'),
            'is_data_saved.required'  => Lang::get('validation.custom.patients.is_data_saved.required'),
            'is_data_saved.boolean'  => Lang::get('validation.custom.patients.is_data_saved.boolean'),
        );
    }
}
