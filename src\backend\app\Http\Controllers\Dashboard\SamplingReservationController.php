<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Resources\SamplingReservations\SamplingReservationDashboardStatisticsResource;
use App\Http\Requests\SamplingReservations\UpdateSamplingReservationByUserRequest;
use App\Http\Requests\SamplingReservations\StoreSamplingReservationByUserRequest;
use App\Http\Resources\SamplingReservations\SamplesDashboardStatisticsResource;
use App\Http\Requests\SamplingReservations\GetWorkingDaysRequest;
use App\Http\Resources\PatientLabTests\SampleFilterCollection;
use App\Services\SamplingReservationService;
use App\Http\Filters\PatientLabTestFilter;
use App\Http\Controllers\Controller;
use App\Models\SamplingReservation;
use App\Models\PatientLabTest;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class SamplingReservationController extends Controller
{
 
    private $service;

    public function __construct(SamplingReservationService $service)
    {
        $this->service = $service;
    }

    public function store(StoreSamplingReservationByUserRequest $request, PatientLabTest $patientLabTest)
    {
        $this->service->createByUser($request->validated(), $patientLabTest);
        return response()->json(
            ['message' => Lang::get('messages.sampling-reservations.success.created')], Response::HTTP_OK
        );
    }

    public function update(
        UpdateSamplingReservationByUserRequest $request,
        PatientLabTest $patientLabTest,
        SamplingReservation $samplingReservation
    ) {
        $this->service->updateByUser($request->validated(), $patientLabTest, $samplingReservation);
        return response()->json(
            ['message' => Lang::get('messages.sampling-reservations.success.updated')], Response::HTTP_OK
        );
    }

    public function workingDays(GetWorkingDaysRequest $request, PatientLabTest $patientLabTest)
    {
        return $this->service->workingDays($request->validated(), $patientLabTest);
    }

    public function samplingStatistics(Request $request)
    {
        $this->authorize('samplingStatistics', SamplingReservation::class);
        $statistics = $this->service->samplingStatistics($request->all());
        return response()->json(
            ['statistics' => new SamplingReservationDashboardStatisticsResource($statistics)], Response::HTTP_OK
        );
    }
    
    public function samplesStatistics(Request $request)
    {
        $this->authorize('samplesStatistics', SamplingReservation::class);
        $statistics = $this->service->samplesStatistics($request->all());
        return response()->json(
            ['statistics' => new SamplesDashboardStatisticsResource($statistics)], Response::HTTP_OK
        );
    }

    public function samplesFilter(PatientLabTestFilter $filter)
    {
        $this->authorize('samplesFilter', PatientLabTest::class);
        $result = $this->service->samplesFilter($filter);
        return new SampleFilterCollection($result);
    }
}
