<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use App\Models\Patient;
use App\Models\Appointment;
use App\Enums\AppointmentStatus;
use App\Enums\RequestStage;
use Carbon\Carbon;

class AppointmentFilter extends Filter
{
    public $activeStatus = -1;
    public $pastStatus = -2;
    public $medicalAppointments = -1;
    public $activeRequestStatus = -1;

    public $fields = array('type', 'status', 'start_date', 'purpose', 'created_at', 'updated_at');

    /**
     * @param string $appointmentStatus
     */
    public function appointmentStatus(string $appointmentStatus)
    {
        $this->builder->when(
            $appointmentStatus == $this->activeStatus, function ($query) {
                return $query->whereIn('status', Appointment::$activeStatuses)
                    ->where(
                        function ($query) {
                            $query->where('completion_due_date', '>', Carbon::now())
                                ->orWhereNull('completion_due_date');
                        }
                    );
            }
        )->when(
            $appointmentStatus == $this->pastStatus, function ($query) {
                return $query->whereNotIn('status', array(AppointmentStatus::NEW, AppointmentStatus::INPROGRESS));
            }
        )->when(
            $appointmentStatus == Appointment::$confirmedWithDelay, function ($query) {
                return $query->confirmedWithDelay();
            }
        )->when(
            $appointmentStatus > 0, function ($query) use ($appointmentStatus) {
                    return $query->where('status', $appointmentStatus);
            }
        );
    }

    /**
     * @param string $requestStatus
     */
    public function requestStatus(string $requestStatus)
    {
        $this->builder->when(
            $requestStatus == $this->activeRequestStatus, function ($query) {
                return $query->activeRequests();
            }
        )->when(
            $requestStatus > 0, function ($query) use ($requestStatus) {
                    return $query->requestStatus($requestStatus);
            }
        );
    }

    /**
     * @param string $appointmentType
     */
    public function appointmentType(string $appointmentType)
    {
        $this->builder->where('type', $appointmentType);
    }

    /**
     * @param string $appointmentPurpose
     */
    public function appointmentPurpose(string $appointmentPurpose)
    {
        $this->builder->when(
            $appointmentPurpose == $this->medicalAppointments, function ($query) {
                // TODO check the lab test status not result explanation
                return $query->whereIn('purpose', Appointment::$medicalAppointments);
            }
        )->when(
            $appointmentPurpose > 0, function ($query) use ($appointmentPurpose) {
                return $query->where('purpose', $appointmentPurpose);
            }
        );
    }

    /**
     * @param string $patientId
     */
    public function patientId(string $patientId)
    {
        $this->builder->where('patient_id', $patientId);
    }

    /**
     * @param string $isLast
     */
    public function isLast(string $isLast)
    {
        $this->builder->where('is_last', $isLast);
    }

    /**
     * @param string $userId
     */
    public function userId(string $userId)
    {
        $this->builder->where('user_id', $userId);
    }

    /**
     * @param string $search
     */
    public function search(string $search)
    {
        $this->builder->whereHas(
            'patient', function (Builder $query) use ($search) {
                $query->where(
                    function ($query) use ($search) {
                        $query->where('phone_number', 'like', '%'.$search.'%')
                            ->orWhere('identification_number', 'like', '%'.$search.'%')
                            ->orWhereHas(
                                'patientTranslations', function (Builder $query) use ($search) {
                                    $query->where('name', 'like', '%'.$search.'%');
                                }
                            );
                    }
                );
            }
        );
    }

    /**
     * @param string $date
     */
    public function date(string $date)
    {
        $this->builder->whereDate('start_date', $date);
    }

    /**
     * @param string $startDate
     */
    public function startDate(string $startDate)
    {
        $this->builder->whereDate('start_date', '>=', $startDate);
    }

    /**
     * @param string $endDate
     */
    public function endDate(string $endDate)
    {
        $this->builder->whereDate('start_date', '<=', $endDate);
    }

    /**
     * @param string $appointmentReasonId
     */
    public function appointmentReasonId(string $appointmentReasonId)
    {
        $this->builder->whereHas(
            'appointmentHistories', function (Builder $query) use ($appointmentReasonId) {
                $query->where('appointment_reason_id', $appointmentReasonId);
            }
        );
    }

    /**
     * Sort the services by the given order and field.
     *
     * @param array $value
     */
    public function sort(string $value)
    {
        $sortData = explode(',', $value);
        $field = in_array($sortData[0], $this->fields)? $sortData[0] : 'created_at';
        $order = isset($sortData[1]) && in_array($sortData[1], $this->orders)? $sortData[1] : 'DESC';
        $this->builder->orderBy($field, $order);
    }
}