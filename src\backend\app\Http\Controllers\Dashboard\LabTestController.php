<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Resources\LabTests\ShowLabTestResource;
use App\Http\Requests\LabTests\StoreRequest;
use App\Http\Requests\LabTests\UpdateRequest;
use App\Services\LabTestService;
use App\Http\Resources\LabTests\LabTestResource;
use App\Http\Resources\LabTests\LabTestCollection;
use App\Http\Resources\LabTests\LabTestStatisticsResource;
use App\Http\Filters\LabTestFilter;
use App\Models\LabTest;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Lang;

class LabTestController extends Controller
{
    private $labTestService;

    public function __construct(LabTestService $labTestService)
    {
        $this->labTestService = $labTestService;
    }

    public function show(LabTest $labTest)
    {
        $this->authorize('view', LabTest::class);
        return new ShowLabTestResource($labTest);
    }

    public function index(LabTestFilter $filter)
    {
        $this->authorize('viewAny', LabTest::class);
        $result = $this->labTestService->filter($filter);
        return new LabTestCollection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->labTestService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.lab_tests.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, LabTest $labTest)
    {
        $this->labTestService->update($request->validated(), $labTest);
        return response()->json(['message' => Lang::get('messages.lab_tests.success.updated')], Response::HTTP_OK);
    }

    public function active(LabTest $labTest)
    {
        $this->authorize('active', LabTest::class);
        $this->labTestService->active($labTest);
        return response()->json(['message' => Lang::get('messages.lab_tests.success.actived')], Response::HTTP_OK);
    }

    public function deactive(LabTest $labTest)
    {
        $this->authorize('deactive', LabTest::class);
        $this->labTestService->deactive($labTest);
        return response()->json(['message' => Lang::get('messages.lab_tests.success.deactived')], Response::HTTP_OK);
    }

    public function statistics()
    {
        $this->authorize('statistics', LabTest::class);
        $statistics = $this->labTestService->statistics();
        return response()->json(['statistics' => new LabTestStatisticsResource($statistics)], Response::HTTP_OK);
    }

    public function list(Request $request)
    {
        $result = $this->labTestService->list($request->all());
        return LabTestResource::collection($result);
    }

    public function destroy(LabTest $labTest)
    {
        $this->authorize('delete', $labTest);
        $this->labTestService->delete($labTest);
        return response()->json(['message' => Lang::get('messages.lab_tests.success.deleted')], Response::HTTP_OK);
    }

}
