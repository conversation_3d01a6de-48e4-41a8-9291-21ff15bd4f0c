<?php

namespace App\Http\Controllers\Dashboard\Auth;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Http\Requests\ForgotPasswordUserRequest;
use App\Services\ForgotPasswordService;
use Lang;

class ForgotPasswordController extends Controller
{
    private $forgotPasswordService;

    public function __construct(ForgotPasswordService $forgotPasswordService)
    {
        $this->forgotPasswordService = $forgotPasswordService;
    }

    public function __invoke(ForgotPasswordUserRequest $request)
    {
        return $this->forgotPasswordService->getResetToken($request->validated());
    }
}
