<?php

namespace App\Jobs\SamplingReservations;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use App\Enums\LabTestStatus;
use Illuminate\Queue\SerializesModels;
use App\Traits\NotificationTrait;
use App\Enums\NotificationType;
use App\Enums\RequestStatus;
use App\Traits\MessageTrait;
use App\Enums\MessageType;
use Carbon\Carbon;
use Lang;

class NotifyPatientSamplingReservationIsChangedJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels,
        NotificationTrait, MessageTrait;

    protected $patientLabTest;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($patientLabTest)
    {
        $this->patientLabTest = $patientLabTest;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->patientLabTest->refresh();
        $patient = $this->patientLabTest->patient;
        $request = $this->patientLabTest->request;
        $patientLabTest = $this->patientLabTest;
        $key = LabTestStatus::getKey((int) $patientLabTest->status);
        $days = $patientLabTest->sample_date? $patientLabTest->receive_result_after : 0;
        $message = Lang::get('messages.request_statuses.'.$key, ['days' => $days], 'ar');
        $notification = $this->notificationObject(
            NotificationType::PATIENT_LAB_TEST, $patientLabTest->id, null, $request->request_status
        );
        $this->createMessage($patient, $message, MessageType::BOTH, $notification);     
    }
}
