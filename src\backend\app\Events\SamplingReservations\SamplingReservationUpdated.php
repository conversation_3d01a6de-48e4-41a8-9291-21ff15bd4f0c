<?php

namespace App\Events\SamplingReservations;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class SamplingReservationUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $samplingReservation, $isAgencyChanged, $byUser;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct($samplingReservation, $isAgencyChanged, $byUser)
    {
        $this->samplingReservation = $samplingReservation;
        $this->isAgencyChanged = $isAgencyChanged;
        $this->byUser = $byUser;
    }
}
