<?php

namespace App\Events\Appointments;

use Illuminate\Broadcasting\Channel;
use Illuminate\Broadcasting\InteractsWithSockets;
use Illuminate\Broadcasting\PresenceChannel;
use Illuminate\Broadcasting\PrivateChannel;
use Illuminate\Contracts\Broadcasting\ShouldBroadcast;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;
use App\Models\Appointment;

class AppointmentUpdated
{
    use Dispatchable, InteractsWithSockets, SerializesModels;

    public $oldAppointment, $updatedAppointment;

    /**
     * Create a new event instance.
     *
     * @return void
     */
    public function __construct(Appointment $oldAppointment, Appointment $updatedAppointment)
    {
        $this->oldAppointment = $oldAppointment;
        $this->updatedAppointment = $updatedAppointment;
    }
}
