<?php

namespace App\Http\Requests\Users;

use Illuminate\Foundation\Http\FormRequest;
use Lang;

class ValidateEmailRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'email' => 'required|email:rfc,dns|unique:users,email,NULL,id,deleted_at,NULL',
        ];
    }

    public function messages()
    {
        return [
            'email.required' => Lang::get('validation.custom.users.email.required'),
            'email.email' => Lang::get('validation.custom.patient-register.email.email'),
            'email.unique' => Lang::get('validation.custom.users.email.unique'),
        ];
    }
}