<?php

namespace App\Http\Requests\SamplingReservations;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\SamplingReservationStatus;
use Lang;
use Auth;

class ChangeSamplingReservationStatusRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('changeStatus', [SamplingReservation::class, $this->sampling_reservation]);  
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'status' => 'required|in:'
                .SamplingReservationStatus::DEPOSITED.','. SamplingReservationStatus::NOT_DEPOSITED,
            'notes' => 'nullable',
        ];
    }

    public function messages()
    {
        return [
            'status.required' => Lang::get('validation.custom.sampling-reservations.status.required'),
            'status.in' => Lang::get(
                'validation.custom.sampling-reservations.status.in',
                ['in' => SamplingReservationStatus::DEPOSITED.','. SamplingReservationStatus::NOT_DEPOSITED]
            ),
        ];
    }
}
