<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;

class PatientLabTestDashboardStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'total' => $this->total,
            'approved' => $this->approved,
            'not_approved' => $this->not_approved,
            'lated' => $this->lated,
            'refunded' => $this->refunded,
        );
    }
}
