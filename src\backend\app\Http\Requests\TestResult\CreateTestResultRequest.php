<?php

namespace App\Http\Requests\TestResult;

use Illuminate\Foundation\Http\FormRequest;
use App\Enums\Role;

class CreateTestResultRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return in_array($this->user()->role_id, [
            Role::ADMIN,
            Role::SPECIALIST
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'general_introduction' => 'nullable|string',
            'general_conclusion' => 'nullable|string',
            'categories' => 'required|array',
            'categories.*.introduction' => 'required|string',
            'categories.*.lab_test_result_category_id' => 'required|exists:lab_test_result_categories,id',
            'categories.*.genes' => 'required|array',
            'categories.*.genes.*.gene_id' => 'required|exists:genes,id',
            'categories.*.genes.*.mutation_site_id' => 'required|exists:mutation_sites,id',
            'categories.*.genes.*.gene_result_id' => 'required|exists:gene_results,id',
            'categories.*.genes.*.gene_transmission_method_id' => 'required|exists:gene_transmission_methods,id',
            'categories.*.genes.*.gene_category_id' => 'required|exists:gene_categories,id',
            'categories.*.genes.*.illness_id' => 'required|exists:illnesses,id',
            'categories.*.conclusion' => 'required|string',
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'categories.required' => 'At least one category is required',
            'categories.*.introduction.required' => 'Category introduction is required',
            'categories.*.genes.required' => 'At least one gene is required for each category',
            'categories.*.genes.*.gene_id.required' => 'Gene is required',
            'categories.*.genes.*.gene_id.exists' => 'Selected gene does not exist',
            'categories.*.genes.*.mutation_site_id.required' => 'Mutation site is required',
            'categories.*.genes.*.mutation_site_id.exists' => 'Selected mutation site does not exist',
            'categories.*.genes.*.gene_result_id.required' => 'Gene result is required',
            'categories.*.genes.*.gene_result_id.exists' => 'Selected gene result does not exist',
            'categories.*.genes.*.gene_transmission_method_id.required' => 'Gene transmission method is required',
            'categories.*.genes.*.gene_transmission_method_id.exists' => 'Selected gene transmission method does not exist',
            'categories.*.genes.*.gene_category_id.required' => 'Gene category is required',
            'categories.*.genes.*.gene_category_id.exists' => 'Selected gene category does not exist',
            'categories.*.genes.*.illness_id.required' => 'Illness is required',
            'categories.*.genes.*.illness_id.exists' => 'Selected illness does not exist',
            'categories.*.conclusion.required' => 'Category conclusion is required',
        ];
    }
}