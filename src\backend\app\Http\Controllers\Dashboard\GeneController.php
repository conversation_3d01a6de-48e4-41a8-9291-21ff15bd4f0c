<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\GeneFilter;
use App\Http\Resources\GeneResource;
use App\Models\Gene;
use App\Http\Resources\GeneSearchResource;
use App\Http\Resources\CustomGeneResource;
use App\Models\MutaionSite;
use App\Services\GeneService;
use App\Http\Requests\Genes\StoreRequest;
use App\Http\Requests\Genes\UpdateRequest;
use App\Http\Requests\Genes\MutaionSiteNameRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class GeneController extends Controller
{
    protected $geneService;

    public function __construct(GeneService $geneService)
    {
        $this->geneService = $geneService;
    }

    public function index(GeneFilter $filter)
    {
        $this->authorize('viewAny', Gene::class);
        $result = $this->geneService->filter($filter);
        return new GeneSearchResource($result);
    }

    public function validateGeneName(MutaionSiteNameRequest $request)
    {
        // MutaionSite::create([
        //     'name' => $request['name'],
        // ]);
        return response()->json(
            ['message' => Lang::get('messages.validate.success.gene-name', ['data' => $request['name']])],
            Response::HTTP_OK
        );
    }

    public function show(Gene $gene)
    {
        $this->authorize('view', Gene::class);
        return new CustomGeneResource($gene);
    }

    public function list()
    {
        $result = $this->geneService->list();
        return GeneResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->geneService->activeList();
        return GeneResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->geneService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.genes.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, Gene $gene)
    {
        $this->geneService->update($request->validated(), $gene);
        return response()->json(['message' => Lang::get('messages.genes.success.updated')], Response::HTTP_OK);
    }

    public function destroy(Gene $gene)
    {
        $this->authorize('delete', Gene::class);
        $this->geneService->delete($gene);
        return response()->json(['message' => Lang::get('messages.genes.success.deleted')], Response::HTTP_OK);
    }

    public function active(Gene $gene)
    {
        $this->authorize('active', Gene::class);
        $this->geneService->active($gene);
        return response()->json(['message' => Lang::get('messages.genes.success.actived')], Response::HTTP_OK);
    }

    public function deactive(Gene $gene)
    {
        $this->authorize('deactive', Gene::class);
        $this->geneService->deactive($gene);
        return response()->json(['message' => Lang::get('messages.genes.success.deactived')], Response::HTTP_OK);
    }

    // public function statistics()
    // {
    //     $this->authorize('statistics', Gene::class);
    //     $statistics = $this->geneService->statistics();
    //     return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    // }
}