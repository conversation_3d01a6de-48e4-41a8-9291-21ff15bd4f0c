<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use App\Http\Connectors\FirebaseDynamicLinkConnector;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\OnlineMeetingService;

class GenerateAppointmentShortLinkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $onlineMeeting;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($onlineMeeting)
    {
        $this->onlineMeeting = $onlineMeeting;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $meetingLink = $this->onlineMeeting->join_url;
        $shortenLink = FirebaseDynamicLinkConnector::createShortLink($meetingLink);

        app(OnlineMeetingService::class)->updateModel(['short_join_url' => $shortenLink], $this->onlineMeeting);
    }
}
