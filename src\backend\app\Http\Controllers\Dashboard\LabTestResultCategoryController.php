<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\LabTestResultCategoryFilter;
use App\Http\Resources\LabTestResultCategoryResource;
use App\Models\LabTestResultCategory;
use App\Http\Resources\CustomLabTestResultCategoryResource;
use App\Http\Resources\LabTestResultCategorySearchResource;
use App\Services\LabTestResultCategoryService;
use App\Http\Requests\Genes\StoreRequest;
use App\Http\Requests\Genes\UpdateRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class LabTestResultCategoryController extends Controller
{
    protected $labTestResultCategoryService;

    public function __construct(LabTestResultCategoryService $labTestResultCategoryService)
    {
        $this->labTestResultCategoryService = $labTestResultCategoryService;
    }

    public function index(LabTestResultCategoryFilter $filter)
    {
        $this->authorize('viewAny', LabTestResultCategory::class);
        $result = $this->labTestResultCategoryService->filter($filter);
        return new LabTestResultCategorySearchResource($result);
    }

    public function show(LabTestResultCategory $labTestResultCategory)
    {
        $this->authorize('view', LabTestResultCategory::class);
        return new CustomLabTestResultCategoryResource($labTestResultCategory);
    }

    public function list()
    {
        $result = $this->labTestResultCategoryService->list();
        return LabTestResultCategoryResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->labTestResultCategoryService->activeList();
        return LabTestResultCategoryResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->labTestResultCategoryService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.genes.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, LabTestResultCategory $labTestResultCategory)
    {
        $this->labTestResultCategoryService->update($request->validated(), $labTestResultCategory);
        return response()->json(['message' => Lang::get('messages.genes.success.updated')], Response::HTTP_OK);
    }

    public function destroy(LabTestResultCategory $labTestResultCategory)
    {
        $this->authorize('delete', LabTestResultCategory::class);
        $this->labTestResultCategoryService->delete($labTestResultCategory);
        return response()->json(['message' => Lang::get('messages.genes.success.deleted')], Response::HTTP_OK);
    }

    public function active(LabTestResultCategory $labTestResultCategory)
    {
        $this->authorize('active', LabTestResultCategory::class);
        $this->labTestResultCategoryService->active($labTestResultCategory);
        return response()->json(['message' => Lang::get('messages.genes.success.actived')], Response::HTTP_OK);
    }

    public function deactive(LabTestResultCategory $labTestResultCategory)
    {
        $this->authorize('deactive', LabTestResultCategory::class);
        $this->labTestResultCategoryService->deactive($labTestResultCategory);
        return response()->json(['message' => Lang::get('messages.genes.success.deactived')], Response::HTTP_OK);
    }

    // public function statistics()
    // {
    //     $this->authorize('statistics', Gene::class);
    //     $statistics = $this->geneService->statistics();
    //     return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    // }
}