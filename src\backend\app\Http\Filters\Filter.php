<?php

namespace App\Http\Filters;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

abstract class Filter
{
    public $orders = array('asc', 'desc');

    /**
     * @var Request
     */
    protected $request;

    /**
     * @var Builder
     */
    protected $builder;

    /**
     * @param Request $request
     * */
    public function __construct(Request $request)
    {
        $this->request = $request;
    }

    /**
     * @param Builder $builder
     */
    public function apply(Builder $builder)
    {
        $this->builder = $builder;

        foreach ($this->fields() as $field => $value) {
            $method = $field;
            if (method_exists($this, $method)) {
                call_user_func_array([$this, $method], (array) $value);
            }
        }
    }
    function recursive_trim($input)
    {
        if (is_string($input)) {
            return trim($input);
        } elseif (is_array($input)) {
            return array_map('recursive_trim', $input);
        } else {
            return $input; // Return unchanged for other types (int, bool, etc.)
        }
    }
    /**
     * @return array
     */
    protected function fields(): array
    {
        $data = $this->request->all();
        array_walk_recursive($data, function (&$value) {
            if (is_string($value)) {
                $value = trim($value);
            }
        });
        return array_filter($data);
    }


}
