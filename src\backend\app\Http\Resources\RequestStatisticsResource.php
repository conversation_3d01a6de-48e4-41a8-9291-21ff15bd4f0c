<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class RequestStatisticsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'new' => $this->new,
            'completed' => $this->completed,
            'rejected' => $this->rejected,
            'canceled' => $this->canceled,
            'inprogress' => $this->inprogress,
        );
    }
}
