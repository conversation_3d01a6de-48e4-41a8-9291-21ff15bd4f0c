<?php

namespace App\Http\Requests\SamplingReservations;

use Illuminate\Foundation\Http\FormRequest;
use Lang;
use Auth;

class UpdateSamplingReservationNotesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        $user = Auth::user();
        return $user && $user->can('updateNotes', [SamplingReservation::class, $this->sampling_reservation]);  
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'notes' => 'nullable',
        ];
    }

    public function messages()
    {
        return [];
    }
}
