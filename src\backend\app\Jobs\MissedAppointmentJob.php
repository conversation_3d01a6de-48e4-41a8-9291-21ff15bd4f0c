<?php

namespace App\Jobs;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use App\Services\UserService;
use App\Services\AppointmentService;
use App\Enums\AppointmentPurpose;
use App\Services\RequestService;
use App\Enums\AppointmentStatus;
use App\Enums\RequestStatus;
use App\Traits\MailTrait;
use App\Enums\Role;
use Carbon\Carbon;
use Lang;

class MissedAppointmentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, MailTrait;

    protected $appointment;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($appointment)
    {
        $this->appointment = $appointment;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $this->appointment->refresh();
        if (!$this->appointment->trashed() 
            && $this->appointment->isStatus(AppointmentStatus::NEW)
            && $this->hasTimePassed()
        ) {
            $this->updateAppointmentStatus();
            $this->updateRequestStatus();
            $this->notifyUser();
            $this->notifyAdmins();
        }
    }

    public function hasTimePassed()
    {
        return $this->appointment->end_date->lte(Carbon::now());
    }

    public function updateAppointmentStatus()
    {
        $data = array('status' => AppointmentStatus::MISSED);
        app(AppointmentService::class)
            ->updateAppointmentStatus($data, $this->appointment, null, null, $this->appointment->user);
    }

    public function updateRequestStatus()
    {
        $status = $this->appointment->isPurpose(AppointmentPurpose::RESULT_EXPLANATION)?
            RequestStatus::NOT_RESULT_EXPLANATION : RequestStatus::NOT_INTERVIEWED;
        app(RequestService::class)->updateRequestStatus($this->appointment->request, $status);
    }
    
    public function notifyUser()
    {
        $user = $this->appointment->user;
        $bodyAr = $this->notificationMessage('ar', 'user');
        $bodyEn =  $this->notificationMessage('en', 'user');
        $this->sendMissedAppointmentMail($user, $this->appointment->patient, null, false);
        app(AppointmentService::class)->notifyNotification($this->appointment->patient_id, $user, $bodyAr, $bodyEn);
    }

    public function notifyAdmins()
    {
        $admins = app(UserService::class)->users([Role::ADMIN, Role::FOLLOW_UP]);
        $bodyAr = $this->notificationMessage('ar', 'admin');
        $bodyEn =  $this->notificationMessage('en', 'admin');
        foreach ($admins as $admin) {
            $this->sendMissedAppointmentMail($admin, $this->appointment->patient, $this->appointment->user, true);
            app(AppointmentService::class)
                ->notifyNotification($this->appointment->patient_id, $admin, $bodyAr, $bodyEn);
        }
    }

    public function notificationMessage($lang, $type)
    {
        $user = $this->appointment->user;
        $patientName = $this->appointment->patient->translate($lang)->name;
        $userName = $user->translate($lang)->name?? Lang::get('translations.user');
        return Lang::get(
            'translations.mails.body.'.$type.'_missed_appointment',
            ['name' => $patientName, 'user_name' => $userName],
            $lang
        );
    }
}
