<?php

namespace App\Http\Resources\PatientLabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\LabTestStatus;
use App\Enums\RequestStatus;
use App\Enums\PaymentStatus;
use App\Enums\RequestStage;
use Lang;

class PatientLabTestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'lab_test_id' => $this->lab_test_id,
            'lab_test_name' => $this->name?? $this->labTest->name, 
            'lab_test_price' => $this->payment->price?? $this->labTest->price_after_discount,
            'notes' => $this->notes,
            'reference_lab' => array(
                'id' => $this->labTest->referenceLab->id,
                'ar' => $this->labTest->referenceLab->translate('ar')->name?? null,
                'en' => $this->labTest->referenceLab->translate('en')->name?? null,      
            ),
            'status' => $this->status,
            'status_name' => LabTestStatus::getDescription($this->status),
            'request_status' => $this->request->status(),
            'request_status_name' => RequestStatus::getDescription($this->request->status()),
            'request_stage' => $this->request->stage,
            'request_stage_name' => RequestStage::getDescription($this->request->stage),
            'is_approved' => $this->isApproved(),
            'payment_status' => $this->payment->status?? PaymentStatus::NOT_PAID,
            'payment_status_name' => Lang::get('translations.payment_statuses.'. PaymentStatus::getKey($this->payment->status?? PaymentStatus::NOT_PAID)),
        );
    }
}
