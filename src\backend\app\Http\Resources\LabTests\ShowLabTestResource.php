<?php

namespace App\Http\Resources\LabTests;

use Illuminate\Http\Resources\Json\JsonResource;
use App\Enums\TubeType;

class ShowLabTestResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        return array(
            'id' => $this->id,
            'code' => $this->code,
            'name' => array(
                'ar' => $this->translate('ar')->name,
                'en' => $this->translate('en')->name
            ),
            'sample_type' => array(
                'ar' => $this->translate('ar')->sample_type,
                'en' => $this->translate('en')->sample_type
            ),
            'sample_quantity' => $this->sample_quantity,
            'tube_type' => $this->tube_type,
            'tube_type_name' => TubeType::getDescription($this->tube_type),
            'tubes_count' => $this->tubes_count,
            'price' => $this->price,
            'price_after_discount' => $this->price_after_discount,
            'is_active' => $this->is_active,
            'reference_lab' => array(
                'id' => $this->referenceLab->id,
                'ar' => $this->referenceLab->translate('ar')->name?? null,
                'en' => $this->referenceLab->translate('en')->name?? null,      
            ),
            'obligation_type' => $this->obligation_type,
            'result_due_days' => $this->result_due_days,
            'lab_test_shipping_locations' => LabTestShippingLocationResource::collection($this->labTestShippingLocations),
            'lab_test_working_day' => LabTestWorkingDay::collection($this->labTestWorkingDays),
        );
    }
}
