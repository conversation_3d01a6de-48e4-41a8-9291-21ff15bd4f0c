<?php

namespace App\Http\Controllers\Patient;

use App\Http\Requests\SamplingReservations\UpdateSamplingReservationRequest;
use App\Http\Requests\SamplingReservations\StoreSamplingReservationRequest;
use App\Http\Requests\SamplingReservations\GetWorkingDaysRequest;
use App\Services\SamplingReservationService;
use App\Http\Controllers\Controller;
use App\Models\SamplingReservation;
use App\Models\PatientLabTest;
use Illuminate\Http\Response;
use Illuminate\Http\Request;
use Lang;

class SamplingReservationController extends Controller
{
 
    private $service;

    public function __construct(SamplingReservationService $service)
    {
        $this->service = $service;
    }

    public function store(StoreSamplingReservationRequest $request, PatientLabTest $patientLabTest)
    {
        $this->service->createByPatient($request->validated(), $patientLabTest);
        return response()->json(
            ['message' => Lang::get('messages.sampling-reservations.success.created')], Response::HTTP_OK
        );
    }

    public function update(
        UpdateSamplingReservationRequest $request,
        PatientLabTest $patientLabTest,
        SamplingReservation $samplingReservation
    ) {
        $this->service->updateByPatient($request->validated(), $patientLabTest, $samplingReservation);
        return response()->json(
            ['message' => Lang::get('messages.sampling-reservations.success.updated')], Response::HTTP_OK
        );
    }

    public function workingDays(GetWorkingDaysRequest $request, PatientLabTest $patientLabTest)
    {
        return $this->service->workingDays($request->validated(), $patientLabTest);
    }
}
