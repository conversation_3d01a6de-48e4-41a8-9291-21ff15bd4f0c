<?php

namespace App\Http\Controllers\Dashboard;

use App\Http\Controllers\Controller;
use App\Http\Filters\GeneTransmissionMethodFilter;
use App\Http\Resources\GeneTransmissionMethodResource;
use App\Models\GeneTransmissionMethod;
use App\Http\Resources\GeneTransmissionMethodSearchResource;
use App\Http\Resources\CustomGeneTransmissionMethodResource;
use App\Services\GeneTransmissionMethodService;
use App\Http\Requests\GeneTransmissionMethods\StoreRequest;
use App\Http\Requests\GeneTransmissionMethods\UpdateRequest;
use Illuminate\Http\Response;
use Cache;
use Lang;

class GeneTransmissionMethodController extends Controller
{
    protected $geneTransmissionMethodService;

    public function __construct(GeneTransmissionMethodService $geneTransmissionMethodService)
    {
        $this->geneTransmissionMethodService = $geneTransmissionMethodService;
    }

    public function index(GeneTransmissionMethodFilter $filter)
    {
        $this->authorize('viewAny', GeneTransmissionMethod::class);
        $result = $this->geneTransmissionMethodService->filter($filter);
        return new GeneTransmissionMethodSearchResource($result);
    }

    public function show(GeneTransmissionMethod $geneTransmissionMethod)
    {
        $this->authorize('view', GeneTransmissionMethod::class);
        return new CustomGeneTransmissionMethodResource($geneTransmissionMethod);
    }

    public function list()
    {
        $result = $this->geneTransmissionMethodService->list();
        return GeneTransmissionMethodResource::collection($result);
    }

    public function activeList()
    {
        $result = $this->geneTransmissionMethodService->activeList();
        return GeneTransmissionMethodResource::collection($result);
    }

    public function store(StoreRequest $request)
    {
        $this->geneTransmissionMethodService->create($request->validated());
        return response()->json(['message' => Lang::get('messages.genes.success.created')], Response::HTTP_OK);
    }

    public function update(UpdateRequest $request, GeneTransmissionMethod $geneTransmissionMethod)
    {
        $this->geneTransmissionMethodService->update($request->validated(), $geneTransmissionMethod);
        return response()->json(['message' => Lang::get('messages.genes.success.updated')], Response::HTTP_OK);
    }

    public function destroy(GeneTransmissionMethod $geneTransmissionMethod)
    {
        $this->authorize('delete', arguments: GeneTransmissionMethod::class);
        $this->geneTransmissionMethodService->delete($geneTransmissionMethod);
        return response()->json(['message' => Lang::get('messages.genes.success.deleted')], Response::HTTP_OK);
    }

    public function active(GeneTransmissionMethod $geneTransmissionMethod)
    {
        $this->authorize('active', GeneTransmissionMethod::class);
        $this->geneTransmissionMethodService->active($geneTransmissionMethod);
        return response()->json(['message' => Lang::get('messages.genes.success.actived')], Response::HTTP_OK);
    }

    public function deactive(GeneTransmissionMethod $geneTransmissionMethod)
    {
        $this->authorize('deactive', GeneTransmissionMethod::class);
        $this->geneTransmissionMethodService->deactive($geneTransmissionMethod);
        return response()->json(['message' => Lang::get('messages.genes.success.deactived')], Response::HTTP_OK);
    }

    // public function statistics()
    // {
    //     $this->authorize('statistics', Gene::class);
    //     $statistics = $this->geneService->statistics();
    //     return response()->json(['statistics' => new ProgramStatisticsResource($statistics)], Response::HTTP_OK);
    // }
}