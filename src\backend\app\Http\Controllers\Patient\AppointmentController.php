<?php

namespace App\Http\Controllers\Patient;

use App\Http\Resources\Appointments\AppointmentCollection;
use App\Http\Controllers\Controller;
use App\Http\Filters\AppointmentFilter;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use App\Services\AppointmentService;
use App\Models\Appointment;

class AppointmentController extends Controller
{
 
    private $appointmentService;

    public function __construct(AppointmentService $appointmentService)
    {
        $this->appointmentService = $appointmentService;
    }

    public function upcoming()
    {
        return $this->appointmentService->upcoming();
    }

    public function index(AppointmentFilter $filter) 
    {
        $result = $this->appointmentService->patientFilter($filter);
        return new AppointmentCollection($result);
    }
}
